<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>ErrorConst</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/constant/error.const.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CANNOT_UPDATE">CANNOT_UPDATE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CANT_APPROVE">CANT_APPROVE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CANT_MODIFY">CANT_MODIFY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CHANGED">CHANGED</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CONTRACT_NOT_FOUND">CONTRACT_NOT_FOUND</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DISCOUNT_POLICY_INVALID">DISCOUNT_POLICY_INVALID</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DISCOUNT_POLICY_TYPE_REAL_ESTATE">DISCOUNT_POLICY_TYPE_REAL_ESTATE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DISCOUNT_POLICY_VALUE_LAND_HOUSE">DISCOUNT_POLICY_VALUE_LAND_HOUSE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DUPLICATED">DUPLICATED</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMPLOYEE_BUSY">EMPLOYEE_BUSY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMPLOYEE_IS_EMPTY">EMPLOYEE_IS_EMPTY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMPLOYEE_NOT_FOUND">EMPLOYEE_NOT_FOUND</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMPLOYEE_NOT_IN_HANDOVER_CONFIG">EMPLOYEE_NOT_IN_HANDOVER_CONFIG</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#ERROR_CREATE_CUSTOMER">ERROR_CREATE_CUSTOMER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EXISTED">EXISTED</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_CONFIG_NOT_FOUND">HANDOVER_CONFIG_NOT_FOUND</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#INTEGRATE">INTEGRATE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#INTERNAL_DATE">INTERNAL_DATE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#INTERNAL_SERVER">INTERNAL_SERVER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#INVALID">INVALID</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#INVALID_INPUT">INVALID_INPUT</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#NOT_ENOUGH_CONDITION">NOT_ENOUGH_CONDITION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#NOT_FOUND">NOT_FOUND</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#ORGCHART_EMPTY">ORGCHART_EMPTY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PAYMENT_PERCENT_EMPTY">PAYMENT_PERCENT_EMPTY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PAYMENT_PERCENT_ERROR">PAYMENT_PERCENT_ERROR</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PAYMENT_PERCENT_NOT_ENOUGH">PAYMENT_PERCENT_NOT_ENOUGH</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PAYMENT_POLICY_INVALID">PAYMENT_POLICY_INVALID</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROERTY_ENOUGH_IN_HANDOVER_CONFIG">PROERTY_ENOUGH_IN_HANDOVER_CONFIG</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REQUIRED">REQUIRED</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REQUIRED_HANDOVER_END_TIME">REQUIRED_HANDOVER_END_TIME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REQUIRED_HANDOVER_START_TIME">REQUIRED_HANDOVER_START_TIME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#TIME_FRAME_EMPTY">TIME_FRAME_EMPTY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#TIME_FRAME_NOT_FOUND">TIME_FRAME_NOT_FOUND</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#TIME_FRAME_NOT_IN_HANDOVER_CONFIG">TIME_FRAME_NOT_IN_HANDOVER_CONFIG</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#UNAUTHORIZED">UNAUTHORIZED</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#Error">Error</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CANNOT_UPDATE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CANNOT_UPDATE</b>
                            <a href="#CANNOT_UPDATE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.cannot.update&#x60;,
    text: &#x60;{object} không thể cập nhật&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="114" class="link-to-prism">src/modules/shared/constant/error.const.ts:114</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CANT_APPROVE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CANT_APPROVE</b>
                            <a href="#CANT_APPROVE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.field&#x60;,
    text: &#x60;{object} không thể duyệt&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="47" class="link-to-prism">src/modules/shared/constant/error.const.ts:47</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CANT_MODIFY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CANT_MODIFY</b>
                            <a href="#CANT_MODIFY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.field.{property}&#x60;,
    text: &#x60;{object} {property} không thể sửa. {value}&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="43" class="link-to-prism">src/modules/shared/constant/error.const.ts:43</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CHANGED"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CHANGED</b>
                            <a href="#CHANGED"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.{property}.changed&#x60;,
    text: &#x60;{object} {property} đã bị thay đổi. Vui lòng kiểm tra lại.&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="63" class="link-to-prism">src/modules/shared/constant/error.const.ts:63</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CONTRACT_NOT_FOUND"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CONTRACT_NOT_FOUND</b>
                            <a href="#CONTRACT_NOT_FOUND"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;contract.not.found&#x60;,
    text: &#x60;Không tìm thấy hợp đồng.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="126" class="link-to-prism">src/modules/shared/constant/error.const.ts:126</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DISCOUNT_POLICY_INVALID"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DISCOUNT_POLICY_INVALID</b>
                            <a href="#DISCOUNT_POLICY_INVALID"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.discount.error&#x60;,
    text: &#x60;Chính sách chiết khẩu không phù hợp. Vui lòng kiểm tra lại.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="96" class="link-to-prism">src/modules/shared/constant/error.const.ts:96</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DISCOUNT_POLICY_TYPE_REAL_ESTATE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DISCOUNT_POLICY_TYPE_REAL_ESTATE</b>
                            <a href="#DISCOUNT_POLICY_TYPE_REAL_ESTATE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.type.real.estate.undefine&#x60;,
    text: &#x60;Giá trị trường Loại của CSCK không có giá trị.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="106" class="link-to-prism">src/modules/shared/constant/error.const.ts:106</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DISCOUNT_POLICY_VALUE_LAND_HOUSE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DISCOUNT_POLICY_VALUE_LAND_HOUSE</b>
                            <a href="#DISCOUNT_POLICY_VALUE_LAND_HOUSE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.discount.error&#x60;,
    text: &#x27;Sản phẩm chưa có giá trị nhà hoặc giá trị đất, vui lòng cập nhật.&#x27;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="101" class="link-to-prism">src/modules/shared/constant/error.const.ts:101</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DUPLICATED"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DUPLICATED</b>
                            <a href="#DUPLICATED"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.duplicated.{property}&#x60;,
    text: &#x60;{object} bị trùng lặp {property} : {value}. Vui lòng kiểm tra lại.&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="59" class="link-to-prism">src/modules/shared/constant/error.const.ts:59</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMPLOYEE_BUSY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMPLOYEE_BUSY</b>
                            <a href="#EMPLOYEE_BUSY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;employee.busy&#x60;,
    text: &#x60;Nhân viên đã bận trong khung giờ này.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="154" class="link-to-prism">src/modules/shared/constant/error.const.ts:154</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMPLOYEE_IS_EMPTY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMPLOYEE_IS_EMPTY</b>
                            <a href="#EMPLOYEE_IS_EMPTY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;employee.is.empty&#x60;,
    text: &#x60;Thiếu thông tin nhân viên hỗ trợ.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="138" class="link-to-prism">src/modules/shared/constant/error.const.ts:138</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMPLOYEE_NOT_FOUND"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMPLOYEE_NOT_FOUND</b>
                            <a href="#EMPLOYEE_NOT_FOUND"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;employee.not.found&#x60;,
    text: &#x60;Không tìm thấy thông tin nhân viên hỗ trợ.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="134" class="link-to-prism">src/modules/shared/constant/error.const.ts:134</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMPLOYEE_NOT_IN_HANDOVER_CONFIG"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMPLOYEE_NOT_IN_HANDOVER_CONFIG</b>
                            <a href="#EMPLOYEE_NOT_IN_HANDOVER_CONFIG"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;employee.not.in.handover.config&#x60;,
    text: &#x60;Nhân viên không nằm trong cấu hình bàn giao.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="142" class="link-to-prism">src/modules/shared/constant/error.const.ts:142</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ERROR_CREATE_CUSTOMER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            ERROR_CREATE_CUSTOMER</b>
                            <a href="#ERROR_CREATE_CUSTOMER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.error.create.customer&#x60;,
    text: &#x60;{object} có lỗi khi tạo khách hàng giao dịch. Vui lòng kiểm tra lại.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="92" class="link-to-prism">src/modules/shared/constant/error.const.ts:92</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EXISTED"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EXISTED</b>
                            <a href="#EXISTED"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.existed&#x60;,
    text: &#x60;{object} đã tồn tại. Vui lòng kiểm tra lại.&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="55" class="link-to-prism">src/modules/shared/constant/error.const.ts:55</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_CONFIG_NOT_FOUND"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_CONFIG_NOT_FOUND</b>
                            <a href="#HANDOVER_CONFIG_NOT_FOUND"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;handover.config.not.found&#x60;,
    text: &#x60;Không tìm thấy cấu hình bàn giao.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="122" class="link-to-prism">src/modules/shared/constant/error.const.ts:122</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="INTEGRATE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            INTEGRATE</b>
                            <a href="#INTEGRATE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.integrate.{property}.error&#x60;,
    text: &#x60;Tích hợp {object} bị lỗi tại {property}: {value}. Vui lòng kiểm tra lại.&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="71" class="link-to-prism">src/modules/shared/constant/error.const.ts:71</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="INTERNAL_DATE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            INTERNAL_DATE</b>
                            <a href="#INTERNAL_DATE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.input.error&#x60;,
    text: &#x60;{object} nhập sai định dạng. Xin vui lòng thử lại&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="84" class="link-to-prism">src/modules/shared/constant/error.const.ts:84</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="INTERNAL_SERVER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            INTERNAL_SERVER</b>
                            <a href="#INTERNAL_SERVER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;internal.server.error&#x60;,
    text: &#x60;Lỗi hệ thống&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="80" class="link-to-prism">src/modules/shared/constant/error.const.ts:80</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="INVALID"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            INVALID</b>
                            <a href="#INVALID"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.field.{property}&#x60;,
    text: &#x60;{object} {property} nhập thông tin bị sai. {value}&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/modules/shared/constant/error.const.ts:35</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="INVALID_INPUT"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            INVALID_INPUT</b>
                            <a href="#INVALID_INPUT"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.input.error&#x60;,
    text: &#x60;{object} nhập sai kiểu dữ liệu. Xin vui lòng thử lại&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="75" class="link-to-prism">src/modules/shared/constant/error.const.ts:75</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NOT_ENOUGH_CONDITION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            NOT_ENOUGH_CONDITION</b>
                            <a href="#NOT_ENOUGH_CONDITION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.not.enough.condition&#x60;,
    text: &#x60;{object} không đủ điều kiện duyệt hợp đồng. Vui lòng kiểm tra lại.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="88" class="link-to-prism">src/modules/shared/constant/error.const.ts:88</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NOT_FOUND"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            NOT_FOUND</b>
                            <a href="#NOT_FOUND"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.not.found&#x60;,
    text: &#x60;{object} không tìm thấy bởi {property}: {value}&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="51" class="link-to-prism">src/modules/shared/constant/error.const.ts:51</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ORGCHART_EMPTY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            ORGCHART_EMPTY</b>
                            <a href="#ORGCHART_EMPTY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;orgchart.empty&#x60;,
    text: &#x60;Thiếu đơn vị thực hiện&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="166" class="link-to-prism">src/modules/shared/constant/error.const.ts:166</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PAYMENT_PERCENT_EMPTY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PAYMENT_PERCENT_EMPTY</b>
                            <a href="#PAYMENT_PERCENT_EMPTY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;payment.percent.empty&#x60;,
    text: &#x60;Thiếu % thanh toán yêu cầu&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="170" class="link-to-prism">src/modules/shared/constant/error.const.ts:170</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PAYMENT_PERCENT_ERROR"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PAYMENT_PERCENT_ERROR</b>
                            <a href="#PAYMENT_PERCENT_ERROR"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;payment.percent.error&#x60;,
    text: &#x60;% thanh toán yêu cầu từ 0% đến 100%&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="174" class="link-to-prism">src/modules/shared/constant/error.const.ts:174</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PAYMENT_PERCENT_NOT_ENOUGH"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PAYMENT_PERCENT_NOT_ENOUGH</b>
                            <a href="#PAYMENT_PERCENT_NOT_ENOUGH"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;payment.percent.not.enough&#x60;,
    text: &#x60;Hợp đồng không đủ % thanh toán.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="118" class="link-to-prism">src/modules/shared/constant/error.const.ts:118</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PAYMENT_POLICY_INVALID"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PAYMENT_POLICY_INVALID</b>
                            <a href="#PAYMENT_POLICY_INVALID"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;{object}.invalid.discount.error&#x60;,
    text: &#x60;Chính sách thanh toán không phù hợp. Vui lòng kiểm tra lại.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="110" class="link-to-prism">src/modules/shared/constant/error.const.ts:110</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROERTY_ENOUGH_IN_HANDOVER_CONFIG"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROERTY_ENOUGH_IN_HANDOVER_CONFIG</b>
                            <a href="#PROERTY_ENOUGH_IN_HANDOVER_CONFIG"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;property.enough.in.handover.config&#x60;,
    text: &#x60;Số lượng sản phẩm bàn giao trong khung giờ đã đủ.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="150" class="link-to-prism">src/modules/shared/constant/error.const.ts:150</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REQUIRED"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REQUIRED</b>
                            <a href="#REQUIRED"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;required.field.{property}&#x60;,
    text: &#x60;{property} là bắt buộc&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/modules/shared/constant/error.const.ts:39</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REQUIRED_HANDOVER_END_TIME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REQUIRED_HANDOVER_END_TIME</b>
                            <a href="#REQUIRED_HANDOVER_END_TIME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;required.handover.end.time&#x60;,
    text: &#x60;Đến giờ là bắt buộc&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="162" class="link-to-prism">src/modules/shared/constant/error.const.ts:162</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REQUIRED_HANDOVER_START_TIME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REQUIRED_HANDOVER_START_TIME</b>
                            <a href="#REQUIRED_HANDOVER_START_TIME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;required.handover.start.time&#x60;,
    text: &#x60;Từ giờ là bắt buộc&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="158" class="link-to-prism">src/modules/shared/constant/error.const.ts:158</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TIME_FRAME_EMPTY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            TIME_FRAME_EMPTY</b>
                            <a href="#TIME_FRAME_EMPTY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;time.frame.empty&#x60;,
    text: &#x60;Vui lòng cấu hình khung giờ bàn giao&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="178" class="link-to-prism">src/modules/shared/constant/error.const.ts:178</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TIME_FRAME_NOT_FOUND"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            TIME_FRAME_NOT_FOUND</b>
                            <a href="#TIME_FRAME_NOT_FOUND"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;time.frame.not.found&#x60;,
    text: &#x60;Không tìm thấy khung giờ bàn giao.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="130" class="link-to-prism">src/modules/shared/constant/error.const.ts:130</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TIME_FRAME_NOT_IN_HANDOVER_CONFIG"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            TIME_FRAME_NOT_IN_HANDOVER_CONFIG</b>
                            <a href="#TIME_FRAME_NOT_IN_HANDOVER_CONFIG"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;time.frame.not.in.handover.config&#x60;,
    text: &#x60;Khung giờ không nằm trong cấu hình bàn giao.&#x60;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="146" class="link-to-prism">src/modules/shared/constant/error.const.ts:146</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="UNAUTHORIZED"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            UNAUTHORIZED</b>
                            <a href="#UNAUTHORIZED"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    key: &#x60;unauthorized.error&#x60;,
    text: &#x60;Bạn không có quyền truy cập chức năng này.&#x60;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/modules/shared/constant/error.const.ts:67</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="Error"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            Error
                        </b>
                        <a href="#Error"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>Error(type, object?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, property?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, value?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, pushError?)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="4"
                            class="link-to-prism">src/modules/shared/constant/error.const.ts:4</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>type</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>object</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>property</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>value</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>pushError</td>
                                    <td>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { isNullOrUndefined } from &quot;util&quot;;

export class ErrorConst {
  static Error(
    type,
    object?: string,
    property?: string,
    value?: string,
    pushError?
  ) {
    let error &#x3D; {};
    let key &#x3D; type.key;
    let text &#x3D; type.text;
    if (!object) object &#x3D; &quot;&quot;;

    key &#x3D; key.replace(&quot;{object}&quot;, object);
    text &#x3D; text.replace(&quot;{object}&quot;, object);

    if (!property) property &#x3D; &quot;&quot;;
    // if (property &amp;&amp; property.length &gt; 0) {
    key &#x3D; key.replace(&quot;{property}&quot;, property);
    text &#x3D; text.replace(&quot;{property}&quot;, property);
    // }
    if (!value) value &#x3D; &quot;&quot;;
    // if (value &amp;&amp; value.length &gt; 0) {
    key &#x3D; key.replace(&quot;{value}&quot;, value);
    text &#x3D; text.replace(&quot;{value}&quot;, value);
    // }
    error[key] &#x3D; text;
    if (!isNullOrUndefined(pushError)) error &#x3D; Object.assign(pushError, error);
    return error;
  }

  // common error
  static INVALID &#x3D; {
    key: &#x60;{object}.invalid.field.{property}&#x60;,
    text: &#x60;{object} {property} nhập thông tin bị sai. {value}&#x60;,
  };
  static REQUIRED &#x3D; {
    key: &#x60;required.field.{property}&#x60;,
    text: &#x60;{property} là bắt buộc&#x60;,
  };
  static CANT_MODIFY &#x3D; {
    key: &#x60;{object}.invalid.field.{property}&#x60;,
    text: &#x60;{object} {property} không thể sửa. {value}&#x60;,
  };
  static CANT_APPROVE &#x3D; {
    key: &#x60;{object}.invalid.field&#x60;,
    text: &#x60;{object} không thể duyệt&#x60;,
  };
  static NOT_FOUND &#x3D; {
    key: &#x60;{object}.not.found&#x60;,
    text: &#x60;{object} không tìm thấy bởi {property}: {value}&#x60;,
  };
  static EXISTED &#x3D; {
    key: &#x60;{object}.existed&#x60;,
    text: &#x60;{object} đã tồn tại. Vui lòng kiểm tra lại.&#x60;,
  };
  static DUPLICATED &#x3D; {
    key: &#x60;{object}.duplicated.{property}&#x60;,
    text: &#x60;{object} bị trùng lặp {property} : {value}. Vui lòng kiểm tra lại.&#x60;,
  };
  static CHANGED &#x3D; {
    key: &#x60;{object}.{property}.changed&#x60;,
    text: &#x60;{object} {property} đã bị thay đổi. Vui lòng kiểm tra lại.&#x60;,
  };
  static UNAUTHORIZED &#x3D; {
    key: &#x60;unauthorized.error&#x60;,
    text: &#x60;Bạn không có quyền truy cập chức năng này.&#x60;,
  };
  static INTEGRATE &#x3D; {
    key: &#x60;{object}.integrate.{property}.error&#x60;,
    text: &#x60;Tích hợp {object} bị lỗi tại {property}: {value}. Vui lòng kiểm tra lại.&#x60;,
  };
  static INVALID_INPUT &#x3D; {
    key: &#x60;{object}.invalid.input.error&#x60;,
    text: &#x60;{object} nhập sai kiểu dữ liệu. Xin vui lòng thử lại&#x60;
  };
  
  static INTERNAL_SERVER &#x3D; {
    key: &#x60;internal.server.error&#x60;,
    text: &#x60;Lỗi hệ thống&#x60;
  }
  static INTERNAL_DATE &#x3D; {
    key: &#x60;{object}.invalid.input.error&#x60;,
    text: &#x60;{object} nhập sai định dạng. Xin vui lòng thử lại&#x60;
  };
  static NOT_ENOUGH_CONDITION &#x3D; {
    key: &#x60;{object}.not.enough.condition&#x60;,
    text: &#x60;{object} không đủ điều kiện duyệt hợp đồng. Vui lòng kiểm tra lại.&#x60;
  };
  static ERROR_CREATE_CUSTOMER &#x3D; {
    key: &#x60;{object}.error.create.customer&#x60;,
    text: &#x60;{object} có lỗi khi tạo khách hàng giao dịch. Vui lòng kiểm tra lại.&#x60;
  };
  static DISCOUNT_POLICY_INVALID &#x3D; {
    key: &#x60;{object}.invalid.discount.error&#x60;,
    text: &#x60;Chính sách chiết khẩu không phù hợp. Vui lòng kiểm tra lại.&#x60;
  };

  static DISCOUNT_POLICY_VALUE_LAND_HOUSE &#x3D; {
    key: &#x60;{object}.invalid.discount.error&#x60;,
    text: &#x27;Sản phẩm chưa có giá trị nhà hoặc giá trị đất, vui lòng cập nhật.&#x27;
  }

  static DISCOUNT_POLICY_TYPE_REAL_ESTATE &#x3D; {
    key: &#x60;{object}.type.real.estate.undefine&#x60;,
    text: &#x60;Giá trị trường Loại của CSCK không có giá trị.&#x60;
  }
  static PAYMENT_POLICY_INVALID &#x3D; {
    key: &#x60;{object}.invalid.discount.error&#x60;,
    text: &#x60;Chính sách thanh toán không phù hợp. Vui lòng kiểm tra lại.&#x60;
  };
  static CANNOT_UPDATE &#x3D; {
    key: &#x60;{object}.cannot.update&#x60;,
    text: &#x60;{object} không thể cập nhật&#x60;
  }
  static PAYMENT_PERCENT_NOT_ENOUGH &#x3D; {
    key: &#x60;payment.percent.not.enough&#x60;,
    text: &#x60;Hợp đồng không đủ % thanh toán.&#x60;
  };
  static HANDOVER_CONFIG_NOT_FOUND &#x3D; {
    key: &#x60;handover.config.not.found&#x60;,
    text: &#x60;Không tìm thấy cấu hình bàn giao.&#x60;
  };
  static CONTRACT_NOT_FOUND &#x3D; {
    key: &#x60;contract.not.found&#x60;,
    text: &#x60;Không tìm thấy hợp đồng.&#x60;
  };
  static TIME_FRAME_NOT_FOUND &#x3D; {
    key: &#x60;time.frame.not.found&#x60;,
    text: &#x60;Không tìm thấy khung giờ bàn giao.&#x60;
  };
  static EMPLOYEE_NOT_FOUND &#x3D; {
    key: &#x60;employee.not.found&#x60;,
    text: &#x60;Không tìm thấy thông tin nhân viên hỗ trợ.&#x60;
  };
  static EMPLOYEE_IS_EMPTY &#x3D; {
    key: &#x60;employee.is.empty&#x60;,
    text: &#x60;Thiếu thông tin nhân viên hỗ trợ.&#x60;
  };
  static EMPLOYEE_NOT_IN_HANDOVER_CONFIG &#x3D; {
    key: &#x60;employee.not.in.handover.config&#x60;,
    text: &#x60;Nhân viên không nằm trong cấu hình bàn giao.&#x60;
  };
  static TIME_FRAME_NOT_IN_HANDOVER_CONFIG &#x3D; {
    key: &#x60;time.frame.not.in.handover.config&#x60;,
    text: &#x60;Khung giờ không nằm trong cấu hình bàn giao.&#x60;
  };
  static PROERTY_ENOUGH_IN_HANDOVER_CONFIG &#x3D; {
    key: &#x60;property.enough.in.handover.config&#x60;,
    text: &#x60;Số lượng sản phẩm bàn giao trong khung giờ đã đủ.&#x60;
  };
  static EMPLOYEE_BUSY &#x3D; {
    key: &#x60;employee.busy&#x60;,
    text: &#x60;Nhân viên đã bận trong khung giờ này.&#x60;
  };
  static REQUIRED_HANDOVER_START_TIME &#x3D; {
    key: &#x60;required.handover.start.time&#x60;,
    text: &#x60;Từ giờ là bắt buộc&#x60;,
  };
  static REQUIRED_HANDOVER_END_TIME &#x3D; {
    key: &#x60;required.handover.end.time&#x60;,
    text: &#x60;Đến giờ là bắt buộc&#x60;,
  };
  static ORGCHART_EMPTY &#x3D; {
    key: &#x60;orgchart.empty&#x60;,
    text: &#x60;Thiếu đơn vị thực hiện&#x60;,
  };
  static PAYMENT_PERCENT_EMPTY &#x3D; {
    key: &#x60;payment.percent.empty&#x60;,
    text: &#x60;Thiếu % thanh toán yêu cầu&#x60;,
  };
  static PAYMENT_PERCENT_ERROR &#x3D; {
    key: &#x60;payment.percent.error&#x60;,
    text: &#x60;% thanh toán yêu cầu từ 0% đến 100%&#x60;,
  };
  static TIME_FRAME_EMPTY &#x3D; {
    key: &#x60;time.frame.empty&#x60;,
    text: &#x60;Vui lòng cấu hình khung giờ bàn giao&#x60;,
  };
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'ErrorConst.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
