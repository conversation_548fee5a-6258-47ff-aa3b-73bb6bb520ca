<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>StatushistoryAdminController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/statusHistory/web/admin-controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/admin/statusHistory</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create">create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete">delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAll">findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findById">findById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update">update</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            create
                        </b>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(user, dto: <a href="../classes/CreateStatushistoryRequestDto.html">CreateStatushistoryRequestDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@ApiOperation({title: &#x27;Create&#x27;})<br />@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="95"
                            class="link-to-prism">src/modules/statusHistory/web/admin-controller.ts:95</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/CreateStatushistoryRequestDto.html" target="_self" >CreateStatushistoryRequestDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            delete
                        </b>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(user, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@ApiOperation({title: &#x27;Delete&#x27;})<br />@ApiImplicitParam({name: &#x27;id&#x27;, required: true, description: &#x27;Id&#x27;})<br />@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Delete(&#x27;:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="126"
                            class="link-to-prism">src/modules/statusHistory/web/admin-controller.ts:126</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAll
                        </b>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll(user, query?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@ApiOperation({title: &#x27;Find all&#x27;})<br />@ApiImplicitQuery({name: &#x27;page&#x27;, required: true, description: &#x27;[Paging] page&#x27;})<br />@ApiImplicitQuery({name: &#x27;pageSize&#x27;, required: true, description: &#x27;[Paging] pageSize&#x27;})<br />@ApiImplicitQuery({name: &#x27;sort&#x27;, required: false, isArray: true, description: &#x27;[Sort] sort&#x27;})<br />@ApiImplicitQuery({name: &#x27;q&#x27;, required: false, description: &#x27;[Filter] Search text&#x27;})<br />@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="70"
                            class="link-to-prism">src/modules/statusHistory/web/admin-controller.ts:70</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findById
                        </b>
                        <a href="#findById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@ApiOperation({title: &#x27;Find one&#x27;})<br />@ApiImplicitParam({name: &#x27;id&#x27;, required: true, description: &#x27;Id&#x27;})<br />@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="83"
                            class="link-to-prism">src/modules/statusHistory/web/admin-controller.ts:83</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            update
                        </b>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(user, dto: <a href="../classes/UpdateStatushistoryRequestDto.html">UpdateStatushistoryRequestDto</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@ApiOperation({title: &#x27;Update&#x27;})<br />@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="110"
                            class="link-to-prism">src/modules/statusHistory/web/admin-controller.ts:110</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateStatushistoryRequestDto.html" target="_self" >UpdateStatushistoryRequestDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Query,
  Body,
  UseGuards,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import {
  ApiUseTags,
  ApiBearerAuth,
  ApiImplicitQuery,
  ApiOperation,
  ApiImplicitParam,
} from &quot;@nestjs/swagger&quot;;
import { AuthGuard } from &quot;@nestjs/passport&quot;;
import { StatushistoryService } from &quot;../application/service&quot;;
import { LoggingInterceptor } from &quot;../../../common/interceptors/logging.interceptor&quot;;
import { RolesGuard } from &quot;../../../common/guards/roles.guard&quot;;
import { ACGuard, UseRoles } from &quot;nest-access-control&quot;;
import { Usr } from &quot;../../shared/services/user/decorator/user.decorator&quot;;
import { PermissionEnum } from &quot;../../shared/enum/permission.enum&quot;;
import { ValidationPipe } from &quot;../../../common/pipes/validation.pipe&quot;;
import {
  CreateStatushistoryRequestDto,
  UpdateStatushistoryRequestDto,
} from &quot;./dto/request.dto&quot;;

@ApiBearerAuth()
@ApiUseTags(&quot;[Admin] Statushistory - API của Admin&quot;)
@Controller(&quot;v1/admin/statusHistory&quot;)
@UseGuards(AuthGuard(&quot;jwt&quot;))
@UseInterceptors(LoggingInterceptor)
export class StatushistoryAdminController {
  constructor(private readonly service: StatushistoryService) {}

  @ApiOperation({ title: &quot;Find all&quot; })
  @ApiImplicitQuery({
    name: &quot;page&quot;,
    required: true,
    description: &quot;[Paging] page&quot;,
  })
  @ApiImplicitQuery({
    name: &quot;pageSize&quot;,
    required: true,
    description: &quot;[Paging] pageSize&quot;,
  })
  @ApiImplicitQuery({
    name: &quot;sort&quot;,
    required: false,
    isArray: true,
    description: &quot;[Sort] sort&quot;,
  })
  @ApiImplicitQuery({
    name: &quot;q&quot;,
    required: false,
    description: &quot;[Filter] Search text&quot;,
  })
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.STATUSHISTORY_GET_ALL,
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Get()
  async findAll(@Usr() user, @Query() query?: any) {
    return await this.service.findAll(query);
  }

  @ApiOperation({ title: &quot;Find one&quot; })
  @ApiImplicitParam({ name: &quot;id&quot;, required: true, description: &quot;Id&quot; })
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.STATUSHISTORY_GET_ID,
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Get(&quot;:id&quot;)
  async findById(@Param(&quot;id&quot;) id: string) {
    return await this.service.findById(id);
  }

  @ApiOperation({ title: &quot;Create&quot; })
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.STATUSHISTORY_CREATE,
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Post()
  async create(
    @Usr() user,
    @Body(new ValidationPipe()) dto: CreateStatushistoryRequestDto
  ) {
    return await this.service.create(user, dto);
  }

  @ApiOperation({ title: &quot;Update&quot; })
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.STATUSHISTORY_UPDATE,
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Put()
  async update(
    @Usr() user,
    @Body(new ValidationPipe()) dto: UpdateStatushistoryRequestDto
  ) {
    return await this.service.update(user, dto);
  }

  @ApiOperation({ title: &quot;Delete&quot; })
  @ApiImplicitParam({ name: &quot;id&quot;, required: true, description: &quot;Id&quot; })
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.STATUSHISTORY_DELETE,
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Delete(&quot;:id&quot;)
  async delete(@Usr() user, @Param(&quot;id&quot;) id: string) {
    return await this.service.delete(user, id);
  }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'StatushistoryAdminController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
