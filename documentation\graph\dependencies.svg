<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="13252pt" height="805pt"
 viewBox="0.00 0.00 13252.00 805.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 801)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-801 13248,-801 13248,4 -4,4"/>
<text text-anchor="start" x="6601.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="6388,-10 6388,-30 6408,-30 6408,-10 6388,-10"/>
<text text-anchor="start" x="6411.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="6501,-10 6501,-30 6521,-30 6521,-10 6501,-10"/>
<text text-anchor="start" x="6524.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="6587,-10 6587,-30 6607,-30 6607,-10 6587,-10"/>
<text text-anchor="start" x="6610.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="6684,-10 6684,-30 6704,-30 6704,-10 6684,-10"/>
<text text-anchor="start" x="6707.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="6780,-10 6780,-30 6800,-30 6800,-10 6780,-10"/>
<text text-anchor="start" x="6803.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_ApplicationModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="4622,-70 4622,-789 6265,-789 6265,-70 4622,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_ApplicationModule_imports</title>
<polygon fill="none" stroke="black" points="4630,-78 4630,-781 6123,-781 6123,-78 4630,-78"/>
</g>
<g id="clust7" class="cluster">
<title>cluster_AuthModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-135 8,-203 526,-203 526,-135 8,-135"/>
</g>
<g id="clust9" class="cluster">
<title>cluster_AuthModule_imports</title>
<polygon fill="none" stroke="black" points="16,-143 16,-195 130,-195 130,-143 16,-143"/>
</g>
<g id="clust10" class="cluster">
<title>cluster_AuthModule_exports</title>
<polygon fill="none" stroke="black" points="412,-143 412,-195 518,-195 518,-143 412,-143"/>
</g>
<g id="clust12" class="cluster">
<title>cluster_AuthModule_providers</title>
<polygon fill="none" stroke="black" points="138,-143 138,-195 404,-195 404,-143 138,-143"/>
</g>
<g id="clust13" class="cluster">
<title>cluster_CodeGenerateModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="1942,-211 1942,-431 2570,-431 2570,-211 1942,-211"/>
</g>
<g id="clust15" class="cluster">
<title>cluster_CodeGenerateModule_imports</title>
<polygon fill="none" stroke="black" points="2400,-219 2400,-271 2562,-271 2562,-219 2400,-219"/>
</g>
<g id="clust16" class="cluster">
<title>cluster_CodeGenerateModule_exports</title>
<polygon fill="none" stroke="black" points="1950,-371 1950,-423 2448,-423 2448,-371 1950,-371"/>
</g>
<g id="clust18" class="cluster">
<title>cluster_CodeGenerateModule_providers</title>
<polygon fill="none" stroke="black" points="1950,-219 1950,-271 2392,-271 2392,-219 1950,-219"/>
</g>
<g id="clust19" class="cluster">
<title>cluster_ConfigModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="4362,-211 4362,-279 4614,-279 4614,-211 4362,-211"/>
</g>
<g id="clust22" class="cluster">
<title>cluster_ConfigModule_exports</title>
<polygon fill="none" stroke="black" points="4370,-219 4370,-271 4606,-271 4606,-219 4370,-219"/>
</g>
<g id="clust25" class="cluster">
<title>cluster_DiscountDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="6273,-363 6273,-431 7154,-431 7154,-363 6273,-363"/>
</g>
<g id="clust27" class="cluster">
<title>cluster_DiscountDomainModule_imports</title>
<polygon fill="none" stroke="black" points="6974,-371 6974,-423 7146,-423 7146,-371 6974,-371"/>
</g>
<g id="clust30" class="cluster">
<title>cluster_DiscountDomainModule_providers</title>
<polygon fill="none" stroke="black" points="6281,-371 6281,-423 6966,-423 6966,-371 6281,-371"/>
</g>
<g id="clust31" class="cluster">
<title>cluster_DiscountQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="3328,-287 3328,-355 4548,-355 4548,-287 3328,-287"/>
</g>
<g id="clust33" class="cluster">
<title>cluster_DiscountQuerySideModule_imports</title>
<polygon fill="none" stroke="black" points="4348,-295 4348,-347 4540,-347 4540,-295 4348,-295"/>
</g>
<g id="clust34" class="cluster">
<title>cluster_DiscountQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="3800,-295 3800,-347 4340,-347 4340,-295 3800,-295"/>
</g>
<g id="clust36" class="cluster">
<title>cluster_DiscountQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="3336,-295 3336,-347 3792,-347 3792,-295 3336,-295"/>
</g>
<g id="clust43" class="cluster">
<title>cluster_EmployeeQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="2644,-211 2644,-279 3418,-279 3418,-211 2644,-211"/>
</g>
<g id="clust46" class="cluster">
<title>cluster_EmployeeQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="3086,-219 3086,-271 3410,-271 3410,-219 3086,-219"/>
</g>
<g id="clust48" class="cluster">
<title>cluster_EmployeeQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="2652,-219 2652,-271 3078,-271 3078,-219 2652,-219"/>
</g>
<g id="clust49" class="cluster">
<title>cluster_HandoverDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="10592,-439 10592,-507 11312,-507 11312,-439 10592,-439"/>
</g>
<g id="clust54" class="cluster">
<title>cluster_HandoverDomainModule_providers</title>
<polygon fill="none" stroke="black" points="10600,-447 10600,-499 11304,-499 11304,-447 10600,-447"/>
</g>
<g id="clust55" class="cluster">
<title>cluster_HandoverQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8331,-439 8331,-507 9378,-507 9378,-439 8331,-439"/>
</g>
<g id="clust58" class="cluster">
<title>cluster_HandoverQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="8817,-447 8817,-499 9370,-499 9370,-447 8817,-447"/>
</g>
<g id="clust60" class="cluster">
<title>cluster_HandoverQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="8339,-447 8339,-499 8809,-499 8809,-447 8339,-447"/>
</g>
<g id="clust61" class="cluster">
<title>cluster_HandoverRequestDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="11778,-363 11778,-499 12684,-499 12684,-363 11778,-363"/>
</g>
<g id="clust66" class="cluster">
<title>cluster_HandoverRequestDomainModule_providers</title>
<polygon fill="none" stroke="black" points="11786,-371 11786,-423 12676,-423 12676,-371 11786,-371"/>
</g>
<g id="clust67" class="cluster">
<title>cluster_HandoverRequestQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="11066,-211 11066,-431 11770,-431 11770,-211 11066,-211"/>
</g>
<g id="clust70" class="cluster">
<title>cluster_HandoverRequestQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="11074,-371 11074,-423 11762,-423 11762,-371 11074,-371"/>
</g>
<g id="clust72" class="cluster">
<title>cluster_HandoverRequestQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="11121,-219 11121,-271 11715,-271 11715,-219 11121,-219"/>
</g>
<g id="clust73" class="cluster">
<title>cluster_HandoverScheduleDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="9414,-439 9414,-507 10584,-507 10584,-439 9414,-439"/>
</g>
<g id="clust76" class="cluster">
<title>cluster_HandoverScheduleDomainModule_exports</title>
<polygon fill="none" stroke="black" points="10346,-447 10346,-499 10576,-499 10576,-447 10346,-447"/>
</g>
<g id="clust78" class="cluster">
<title>cluster_HandoverScheduleDomainModule_providers</title>
<polygon fill="none" stroke="black" points="9422,-447 9422,-499 10338,-499 10338,-447 9422,-447"/>
</g>
<g id="clust79" class="cluster">
<title>cluster_HandoverScheduleQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="7418,-363 7418,-431 8760,-431 8760,-363 7418,-363"/>
</g>
<g id="clust82" class="cluster">
<title>cluster_HandoverScheduleQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="8046,-371 8046,-423 8752,-423 8752,-371 8046,-371"/>
</g>
<g id="clust84" class="cluster">
<title>cluster_HandoverScheduleQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="7426,-371 7426,-423 8038,-423 8038,-371 7426,-371"/>
</g>
<g id="clust85" class="cluster">
<title>cluster_HistoryImportQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="6273,-591 6273,-659 7463,-659 7463,-591 6273,-591"/>
</g>
<g id="clust88" class="cluster">
<title>cluster_HistoryImportQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="6827,-599 6827,-651 7455,-651 7455,-599 6827,-599"/>
</g>
<g id="clust90" class="cluster">
<title>cluster_HistoryImportQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="6281,-599 6281,-651 6819,-651 6819,-599 6281,-599"/>
</g>
<g id="clust91" class="cluster">
<title>cluster_LiquidationDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="3618,-591 3618,-659 4576,-659 4576,-591 3618,-591"/>
</g>
<g id="clust94" class="cluster">
<title>cluster_LiquidationDomainModule_exports</title>
<polygon fill="none" stroke="black" points="4380,-599 4380,-651 4568,-651 4568,-599 4380,-599"/>
</g>
<g id="clust96" class="cluster">
<title>cluster_LiquidationDomainModule_providers</title>
<polygon fill="none" stroke="black" points="3626,-599 3626,-651 4372,-651 4372,-599 3626,-599"/>
</g>
<g id="clust97" class="cluster">
<title>cluster_LiquidationQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="9710,-363 9710,-431 11030,-431 11030,-363 9710,-363"/>
</g>
<g id="clust100" class="cluster">
<title>cluster_LiquidationQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="10438,-371 10438,-423 11022,-423 11022,-371 10438,-371"/>
</g>
<g id="clust102" class="cluster">
<title>cluster_LiquidationQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="9718,-371 9718,-423 10430,-423 10430,-371 9718,-371"/>
</g>
<g id="clust109" class="cluster">
<title>cluster_LoggerModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="11778,-211 11778,-279 12252,-279 12252,-211 11778,-211"/>
</g>
<g id="clust112" class="cluster">
<title>cluster_LoggerModule_exports</title>
<polygon fill="none" stroke="black" points="11980,-219 11980,-271 12244,-271 12244,-219 11980,-219"/>
</g>
<g id="clust114" class="cluster">
<title>cluster_LoggerModule_providers</title>
<polygon fill="none" stroke="black" points="11786,-219 11786,-271 11972,-271 11972,-219 11786,-219"/>
</g>
<g id="clust115" class="cluster">
<title>cluster_MgsSenderModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="534,-135 534,-203 4614,-203 4614,-135 534,-135"/>
</g>
<g id="clust118" class="cluster">
<title>cluster_MgsSenderModule_exports</title>
<polygon fill="none" stroke="black" points="2828,-143 2828,-195 4606,-195 4606,-143 2828,-143"/>
</g>
<g id="clust120" class="cluster">
<title>cluster_MgsSenderModule_providers</title>
<polygon fill="none" stroke="black" points="542,-143 542,-195 2820,-195 2820,-143 542,-143"/>
</g>
<g id="clust121" class="cluster">
<title>cluster_PolicyDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="1064,-363 1064,-431 1934,-431 1934,-363 1064,-363"/>
</g>
<g id="clust126" class="cluster">
<title>cluster_PolicyDomainModule_providers</title>
<polygon fill="none" stroke="black" points="1072,-371 1072,-423 1926,-423 1926,-371 1072,-371"/>
</g>
<g id="clust127" class="cluster">
<title>cluster_PolicyQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8768,-363 8768,-431 9702,-431 9702,-363 8768,-363"/>
</g>
<g id="clust130" class="cluster">
<title>cluster_PolicyQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="9200,-371 9200,-423 9694,-423 9694,-371 9200,-371"/>
</g>
<g id="clust132" class="cluster">
<title>cluster_PolicyQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="8776,-371 8776,-423 9192,-423 9192,-371 8776,-371"/>
</g>
<g id="clust133" class="cluster">
<title>cluster_PrimaryContractDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="1441,-515 1441,-583 3178,-583 3178,-515 1441,-515"/>
</g>
<g id="clust136" class="cluster">
<title>cluster_PrimaryContractDomainModule_exports</title>
<polygon fill="none" stroke="black" points="2682,-523 2682,-575 3170,-575 3170,-523 2682,-523"/>
</g>
<g id="clust138" class="cluster">
<title>cluster_PrimaryContractDomainModule_providers</title>
<polygon fill="none" stroke="black" points="1449,-523 1449,-575 2674,-575 2674,-523 1449,-523"/>
</g>
<g id="clust139" class="cluster">
<title>cluster_PrimaryContractQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="3186,-515 3186,-583 4614,-583 4614,-515 3186,-515"/>
</g>
<g id="clust142" class="cluster">
<title>cluster_PrimaryContractQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="3776,-523 3776,-575 4606,-575 4606,-523 3776,-523"/>
</g>
<g id="clust144" class="cluster">
<title>cluster_PrimaryContractQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="3194,-523 3194,-575 3768,-575 3768,-523 3194,-523"/>
</g>
<g id="clust145" class="cluster">
<title>cluster_ProposalDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="542,-591 542,-659 1236,-659 1236,-591 542,-591"/>
</g>
<g id="clust150" class="cluster">
<title>cluster_ProposalDomainModule_providers</title>
<polygon fill="none" stroke="black" points="550,-599 550,-651 1228,-651 1228,-599 550,-599"/>
</g>
<g id="clust151" class="cluster">
<title>cluster_ProposalQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="7471,-591 7471,-659 8481,-659 8481,-591 7471,-591"/>
</g>
<g id="clust154" class="cluster">
<title>cluster_ProposalQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="7939,-599 7939,-651 8473,-651 8473,-599 7939,-599"/>
</g>
<g id="clust156" class="cluster">
<title>cluster_ProposalQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="7479,-599 7479,-651 7931,-651 7931,-599 7479,-599"/>
</g>
<g id="clust163" class="cluster">
<title>cluster_ScheduleDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="480,-363 480,-431 952,-431 952,-363 480,-363"/>
</g>
<g id="clust168" class="cluster">
<title>cluster_ScheduleDomainModule_providers</title>
<polygon fill="none" stroke="black" points="488,-371 488,-423 944,-423 944,-371 488,-371"/>
</g>
<g id="clust169" class="cluster">
<title>cluster_ScheduleQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="6273,-439 6273,-507 7298,-507 7298,-439 6273,-439"/>
</g>
<g id="clust172" class="cluster">
<title>cluster_ScheduleQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="6748,-447 6748,-499 7290,-499 7290,-447 6748,-447"/>
</g>
<g id="clust174" class="cluster">
<title>cluster_ScheduleQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="6281,-447 6281,-499 6740,-499 6740,-447 6281,-447"/>
</g>
<g id="clust175" class="cluster">
<title>cluster_StatushistoryExportModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="12692,-211 12692,-431 13236,-431 13236,-211 12692,-211"/>
</g>
<g id="clust178" class="cluster">
<title>cluster_StatushistoryExportModule_exports</title>
<polygon fill="none" stroke="black" points="12700,-371 12700,-423 12888,-423 12888,-371 12700,-371"/>
</g>
<g id="clust180" class="cluster">
<title>cluster_StatushistoryExportModule_providers</title>
<polygon fill="none" stroke="black" points="12700,-219 12700,-271 13228,-271 13228,-219 12700,-219"/>
</g>
<g id="clust181" class="cluster">
<title>cluster_StatushistoryModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="3492,-211 3492,-279 4326,-279 4326,-211 3492,-211"/>
</g>
<g id="clust184" class="cluster">
<title>cluster_StatushistoryModule_exports</title>
<polygon fill="none" stroke="black" points="4168,-219 4168,-271 4318,-271 4318,-219 4168,-219"/>
</g>
<g id="clust186" class="cluster">
<title>cluster_StatushistoryModule_providers</title>
<polygon fill="none" stroke="black" points="3500,-219 3500,-271 4160,-271 4160,-219 3500,-219"/>
</g>
<g id="clust187" class="cluster">
<title>cluster_TransferHistoryModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="7306,-439 7306,-507 8323,-507 8323,-439 7306,-439"/>
</g>
<g id="clust190" class="cluster">
<title>cluster_TransferHistoryModule_exports</title>
<polygon fill="none" stroke="black" points="7790,-447 7790,-499 8315,-499 8315,-447 7790,-447"/>
</g>
<g id="clust192" class="cluster">
<title>cluster_TransferHistoryModule_providers</title>
<polygon fill="none" stroke="black" points="7314,-447 7314,-499 7782,-499 7782,-447 7314,-447"/>
</g>
<!-- AuthModule -->
<g id="node1" class="node">
<title>AuthModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5167.55,-263 5164.55,-267 5143.55,-267 5140.55,-263 5080.45,-263 5080.45,-227 5167.55,-227 5167.55,-263"/>
<text text-anchor="middle" x="5124" y="-240.8" font-family="Times,serif" font-size="14.00">AuthModule</text>
</g>
<!-- DiscountQuerySideModule -->
<g id="node3" class="node">
<title>DiscountQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4827.97,-415 4824.97,-419 4803.97,-419 4800.97,-415 4658.03,-415 4658.03,-379 4827.97,-379 4827.97,-415"/>
<text text-anchor="middle" x="4743" y="-392.8" font-family="Times,serif" font-size="14.00">DiscountQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;DiscountQuerySideModule -->
<g id="edge47" class="edge">
<title>AuthModule&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M5080.41,-254C4995.27,-254 4815.16,-254 4815.16,-254 4815.16,-254 4815.16,-368.89 4815.16,-368.89"/>
<polygon fill="black" stroke="black" points="4811.66,-368.89 4815.16,-378.89 4818.66,-368.89 4811.66,-368.89"/>
</g>
<!-- HandoverQuerySideModule -->
<g id="node5" class="node">
<title>HandoverQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="6115.12,-415 6112.12,-419 6091.12,-419 6088.12,-415 5940.88,-415 5940.88,-379 6115.12,-379 6115.12,-415"/>
<text text-anchor="middle" x="6028" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;HandoverQuerySideModule -->
<g id="edge75" class="edge">
<title>AuthModule&#45;&gt;HandoverQuerySideModule</title>
<path fill="none" stroke="black" d="M5150.26,-263.12C5150.26,-309.15 5150.26,-427 5150.26,-427 5150.26,-427 5943.18,-427 5943.18,-427 5943.18,-427 5943.18,-425.14 5943.18,-425.14"/>
<polygon fill="black" stroke="black" points="5946.68,-425.14 5943.18,-415.14 5939.68,-425.14 5946.68,-425.14"/>
</g>
<!-- HandoverScheduleQuerySideModule -->
<g id="node7" class="node">
<title>HandoverScheduleQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5852.93,-339 5849.93,-343 5828.93,-343 5825.93,-339 5627.07,-339 5627.07,-303 5852.93,-303 5852.93,-339"/>
<text text-anchor="middle" x="5740" y="-316.8" font-family="Times,serif" font-size="14.00">HandoverScheduleQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;HandoverScheduleQuerySideModule -->
<g id="edge113" class="edge">
<title>AuthModule&#45;&gt;HandoverScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M5167.63,-245C5304.67,-245 5717.41,-245 5717.41,-245 5717.41,-245 5717.41,-292.99 5717.41,-292.99"/>
<polygon fill="black" stroke="black" points="5713.91,-292.99 5717.41,-302.99 5720.91,-292.99 5713.91,-292.99"/>
</g>
<!-- HistoryImportQuerySideModule -->
<g id="node8" class="node">
<title>HistoryImportQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5770.52,-567 5767.52,-571 5746.52,-571 5743.52,-567 5571.48,-567 5571.48,-531 5770.52,-531 5770.52,-567"/>
<text text-anchor="middle" x="5671" y="-544.8" font-family="Times,serif" font-size="14.00">HistoryImportQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;HistoryImportQuerySideModule -->
<g id="edge122" class="edge">
<title>AuthModule&#45;&gt;HistoryImportQuerySideModule</title>
<path fill="none" stroke="black" d="M5167.85,-250C5285.47,-250 5599.14,-250 5599.14,-250 5599.14,-250 5599.14,-520.75 5599.14,-520.75"/>
<polygon fill="black" stroke="black" points="5595.64,-520.75 5599.14,-530.75 5602.64,-520.75 5595.64,-520.75"/>
</g>
<!-- LiquidationQuerySideModule -->
<g id="node10" class="node">
<title>LiquidationQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="6115.25,-339 6112.25,-343 6091.25,-343 6088.25,-339 5930.75,-339 5930.75,-303 6115.25,-303 6115.25,-339"/>
<text text-anchor="middle" x="6023" y="-316.8" font-family="Times,serif" font-size="14.00">LiquidationQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;LiquidationQuerySideModule -->
<g id="edge141" class="edge">
<title>AuthModule&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M5167.65,-241C5343.71,-241 5992.21,-241 5992.21,-241 5992.21,-241 5992.21,-292.85 5992.21,-292.85"/>
<polygon fill="black" stroke="black" points="5988.71,-292.85 5992.21,-302.85 5995.71,-292.85 5988.71,-292.85"/>
</g>
<!-- PolicyQuerySideModule -->
<g id="node15" class="node">
<title>PolicyQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5528.7,-339 5525.7,-343 5504.7,-343 5501.7,-339 5373.3,-339 5373.3,-303 5528.7,-303 5528.7,-339"/>
<text text-anchor="middle" x="5451" y="-316.8" font-family="Times,serif" font-size="14.00">PolicyQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;PolicyQuerySideModule -->
<g id="edge210" class="edge">
<title>AuthModule&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M5167.58,-254C5244.7,-254 5397.14,-254 5397.14,-254 5397.14,-254 5397.14,-292.69 5397.14,-292.69"/>
<polygon fill="black" stroke="black" points="5393.64,-292.69 5397.14,-302.69 5400.64,-292.69 5393.64,-292.69"/>
</g>
<!-- PrimaryContractQuerySideModule -->
<g id="node17" class="node">
<title>PrimaryContractQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5222.23,-491 5219.23,-495 5198.23,-495 5195.23,-491 5009.77,-491 5009.77,-455 5222.23,-455 5222.23,-491"/>
<text text-anchor="middle" x="5116" y="-468.8" font-family="Times,serif" font-size="14.00">PrimaryContractQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge239" class="edge">
<title>AuthModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M5115.25,-263.27C5115.25,-263.27 5115.25,-444.96 5115.25,-444.96"/>
<polygon fill="black" stroke="black" points="5111.75,-444.96 5115.25,-454.96 5118.75,-444.96 5111.75,-444.96"/>
</g>
<!-- ProposalQuerySideModule -->
<g id="node19" class="node">
<title>ProposalQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5957.41,-567 5954.41,-571 5933.41,-571 5930.41,-567 5788.59,-567 5788.59,-531 5957.41,-531 5957.41,-567"/>
<text text-anchor="middle" x="5873" y="-544.8" font-family="Times,serif" font-size="14.00">ProposalQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;ProposalQuerySideModule -->
<g id="edge267" class="edge">
<title>AuthModule&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M5132.75,-263.18C5132.75,-311.95 5132.75,-442 5132.75,-442 5132.75,-442 5798.28,-442 5798.28,-442 5798.28,-442 5798.28,-520.85 5798.28,-520.85"/>
<polygon fill="black" stroke="black" points="5794.78,-520.85 5798.28,-530.85 5801.78,-520.85 5794.78,-520.85"/>
</g>
<!-- ScheduleQuerySideModule -->
<g id="node21" class="node">
<title>ScheduleQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5476.24,-415 5473.24,-419 5452.24,-419 5449.24,-415 5305.76,-415 5305.76,-379 5476.24,-379 5476.24,-415"/>
<text text-anchor="middle" x="5391" y="-392.8" font-family="Times,serif" font-size="14.00">ScheduleQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;ScheduleQuerySideModule -->
<g id="edge289" class="edge">
<title>AuthModule&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M5167.8,-259C5232.06,-259 5344.36,-259 5344.36,-259 5344.36,-259 5344.36,-368.96 5344.36,-368.96"/>
<polygon fill="black" stroke="black" points="5340.86,-368.96 5344.36,-378.96 5347.86,-368.96 5340.86,-368.96"/>
</g>
<!-- StatushistoryModule -->
<g id="node22" class="node">
<title>StatushistoryModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4802.16,-339 4799.16,-343 4778.16,-343 4775.16,-339 4669.84,-339 4669.84,-303 4802.16,-303 4802.16,-339"/>
<text text-anchor="middle" x="4736" y="-316.8" font-family="Times,serif" font-size="14.00">StatushistoryModule</text>
</g>
<!-- AuthModule&#45;&gt;StatushistoryModule -->
<g id="edge309" class="edge">
<title>AuthModule&#45;&gt;StatushistoryModule</title>
<path fill="none" stroke="black" d="M5080.17,-245C4989.31,-245 4788.77,-245 4788.77,-245 4788.77,-245 4788.77,-292.99 4788.77,-292.99"/>
<polygon fill="black" stroke="black" points="4785.27,-292.99 4788.77,-302.99 4792.27,-292.99 4785.27,-292.99"/>
</g>
<!-- ApplicationModule -->
<g id="node24" class="node">
<title>ApplicationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="6256.65,-773 6253.65,-777 6232.65,-777 6229.65,-773 6131.35,-773 6131.35,-737 6256.65,-737 6256.65,-773"/>
<text text-anchor="middle" x="6194" y="-750.8" font-family="Times,serif" font-size="14.00">ApplicationModule</text>
</g>
<!-- AuthModule&#45;&gt;ApplicationModule -->
<g id="edge1" class="edge">
<title>AuthModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5167.65,-236C5372.54,-236 6228.9,-236 6228.9,-236 6228.9,-236 6228.9,-726.83 6228.9,-726.83"/>
<polygon fill="black" stroke="black" points="6225.4,-726.83 6228.9,-736.83 6232.4,-726.83 6225.4,-726.83"/>
</g>
<!-- AuthService  -->
<g id="node26" class="node">
<title>AuthService </title>
<polygon fill="#fb8072" stroke="black" points="509.98,-187 420.02,-187 420.02,-151 509.98,-151 509.98,-187"/>
<text text-anchor="middle" x="465" y="-164.8" font-family="Times,serif" font-size="14.00">AuthService </text>
</g>
<!-- AuthModule&#45;&gt;AuthService  -->
<g id="edge27" class="edge">
<title>AuthModule&#45;&gt;AuthService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5155.27,-226.96C5155.27,-213.71 5155.27,-198 5155.27,-198 5155.27,-198 502.7,-198 502.7,-198 502.7,-198 502.7,-196.92 502.7,-196.92"/>
<polygon fill="black" stroke="black" points="506.2,-197.2 502.7,-187.2 499.2,-197.2 506.2,-197.2"/>
</g>
<!-- EmployeeQuerySideModule -->
<g id="node42" class="node">
<title>EmployeeQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4532.18,-339 4529.18,-343 4508.18,-343 4505.18,-339 4355.82,-339 4355.82,-303 4532.18,-303 4532.18,-339"/>
<text text-anchor="middle" x="4444" y="-316.8" font-family="Times,serif" font-size="14.00">EmployeeQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;EmployeeQuerySideModule -->
<g id="edge58" class="edge">
<title>AuthModule&#45;&gt;EmployeeQuerySideModule</title>
<path fill="none" stroke="black" d="M5080.38,-236C4964.96,-236 4660.35,-236 4660.35,-236 4660.35,-236 4660.35,-315 4660.35,-315 4660.35,-315 4542.31,-315 4542.31,-315"/>
<polygon fill="black" stroke="black" points="4542.31,-311.5 4532.31,-315 4542.31,-318.5 4542.31,-311.5"/>
</g>
<!-- HandoverRequestQuerySideModule -->
<g id="node64" class="node">
<title>HandoverRequestQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="11537.72,-339 11534.72,-343 11513.72,-343 11510.72,-339 11318.28,-339 11318.28,-303 11537.72,-303 11537.72,-339"/>
<text text-anchor="middle" x="11428" y="-316.8" font-family="Times,serif" font-size="14.00">HandoverRequestQuerySideModule</text>
</g>
<!-- AuthModule&#45;&gt;HandoverRequestQuerySideModule -->
<g id="edge93" class="edge">
<title>AuthModule&#45;&gt;HandoverRequestQuerySideModule</title>
<path fill="none" stroke="black" d="M5167.59,-232C5373.16,-232 6235.88,-232 6235.88,-232 6235.88,-232 6235.88,-307 6235.88,-307 6235.88,-307 11308.06,-307 11308.06,-307"/>
<polygon fill="black" stroke="black" points="11308.06,-310.5 11318.06,-307 11308.06,-303.5 11308.06,-310.5"/>
</g>
<!-- StatushistoryExportModule -->
<g id="node163" class="node">
<title>StatushistoryExportModule</title>
<polygon fill="#8dd3c7" stroke="black" points="12874.26,-339 12871.26,-343 12850.26,-343 12847.26,-339 12703.74,-339 12703.74,-303 12874.26,-303 12874.26,-339"/>
<text text-anchor="middle" x="12789" y="-316.8" font-family="Times,serif" font-size="14.00">StatushistoryExportModule</text>
</g>
<!-- AuthModule&#45;&gt;StatushistoryExportModule -->
<g id="edge301" class="edge">
<title>AuthModule&#45;&gt;StatushistoryExportModule</title>
<path fill="none" stroke="black" d="M5097.74,-263.15C5097.74,-264.3 5097.74,-265 5097.74,-265 5097.74,-265 12807.95,-265 12807.95,-265 12807.95,-265 12807.95,-292.97 12807.95,-292.97"/>
<polygon fill="black" stroke="black" points="12804.45,-292.97 12807.95,-302.97 12811.45,-292.97 12804.45,-292.97"/>
</g>
<!-- DiscountDomainModule -->
<g id="node2" class="node">
<title>DiscountDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5960.7,-491 5957.7,-495 5936.7,-495 5933.7,-491 5805.3,-491 5805.3,-455 5960.7,-455 5960.7,-491"/>
<text text-anchor="middle" x="5883" y="-468.8" font-family="Times,serif" font-size="14.00">DiscountDomainModule</text>
</g>
<!-- DiscountDomainModule&#45;&gt;ApplicationModule -->
<g id="edge2" class="edge">
<title>DiscountDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5960.9,-479C6050.97,-479 6187.02,-479 6187.02,-479 6187.02,-479 6187.02,-726.89 6187.02,-726.89"/>
<polygon fill="black" stroke="black" points="6183.52,-726.89 6187.02,-736.89 6190.52,-726.89 6183.52,-726.89"/>
</g>
<!-- DiscountQuerySideModule&#45;&gt;DiscountDomainModule -->
<g id="edge39" class="edge">
<title>DiscountQuerySideModule&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M4827.99,-388C4969.73,-388 5239.02,-388 5239.02,-388 5239.02,-388 5239.02,-475 5239.02,-475 5239.02,-475 5795.32,-475 5795.32,-475"/>
<polygon fill="black" stroke="black" points="5795.32,-478.5 5805.32,-475 5795.32,-471.5 5795.32,-478.5"/>
</g>
<!-- PolicyDomainModule -->
<g id="node14" class="node">
<title>PolicyDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4972.43,-491 4969.43,-495 4948.43,-495 4945.43,-491 4831.57,-491 4831.57,-455 4972.43,-455 4972.43,-491"/>
<text text-anchor="middle" x="4902" y="-468.8" font-family="Times,serif" font-size="14.00">PolicyDomainModule</text>
</g>
<!-- DiscountQuerySideModule&#45;&gt;PolicyDomainModule -->
<g id="edge199" class="edge">
<title>DiscountQuerySideModule&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M4825.18,-415.01C4825.18,-437.49 4825.18,-473 4825.18,-473 4825.18,-473 4825.84,-473 4825.84,-473"/>
<polygon fill="black" stroke="black" points="4821.73,-476.5 4831.73,-473 4821.73,-469.5 4821.73,-476.5"/>
</g>
<!-- DiscountQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge3" class="edge">
<title>DiscountQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M4822.38,-415.03C4822.38,-421.85 4822.38,-428 4822.38,-428 4822.38,-428 6194,-428 6194,-428 6194,-428 6194,-726.95 6194,-726.95"/>
<polygon fill="black" stroke="black" points="6190.5,-726.95 6194,-736.95 6197.5,-726.95 6190.5,-726.95"/>
</g>
<!-- DiscountQueryRepository  -->
<g id="node43" class="node">
<title>DiscountQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="4140.47,-339 3973.53,-339 3973.53,-303 4140.47,-303 4140.47,-339"/>
<text text-anchor="middle" x="4057" y="-316.8" font-family="Times,serif" font-size="14.00">DiscountQueryRepository </text>
</g>
<!-- DiscountQuerySideModule&#45;&gt;DiscountQueryRepository  -->
<g id="edge53" class="edge">
<title>DiscountQuerySideModule&#45;&gt;DiscountQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4657.91,-390C4478.79,-390 4079.17,-390 4079.17,-390 4079.17,-390 4079.17,-349.13 4079.17,-349.13"/>
<polygon fill="black" stroke="black" points="4082.68,-349.13 4079.17,-339.13 4075.68,-349.13 4082.68,-349.13"/>
</g>
<!-- DiscountQueryService  -->
<g id="node44" class="node">
<title>DiscountQueryService </title>
<polygon fill="#fb8072" stroke="black" points="3955.51,-339 3808.49,-339 3808.49,-303 3955.51,-303 3955.51,-339"/>
<text text-anchor="middle" x="3882" y="-316.8" font-family="Times,serif" font-size="14.00">DiscountQueryService </text>
</g>
<!-- DiscountQuerySideModule&#45;&gt;DiscountQueryService  -->
<g id="edge54" class="edge">
<title>DiscountQuerySideModule&#45;&gt;DiscountQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4657.69,-395C4440.19,-395 3882,-395 3882,-395 3882,-395 3882,-349.29 3882,-349.29"/>
<polygon fill="black" stroke="black" points="3885.5,-349.29 3882,-339.29 3878.5,-349.29 3885.5,-349.29"/>
</g>
<!-- DiscountQuerySideModule  -->
<g id="node45" class="node">
<title>DiscountQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="4331.97,-339 4158.03,-339 4158.03,-303 4331.97,-303 4331.97,-339"/>
<text text-anchor="middle" x="4245" y="-316.8" font-family="Times,serif" font-size="14.00">DiscountQuerySideModule </text>
</g>
<!-- DiscountQuerySideModule&#45;&gt;DiscountQuerySideModule  -->
<g id="edge55" class="edge">
<title>DiscountQuerySideModule&#45;&gt;DiscountQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4657.81,-385C4523.73,-385 4278.56,-385 4278.56,-385 4278.56,-385 4278.56,-349.17 4278.56,-349.17"/>
<polygon fill="black" stroke="black" points="4282.06,-349.17 4278.56,-339.17 4275.06,-349.17 4282.06,-349.17"/>
</g>
<!-- HandoverDomainModule -->
<g id="node4" class="node">
<title>HandoverDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5552.85,-567 5549.85,-571 5528.85,-571 5525.85,-567 5393.15,-567 5393.15,-531 5552.85,-531 5552.85,-567"/>
<text text-anchor="middle" x="5473" y="-544.8" font-family="Times,serif" font-size="14.00">HandoverDomainModule</text>
</g>
<!-- HandoverDomainModule&#45;&gt;ApplicationModule -->
<g id="edge4" class="edge">
<title>HandoverDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5473,-567.1C5473,-616.33 5473,-749 5473,-749 5473,-749 6121.28,-749 6121.28,-749"/>
<polygon fill="black" stroke="black" points="6121.28,-752.5 6131.28,-749 6121.28,-745.5 6121.28,-752.5"/>
</g>
<!-- HandoverQuerySideModule&#45;&gt;HandoverDomainModule -->
<g id="edge67" class="edge">
<title>HandoverQuerySideModule&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M5950.65,-415.24C5950.65,-430.57 5950.65,-450 5950.65,-450 5950.65,-450 5548.06,-450 5548.06,-450 5548.06,-450 5548.06,-520.84 5548.06,-520.84"/>
<polygon fill="black" stroke="black" points="5544.56,-520.84 5548.06,-530.84 5551.56,-520.84 5544.56,-520.84"/>
</g>
<!-- HandoverScheduleDomainModule -->
<g id="node6" class="node">
<title>HandoverScheduleDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5374.67,-567 5371.67,-571 5350.67,-571 5347.67,-567 5163.33,-567 5163.33,-531 5374.67,-531 5374.67,-567"/>
<text text-anchor="middle" x="5269" y="-544.8" font-family="Times,serif" font-size="14.00">HandoverScheduleDomainModule</text>
</g>
<!-- HandoverQuerySideModule&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge104" class="edge">
<title>HandoverQuerySideModule&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M5948.16,-415.04C5948.16,-428.29 5948.16,-444 5948.16,-444 5948.16,-444 5328.62,-444 5328.62,-444 5328.62,-444 5328.62,-520.71 5328.62,-520.71"/>
<polygon fill="black" stroke="black" points="5325.12,-520.71 5328.62,-530.71 5332.12,-520.71 5325.12,-520.71"/>
</g>
<!-- ListenerModule -->
<g id="node11" class="node">
<title>ListenerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5161.92,-773 5158.92,-777 5137.92,-777 5134.92,-773 5056.08,-773 5056.08,-737 5161.92,-737 5161.92,-773"/>
<text text-anchor="middle" x="5109" y="-750.8" font-family="Times,serif" font-size="14.00">ListenerModule</text>
</g>
<!-- HandoverQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge153" class="edge">
<title>HandoverQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M6012.17,-415.15C6012.17,-489.63 6012.17,-768 6012.17,-768 6012.17,-768 5171.79,-768 5171.79,-768"/>
<polygon fill="black" stroke="black" points="5171.79,-764.5 5161.79,-768 5171.79,-771.5 5171.79,-764.5"/>
</g>
<!-- PrimaryContractDomainModule -->
<g id="node16" class="node">
<title>PrimaryContractDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5039.96,-643 5036.96,-647 5015.96,-647 5012.96,-643 4842.04,-643 4842.04,-607 5039.96,-607 5039.96,-643"/>
<text text-anchor="middle" x="4941" y="-620.8" font-family="Times,serif" font-size="14.00">PrimaryContractDomainModule</text>
</g>
<!-- HandoverQuerySideModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge223" class="edge">
<title>HandoverQuerySideModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5977.79,-415.13C5977.79,-470.39 5977.79,-633 5977.79,-633 5977.79,-633 5050.41,-633 5050.41,-633"/>
<polygon fill="black" stroke="black" points="5050.41,-629.5 5040.41,-633 5050.41,-636.5 5050.41,-629.5"/>
</g>
<!-- HandoverQuerySideModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge242" class="edge">
<title>HandoverQuerySideModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M5945.67,-415.06C5945.67,-426.44 5945.67,-439 5945.67,-439 5945.67,-439 5200.53,-439 5200.53,-439 5200.53,-439 5200.53,-444.81 5200.53,-444.81"/>
<polygon fill="black" stroke="black" points="5197.03,-444.81 5200.53,-454.81 5204.03,-444.81 5197.03,-444.81"/>
</g>
<!-- HandoverQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge5" class="edge">
<title>HandoverQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M6115.25,-403C6158.24,-403 6200.98,-403 6200.98,-403 6200.98,-403 6200.98,-726.57 6200.98,-726.57"/>
<polygon fill="black" stroke="black" points="6197.48,-726.57 6200.98,-736.57 6204.48,-726.57 6197.48,-726.57"/>
</g>
<!-- HandoverQueryRepository  -->
<g id="node55" class="node">
<title>HandoverQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="9361.62,-491 9190.38,-491 9190.38,-455 9361.62,-455 9361.62,-491"/>
<text text-anchor="middle" x="9276" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverQueryRepository </text>
</g>
<!-- HandoverQuerySideModule&#45;&gt;HandoverQueryRepository  -->
<g id="edge80" class="edge">
<title>HandoverQuerySideModule&#45;&gt;HandoverQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6071.65,-378.96C6071.65,-365.71 6071.65,-350 6071.65,-350 6071.65,-350 9199.3,-350 9199.3,-350 9199.3,-350 9199.3,-444.85 9199.3,-444.85"/>
<polygon fill="black" stroke="black" points="9195.8,-444.85 9199.3,-454.85 9202.8,-444.85 9195.8,-444.85"/>
</g>
<!-- HandoverQueryService  -->
<g id="node56" class="node">
<title>HandoverQueryService </title>
<polygon fill="#fb8072" stroke="black" points="9172.66,-491 9021.34,-491 9021.34,-455 9172.66,-455 9172.66,-491"/>
<text text-anchor="middle" x="9097" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverQueryService </text>
</g>
<!-- HandoverQuerySideModule&#45;&gt;HandoverQueryService  -->
<g id="edge81" class="edge">
<title>HandoverQuerySideModule&#45;&gt;HandoverQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6046.55,-415.16C6046.55,-443.74 6046.55,-496 6046.55,-496 6046.55,-496 9097,-496 9097,-496 9097,-496 9097,-495.51 9097,-495.51"/>
<polygon fill="black" stroke="black" points="9100.5,-501.12 9097,-491.12 9093.5,-501.12 9100.5,-501.12"/>
</g>
<!-- HandoverQuerySideModule  -->
<g id="node57" class="node">
<title>HandoverQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="9003.12,-491 8824.88,-491 8824.88,-455 9003.12,-455 9003.12,-491"/>
<text text-anchor="middle" x="8914" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverQuerySideModule </text>
</g>
<!-- HandoverQuerySideModule&#45;&gt;HandoverQuerySideModule  -->
<g id="edge82" class="edge">
<title>HandoverQuerySideModule&#45;&gt;HandoverQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6063.74,-415.43C6063.74,-443.82 6063.74,-495 6063.74,-495 6063.74,-495 8914,-495 8914,-495 8914,-495 8914,-494.62 8914,-494.62"/>
<polygon fill="black" stroke="black" points="8917.5,-501.17 8914,-491.17 8910.5,-501.17 8917.5,-501.17"/>
</g>
<!-- HandoverRequestDomainModule -->
<g id="node60" class="node">
<title>HandoverRequestDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="12015.45,-491 12012.45,-495 11991.45,-495 11988.45,-491 11810.55,-491 11810.55,-455 12015.45,-455 12015.45,-491"/>
<text text-anchor="middle" x="11913" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverRequestDomainModule</text>
</g>
<!-- HandoverQuerySideModule&#45;&gt;HandoverRequestDomainModule -->
<g id="edge87" class="edge">
<title>HandoverQuerySideModule&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M6029.36,-415.06C6029.36,-444.11 6029.36,-498 6029.36,-498 6029.36,-498 11913,-498 11913,-498 11913,-498 11913,-497.32 11913,-497.32"/>
<polygon fill="black" stroke="black" points="11916.5,-501.17 11913,-491.17 11909.5,-501.17 11916.5,-501.17"/>
</g>
<!-- HandoverScheduleDomainModule&#45;&gt;ListenerModule -->
<g id="edge154" class="edge">
<title>HandoverScheduleDomainModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5163.28,-558C5149.82,-558 5140.63,-558 5140.63,-558 5140.63,-558 5140.63,-726.82 5140.63,-726.82"/>
<polygon fill="black" stroke="black" points="5137.13,-726.82 5140.63,-736.82 5144.13,-726.82 5137.13,-726.82"/>
</g>
<!-- HandoverScheduleDomainModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge224" class="edge">
<title>HandoverScheduleDomainModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5163.16,-549C5101.96,-549 5036.95,-549 5036.95,-549 5036.95,-549 5036.95,-596.99 5036.95,-596.99"/>
<polygon fill="black" stroke="black" points="5033.45,-596.99 5036.95,-606.99 5040.45,-596.99 5033.45,-596.99"/>
</g>
<!-- HandoverScheduleDomainModule&#45;&gt;ApplicationModule -->
<g id="edge6" class="edge">
<title>HandoverScheduleDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5269,-567.09C5269,-617.7 5269,-757 5269,-757 5269,-757 6121.15,-757 6121.15,-757"/>
<polygon fill="black" stroke="black" points="6121.15,-760.5 6131.15,-757 6121.15,-753.5 6121.15,-760.5"/>
</g>
<!-- HandoverScheduleDomainService  -->
<g id="node70" class="node">
<title>HandoverScheduleDomainService </title>
<polygon fill="#fb8072" stroke="black" points="10567.59,-491 10354.41,-491 10354.41,-455 10567.59,-455 10567.59,-491"/>
<text text-anchor="middle" x="10461" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverScheduleDomainService </text>
</g>
<!-- HandoverScheduleDomainModule&#45;&gt;HandoverScheduleDomainService  -->
<g id="edge109" class="edge">
<title>HandoverScheduleDomainModule&#45;&gt;HandoverScheduleDomainService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5343.94,-530.59C5343.94,-500.96 5343.94,-446 5343.94,-446 5343.94,-446 10388.03,-446 10388.03,-446 10388.03,-446 10388.03,-446.88 10388.03,-446.88"/>
<polygon fill="black" stroke="black" points="10384.53,-444.84 10388.03,-454.84 10391.53,-444.84 10384.53,-444.84"/>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;HandoverDomainModule -->
<g id="edge68" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M5626.94,-311C5583.08,-311 5543.19,-311 5543.19,-311 5543.19,-311 5543.19,-520.72 5543.19,-520.72"/>
<polygon fill="black" stroke="black" points="5539.69,-520.72 5543.19,-530.72 5546.69,-520.72 5539.69,-520.72"/>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;HandoverQuerySideModule -->
<g id="edge76" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;HandoverQuerySideModule</title>
<path fill="none" stroke="black" d="M5853.04,-321C5892.61,-321 5927.37,-321 5927.37,-321 5927.37,-321 5927.37,-403 5927.37,-403 5927.37,-403 5930.52,-403 5930.52,-403"/>
<polygon fill="black" stroke="black" points="5930.52,-406.5 5940.52,-403 5930.52,-399.5 5930.52,-406.5"/>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge105" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M5674.94,-339C5674.94,-372.94 5674.94,-443 5674.94,-443 5674.94,-443 5320.95,-443 5320.95,-443 5320.95,-443 5320.95,-520.54 5320.95,-520.54"/>
<polygon fill="black" stroke="black" points="5317.45,-520.54 5320.95,-530.54 5324.45,-520.54 5317.45,-520.54"/>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge155" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5626.84,-332C5594.67,-332 5568.19,-332 5568.19,-332 5568.19,-332 5568.19,-765 5568.19,-765 5568.19,-765 5172.12,-765 5172.12,-765"/>
<polygon fill="black" stroke="black" points="5172.12,-761.5 5162.12,-765 5172.12,-768.5 5172.12,-761.5"/>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge225" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5626.86,-325C5588.88,-325 5555.98,-325 5555.98,-325 5555.98,-325 5555.98,-613 5555.98,-613 5555.98,-613 5050.15,-613 5050.15,-613"/>
<polygon fill="black" stroke="black" points="5050.15,-609.5 5040.15,-613 5050.15,-616.5 5050.15,-609.5"/>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge243" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M5722.85,-339.04C5722.85,-376.45 5722.85,-459 5722.85,-459 5722.85,-459 5232.4,-459 5232.4,-459"/>
<polygon fill="black" stroke="black" points="5232.4,-455.5 5222.4,-459 5232.4,-462.5 5232.4,-455.5"/>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge7" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5813.25,-339.06C5813.25,-350.44 5813.25,-363 5813.25,-363 5813.25,-363 6214.94,-363 6214.94,-363 6214.94,-363 6214.94,-726.88 6214.94,-726.88"/>
<polygon fill="black" stroke="black" points="6211.44,-726.88 6214.94,-736.88 6218.44,-726.88 6211.44,-726.88"/>
</g>
<!-- HandoverScheduleQueryRepository  -->
<g id="node74" class="node">
<title>HandoverScheduleQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="8276.43,-415 8053.57,-415 8053.57,-379 8276.43,-379 8276.43,-415"/>
<text text-anchor="middle" x="8165" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverScheduleQueryRepository </text>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleQueryRepository  -->
<g id="edge117" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5823.18,-339.19C5823.18,-347.28 5823.18,-355 5823.18,-355 5823.18,-355 8165,-355 8165,-355 8165,-355 8165,-368.94 8165,-368.94"/>
<polygon fill="black" stroke="black" points="8161.5,-368.94 8165,-378.94 8168.5,-368.94 8161.5,-368.94"/>
</g>
<!-- HandoverScheduleQueryService  -->
<g id="node75" class="node">
<title>HandoverScheduleQueryService </title>
<polygon fill="#fb8072" stroke="black" points="8744.47,-415 8541.53,-415 8541.53,-379 8744.47,-379 8744.47,-415"/>
<text text-anchor="middle" x="8643" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverScheduleQueryService </text>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleQueryService  -->
<g id="edge118" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5843.04,-339.03C5843.04,-345.85 5843.04,-352 5843.04,-352 5843.04,-352 8643,-352 8643,-352 8643,-352 8643,-368.96 8643,-368.96"/>
<polygon fill="black" stroke="black" points="8639.5,-368.96 8643,-378.96 8646.5,-368.96 8639.5,-368.96"/>
</g>
<!-- HandoverScheduleQuerySideModule  -->
<g id="node76" class="node">
<title>HandoverScheduleQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="8523.93,-415 8294.07,-415 8294.07,-379 8523.93,-379 8523.93,-415"/>
<text text-anchor="middle" x="8409" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverScheduleQuerySideModule </text>
</g>
<!-- HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleQuerySideModule  -->
<g id="edge119" class="edge">
<title>HandoverScheduleQuerySideModule&#45;&gt;HandoverScheduleQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5833.11,-339.24C5833.11,-346.43 5833.11,-353 5833.11,-353 5833.11,-353 8409,-353 8409,-353 8409,-353 8409,-368.85 8409,-368.85"/>
<polygon fill="black" stroke="black" points="8405.5,-368.85 8409,-378.85 8412.5,-368.85 8405.5,-368.85"/>
</g>
<!-- HistoryImportQuerySideModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge226" class="edge">
<title>HistoryImportQuerySideModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5695.94,-567.29C5695.94,-589.21 5695.94,-623 5695.94,-623 5695.94,-623 5050.08,-623 5050.08,-623"/>
<polygon fill="black" stroke="black" points="5050.08,-619.5 5040.08,-623 5050.08,-626.5 5050.08,-619.5"/>
</g>
<!-- HistoryImportQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge8" class="edge">
<title>HistoryImportQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5720.88,-567.25C5720.88,-615.54 5720.88,-743 5720.88,-743 5720.88,-743 6121.17,-743 6121.17,-743"/>
<polygon fill="black" stroke="black" points="6121.17,-746.5 6131.17,-743 6121.17,-739.5 6121.17,-746.5"/>
</g>
<!-- HistoryImportQueryRepository  -->
<g id="node79" class="node">
<title>HistoryImportQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="7031.01,-643 6834.99,-643 6834.99,-607 7031.01,-607 7031.01,-643"/>
<text text-anchor="middle" x="6933" y="-620.8" font-family="Times,serif" font-size="14.00">HistoryImportQueryRepository </text>
</g>
<!-- HistoryImportQuerySideModule&#45;&gt;HistoryImportQueryRepository  -->
<g id="edge124" class="edge">
<title>HistoryImportQuerySideModule&#45;&gt;HistoryImportQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5596.18,-567.04C5596.18,-579.56 5596.18,-594 5596.18,-594 5596.18,-594 6882.56,-594 6882.56,-594 6882.56,-594 6882.56,-596.97 6882.56,-596.97"/>
<polygon fill="black" stroke="black" points="6879.06,-596.97 6882.56,-606.97 6886.06,-596.97 6879.06,-596.97"/>
</g>
<!-- HistoryImportQueryService  -->
<g id="node80" class="node">
<title>HistoryImportQueryService </title>
<polygon fill="#fb8072" stroke="black" points="7447.06,-643 7270.94,-643 7270.94,-607 7447.06,-607 7447.06,-643"/>
<text text-anchor="middle" x="7359" y="-620.8" font-family="Times,serif" font-size="14.00">HistoryImportQueryService </text>
</g>
<!-- HistoryImportQuerySideModule&#45;&gt;HistoryImportQueryService  -->
<g id="edge125" class="edge">
<title>HistoryImportQuerySideModule&#45;&gt;HistoryImportQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5646.06,-567.24C5646.06,-574.43 5646.06,-581 5646.06,-581 5646.06,-581 7276.17,-581 7276.17,-581 7276.17,-581 7276.17,-596.85 7276.17,-596.85"/>
<polygon fill="black" stroke="black" points="7272.67,-596.85 7276.17,-606.85 7279.67,-596.85 7272.67,-596.85"/>
</g>
<!-- HistoryImportQuerySideModule  -->
<g id="node81" class="node">
<title>HistoryImportQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="7252.51,-643 7049.49,-643 7049.49,-607 7252.51,-607 7252.51,-643"/>
<text text-anchor="middle" x="7151" y="-620.8" font-family="Times,serif" font-size="14.00">HistoryImportQuerySideModule </text>
</g>
<!-- HistoryImportQuerySideModule&#45;&gt;HistoryImportQuerySideModule  -->
<g id="edge126" class="edge">
<title>HistoryImportQuerySideModule&#45;&gt;HistoryImportQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5621.12,-567.11C5621.12,-576.9 5621.12,-587 5621.12,-587 5621.12,-587 7072.69,-587 7072.69,-587 7072.69,-587 7072.69,-596.89 7072.69,-596.89"/>
<polygon fill="black" stroke="black" points="7069.19,-596.89 7072.69,-606.89 7076.19,-596.89 7069.19,-596.89"/>
</g>
<!-- LiquidationDomainModule -->
<g id="node9" class="node">
<title>LiquidationDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5019.98,-708 5016.98,-712 4995.98,-712 4992.98,-708 4850.02,-708 4850.02,-672 5019.98,-672 5019.98,-708"/>
<text text-anchor="middle" x="4935" y="-685.8" font-family="Times,serif" font-size="14.00">LiquidationDomainModule</text>
</g>
<!-- LiquidationDomainModule&#45;&gt;ListenerModule -->
<g id="edge156" class="edge">
<title>LiquidationDomainModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5020.29,-704C5050.61,-704 5077.37,-704 5077.37,-704 5077.37,-704 5077.37,-726.88 5077.37,-726.88"/>
<polygon fill="black" stroke="black" points="5073.87,-726.88 5077.37,-736.88 5080.87,-726.88 5073.87,-726.88"/>
</g>
<!-- LiquidationDomainModule&#45;&gt;ApplicationModule -->
<g id="edge9" class="edge">
<title>LiquidationDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5020.09,-699C5295.55,-699 6145.14,-699 6145.14,-699 6145.14,-699 6145.14,-726.97 6145.14,-726.97"/>
<polygon fill="black" stroke="black" points="6141.64,-726.97 6145.14,-736.97 6148.64,-726.97 6141.64,-726.97"/>
</g>
<!-- LiquidationDomainService  -->
<g id="node84" class="node">
<title>LiquidationDomainService </title>
<polygon fill="#fb8072" stroke="black" points="4559.91,-643 4388.09,-643 4388.09,-607 4559.91,-607 4559.91,-643"/>
<text text-anchor="middle" x="4474" y="-620.8" font-family="Times,serif" font-size="14.00">LiquidationDomainService </text>
</g>
<!-- LiquidationDomainModule&#45;&gt;LiquidationDomainService  -->
<g id="edge137" class="edge">
<title>LiquidationDomainModule&#45;&gt;LiquidationDomainService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4849.71,-690C4831.07,-690 4816.78,-690 4816.78,-690 4816.78,-690 4816.78,-631 4816.78,-631 4816.78,-631 4570.06,-631 4570.06,-631"/>
<polygon fill="black" stroke="black" points="4570.06,-627.5 4560.06,-631 4570.06,-634.5 4570.06,-627.5"/>
</g>
<!-- LiquidationQuerySideModule&#45;&gt;LiquidationDomainModule -->
<g id="edge131" class="edge">
<title>LiquidationQuerySideModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M6115.41,-330C6120.31,-330 6123.28,-330 6123.28,-330 6123.28,-330 6123.28,-695 6123.28,-695 6123.28,-695 5030.46,-695 5030.46,-695"/>
<polygon fill="black" stroke="black" points="5030.46,-691.5 5020.46,-695 5030.46,-698.5 5030.46,-691.5"/>
</g>
<!-- LiquidationQuerySideModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge227" class="edge">
<title>LiquidationQuerySideModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5935.66,-339.11C5935.66,-348.9 5935.66,-359 5935.66,-359 5935.66,-359 4994.67,-359 4994.67,-359 4994.67,-359 4994.67,-596.72 4994.67,-596.72"/>
<polygon fill="black" stroke="black" points="4991.17,-596.72 4994.67,-606.72 4998.17,-596.72 4991.17,-596.72"/>
</g>
<!-- LiquidationQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge10" class="edge">
<title>LiquidationQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M6115.47,-333C6167.42,-333 6221.92,-333 6221.92,-333 6221.92,-333 6221.92,-726.87 6221.92,-726.87"/>
<polygon fill="black" stroke="black" points="6218.42,-726.87 6221.92,-736.87 6225.42,-726.87 6218.42,-726.87"/>
</g>
<!-- LiquidationQueryRepository  -->
<g id="node88" class="node">
<title>LiquidationQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="10627.75,-415 10446.25,-415 10446.25,-379 10627.75,-379 10627.75,-415"/>
<text text-anchor="middle" x="10537" y="-392.8" font-family="Times,serif" font-size="14.00">LiquidationQueryRepository </text>
</g>
<!-- LiquidationQuerySideModule&#45;&gt;LiquidationQueryRepository  -->
<g id="edge146" class="edge">
<title>LiquidationQuerySideModule&#45;&gt;LiquidationQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6115.47,-317C6758.45,-317 10537,-317 10537,-317 10537,-317 10537,-368.85 10537,-368.85"/>
<polygon fill="black" stroke="black" points="10533.5,-368.85 10537,-378.85 10540.5,-368.85 10533.5,-368.85"/>
</g>
<!-- LiquidationQueryService  -->
<g id="node89" class="node">
<title>LiquidationQueryService </title>
<polygon fill="#fb8072" stroke="black" points="11013.79,-415 10852.21,-415 10852.21,-379 11013.79,-379 11013.79,-415"/>
<text text-anchor="middle" x="10933" y="-392.8" font-family="Times,serif" font-size="14.00">LiquidationQueryService </text>
</g>
<!-- LiquidationQuerySideModule&#45;&gt;LiquidationQueryService  -->
<g id="edge147" class="edge">
<title>LiquidationQuerySideModule&#45;&gt;LiquidationQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6115.35,-310C6790,-310 10933,-310 10933,-310 10933,-310 10933,-368.97 10933,-368.97"/>
<polygon fill="black" stroke="black" points="10929.5,-368.97 10933,-378.97 10936.5,-368.97 10929.5,-368.97"/>
</g>
<!-- LiquidationQuerySideModule  -->
<g id="node90" class="node">
<title>LiquidationQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="10834.25,-415 10645.75,-415 10645.75,-379 10834.25,-379 10834.25,-415"/>
<text text-anchor="middle" x="10740" y="-392.8" font-family="Times,serif" font-size="14.00">LiquidationQuerySideModule </text>
</g>
<!-- LiquidationQuerySideModule&#45;&gt;LiquidationQuerySideModule  -->
<g id="edge148" class="edge">
<title>LiquidationQuerySideModule&#45;&gt;LiquidationQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6115.37,-313C6774.65,-313 10740,-313 10740,-313 10740,-313 10740,-368.77 10740,-368.77"/>
<polygon fill="black" stroke="black" points="10736.5,-368.77 10740,-378.77 10743.5,-368.77 10736.5,-368.77"/>
</g>
<!-- ListenerModule&#45;&gt;ApplicationModule -->
<g id="edge11" class="edge">
<title>ListenerModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5162.07,-771C5162.07,-771 6121.09,-771 6121.09,-771"/>
<polygon fill="black" stroke="black" points="6121.09,-774.5 6131.09,-771 6121.09,-767.5 6121.09,-774.5"/>
</g>
<!-- LoggerModule -->
<g id="node12" class="node">
<title>LoggerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5508.98,-187 5505.98,-191 5484.98,-191 5481.98,-187 5409.02,-187 5409.02,-151 5508.98,-151 5508.98,-187"/>
<text text-anchor="middle" x="5459" y="-164.8" font-family="Times,serif" font-size="14.00">LoggerModule</text>
</g>
<!-- LoggerModule&#45;&gt;AuthModule -->
<g id="edge25" class="edge">
<title>LoggerModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M5408.89,-176C5319.72,-176 5142.76,-176 5142.76,-176 5142.76,-176 5142.76,-216.87 5142.76,-216.87"/>
<polygon fill="black" stroke="black" points="5139.26,-216.87 5142.76,-226.87 5146.26,-216.87 5139.26,-216.87"/>
</g>
<!-- LoggerModule&#45;&gt;DiscountDomainModule -->
<g id="edge41" class="edge">
<title>LoggerModule&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M5509.22,-174C5629.48,-174 5924.11,-174 5924.11,-174 5924.11,-174 5924.11,-444.75 5924.11,-444.75"/>
<polygon fill="black" stroke="black" points="5920.61,-444.75 5924.11,-454.75 5927.61,-444.75 5920.61,-444.75"/>
</g>
<!-- LoggerModule&#45;&gt;DiscountQuerySideModule -->
<g id="edge49" class="edge">
<title>LoggerModule&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M5408.79,-168C5256.26,-168 4808.74,-168 4808.74,-168 4808.74,-168 4808.74,-368.65 4808.74,-368.65"/>
<polygon fill="black" stroke="black" points="4805.24,-368.65 4808.74,-378.65 4812.24,-368.65 4805.24,-368.65"/>
</g>
<!-- LoggerModule&#45;&gt;HandoverDomainModule -->
<g id="edge69" class="edge">
<title>LoggerModule&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M5509.08,-185C5522.55,-185 5533.46,-185 5533.46,-185 5533.46,-185 5533.46,-520.95 5533.46,-520.95"/>
<polygon fill="black" stroke="black" points="5529.96,-520.95 5533.46,-530.95 5536.96,-520.95 5529.96,-520.95"/>
</g>
<!-- LoggerModule&#45;&gt;HandoverQuerySideModule -->
<g id="edge77" class="edge">
<title>LoggerModule&#45;&gt;HandoverQuerySideModule</title>
<path fill="none" stroke="black" d="M5509.3,-172C5630.54,-172 5929,-172 5929,-172 5929,-172 5929,-391 5929,-391 5929,-391 5930.74,-391 5930.74,-391"/>
<polygon fill="black" stroke="black" points="5930.74,-394.5 5940.74,-391 5930.74,-387.5 5930.74,-394.5"/>
</g>
<!-- LoggerModule&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge106" class="edge">
<title>LoggerModule&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M5408.91,-182C5358.76,-182 5288.98,-182 5288.98,-182 5288.98,-182 5288.98,-520.8 5288.98,-520.8"/>
<polygon fill="black" stroke="black" points="5285.48,-520.8 5288.98,-530.8 5292.48,-520.8 5285.48,-520.8"/>
</g>
<!-- LoggerModule&#45;&gt;HandoverScheduleQuerySideModule -->
<g id="edge114" class="edge">
<title>LoggerModule&#45;&gt;HandoverScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M5509.13,-176C5595.36,-176 5762.59,-176 5762.59,-176 5762.59,-176 5762.59,-292.93 5762.59,-292.93"/>
<polygon fill="black" stroke="black" points="5759.09,-292.93 5762.59,-302.93 5766.09,-292.93 5759.09,-292.93"/>
</g>
<!-- LoggerModule&#45;&gt;LiquidationDomainModule -->
<g id="edge132" class="edge">
<title>LoggerModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M5509.03,-183C5535.3,-183 5562.08,-183 5562.08,-183 5562.08,-183 5562.08,-681 5562.08,-681 5562.08,-681 5030.22,-681 5030.22,-681"/>
<polygon fill="black" stroke="black" points="5030.22,-677.5 5020.22,-681 5030.22,-684.5 5030.22,-677.5"/>
</g>
<!-- LoggerModule&#45;&gt;LiquidationQuerySideModule -->
<g id="edge143" class="edge">
<title>LoggerModule&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M5509.2,-169C5652.6,-169 6053.79,-169 6053.79,-169 6053.79,-169 6053.79,-292.97 6053.79,-292.97"/>
<polygon fill="black" stroke="black" points="6050.29,-292.97 6053.79,-302.97 6057.29,-292.97 6050.29,-292.97"/>
</g>
<!-- LoggerModule&#45;&gt;ListenerModule -->
<g id="edge157" class="edge">
<title>LoggerModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5509.27,-181C5536.7,-181 5565.14,-181 5565.14,-181 5565.14,-181 5565.14,-762 5565.14,-762 5565.14,-762 5171.77,-762 5171.77,-762"/>
<polygon fill="black" stroke="black" points="5171.77,-758.5 5161.77,-762 5171.77,-765.5 5171.77,-758.5"/>
</g>
<!-- LoggerModule&#45;&gt;PolicyDomainModule -->
<g id="edge201" class="edge">
<title>LoggerModule&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M5408.68,-171C5273.68,-171 4913.7,-171 4913.7,-171 4913.7,-171 4913.7,-444.56 4913.7,-444.56"/>
<polygon fill="black" stroke="black" points="4910.2,-444.56 4913.7,-454.56 4917.2,-444.56 4910.2,-444.56"/>
</g>
<!-- LoggerModule&#45;&gt;PolicyQuerySideModule -->
<g id="edge213" class="edge">
<title>LoggerModule&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M5475.66,-187.03C5475.66,-187.03 5475.66,-292.99 5475.66,-292.99"/>
<polygon fill="black" stroke="black" points="5472.16,-292.99 5475.66,-302.99 5479.16,-292.99 5472.16,-292.99"/>
</g>
<!-- LoggerModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge228" class="edge">
<title>LoggerModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5408.61,-174C5285.55,-174 4979.7,-174 4979.7,-174 4979.7,-174 4979.7,-596.76 4979.7,-596.76"/>
<polygon fill="black" stroke="black" points="4976.2,-596.76 4979.7,-606.76 4983.2,-596.76 4976.2,-596.76"/>
</g>
<!-- LoggerModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge244" class="edge">
<title>LoggerModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M5408.89,-179C5328.26,-179 5178.69,-179 5178.69,-179 5178.69,-179 5178.69,-444.61 5178.69,-444.61"/>
<polygon fill="black" stroke="black" points="5175.19,-444.61 5178.69,-454.61 5182.19,-444.61 5175.19,-444.61"/>
</g>
<!-- ProposalDomainModule -->
<g id="node18" class="node">
<title>ProposalDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4811.65,-708 4808.65,-712 4787.65,-712 4784.65,-708 4658.35,-708 4658.35,-672 4811.65,-672 4811.65,-708"/>
<text text-anchor="middle" x="4735" y="-685.8" font-family="Times,serif" font-size="14.00">ProposalDomainModule</text>
</g>
<!-- LoggerModule&#45;&gt;ProposalDomainModule -->
<g id="edge259" class="edge">
<title>LoggerModule&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M5408.73,-160C5231.36,-160 4645.5,-160 4645.5,-160 4645.5,-160 4645.5,-678 4645.5,-678 4645.5,-678 4648.32,-678 4648.32,-678"/>
<polygon fill="black" stroke="black" points="4648.32,-681.5 4658.32,-678 4648.32,-674.5 4648.32,-681.5"/>
</g>
<!-- LoggerModule&#45;&gt;ProposalQuerySideModule -->
<g id="edge270" class="edge">
<title>LoggerModule&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M5509.03,-178C5554.02,-178 5613.09,-178 5613.09,-178 5613.09,-178 5613.09,-441 5613.09,-441 5613.09,-441 5800.66,-441 5800.66,-441 5800.66,-441 5800.66,-520.68 5800.66,-520.68"/>
<polygon fill="black" stroke="black" points="5797.16,-520.68 5800.66,-530.68 5804.16,-520.68 5797.16,-520.68"/>
</g>
<!-- ScheduleDomainModule -->
<g id="node20" class="node">
<title>ScheduleDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4813.97,-491 4810.97,-495 4789.97,-495 4786.97,-491 4658.03,-491 4658.03,-455 4813.97,-455 4813.97,-491"/>
<text text-anchor="middle" x="4736" y="-468.8" font-family="Times,serif" font-size="14.00">ScheduleDomainModule</text>
</g>
<!-- LoggerModule&#45;&gt;ScheduleDomainModule -->
<g id="edge282" class="edge">
<title>LoggerModule&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M5408.78,-163C5233.04,-163 4656.23,-163 4656.23,-163 4656.23,-163 4656.23,-481 4656.23,-481 4656.23,-481 4656.39,-481 4656.39,-481"/>
<polygon fill="black" stroke="black" points="4647.89,-484.5 4657.89,-481 4647.89,-477.5 4647.89,-484.5"/>
</g>
<!-- LoggerModule&#45;&gt;ScheduleQuerySideModule -->
<g id="edge292" class="edge">
<title>LoggerModule&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M5408.85,-185C5385.89,-185 5363.72,-185 5363.72,-185 5363.72,-185 5363.72,-368.92 5363.72,-368.92"/>
<polygon fill="black" stroke="black" points="5360.22,-368.92 5363.72,-378.92 5367.22,-368.93 5360.22,-368.92"/>
</g>
<!-- LoggerModule&#45;&gt;StatushistoryModule -->
<g id="edge310" class="edge">
<title>LoggerModule&#45;&gt;StatushistoryModule</title>
<path fill="none" stroke="black" d="M5409,-165C5251.36,-165 4775.21,-165 4775.21,-165 4775.21,-165 4775.21,-292.8 4775.21,-292.8"/>
<polygon fill="black" stroke="black" points="4771.71,-292.8 4775.21,-302.8 4778.71,-292.8 4771.71,-292.8"/>
</g>
<!-- LoggerModule&#45;&gt;ApplicationModule -->
<g id="edge12" class="edge">
<title>LoggerModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5509.23,-167C5682.23,-167 6242.86,-167 6242.86,-167 6242.86,-167 6242.86,-726.76 6242.86,-726.76"/>
<polygon fill="black" stroke="black" points="6239.36,-726.76 6242.86,-736.76 6246.36,-726.76 6239.36,-726.76"/>
</g>
<!-- LoggerModule&#45;&gt;HandoverRequestDomainModule -->
<g id="edge88" class="edge">
<title>LoggerModule&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M5509.37,-160C6104.57,-160 11774.13,-160 11774.13,-160 11774.13,-160 11774.13,-473 11774.13,-473 11774.13,-473 11800.25,-473 11800.25,-473"/>
<polygon fill="black" stroke="black" points="11800.25,-476.5 11810.25,-473 11800.25,-469.5 11800.25,-476.5"/>
</g>
<!-- LoggerModule&#45;&gt;HandoverRequestQuerySideModule -->
<g id="edge94" class="edge">
<title>LoggerModule&#45;&gt;HandoverRequestQuerySideModule</title>
<path fill="none" stroke="black" d="M5509.03,-165C6082.93,-165 11401.29,-165 11401.29,-165 11401.29,-165 11401.29,-292.8 11401.29,-292.8"/>
<polygon fill="black" stroke="black" points="11397.79,-292.8 11401.29,-302.8 11404.79,-292.8 11397.79,-292.8"/>
</g>
<!-- LoggerModule  -->
<g id="node94" class="node">
<title>LoggerModule </title>
<polygon fill="#fb8072" stroke="black" points="12235.98,-263 12132.02,-263 12132.02,-227 12235.98,-227 12235.98,-263"/>
<text text-anchor="middle" x="12184" y="-240.8" font-family="Times,serif" font-size="14.00">LoggerModule </text>
</g>
<!-- LoggerModule&#45;&gt;LoggerModule  -->
<g id="edge165" class="edge">
<title>LoggerModule&#45;&gt;LoggerModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5509.26,-154C6124.67,-154 12184,-154 12184,-154 12184,-154 12184,-216.58 12184,-216.58"/>
<polygon fill="black" stroke="black" points="12180.5,-216.58 12184,-226.58 12187.5,-216.58 12180.5,-216.58"/>
</g>
<!-- MsxLoggerService  -->
<g id="node95" class="node">
<title>MsxLoggerService </title>
<polygon fill="#fb8072" stroke="black" points="12114.29,-263 11987.71,-263 11987.71,-227 12114.29,-227 12114.29,-263"/>
<text text-anchor="middle" x="12051" y="-240.8" font-family="Times,serif" font-size="14.00">MsxLoggerService </text>
</g>
<!-- LoggerModule&#45;&gt;MsxLoggerService  -->
<g id="edge166" class="edge">
<title>LoggerModule&#45;&gt;MsxLoggerService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5509.21,-156C6117.61,-156 12051,-156 12051,-156 12051,-156 12051,-216.99 12051,-216.99"/>
<polygon fill="black" stroke="black" points="12047.5,-216.99 12051,-226.99 12054.5,-216.99 12047.5,-216.99"/>
</g>
<!-- LoggerModule&#45;&gt;StatushistoryExportModule -->
<g id="edge303" class="edge">
<title>LoggerModule&#45;&gt;StatushistoryExportModule</title>
<path fill="none" stroke="black" d="M5509.42,-163C6102.2,-163 11722.61,-163 11722.61,-163 11722.61,-163 11722.61,-321 11722.61,-321 11722.61,-321 12693.54,-321 12693.54,-321"/>
<polygon fill="black" stroke="black" points="12693.54,-324.5 12703.54,-321 12693.54,-317.5 12693.54,-324.5"/>
</g>
<!-- MgsSenderModule -->
<g id="node13" class="node">
<title>MgsSenderModule</title>
<polygon fill="#8dd3c7" stroke="black" points="4761.81,-122 4758.81,-126 4737.81,-126 4734.81,-122 4638.19,-122 4638.19,-86 4761.81,-86 4761.81,-122"/>
<text text-anchor="middle" x="4700" y="-99.8" font-family="Times,serif" font-size="14.00">MgsSenderModule</text>
</g>
<!-- MgsSenderModule&#45;&gt;AuthModule -->
<g id="edge26" class="edge">
<title>MgsSenderModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M4762.1,-116C4881.25,-116 5130.25,-116 5130.25,-116 5130.25,-116 5130.25,-216.79 5130.25,-216.79"/>
<polygon fill="black" stroke="black" points="5126.75,-216.79 5130.25,-226.79 5133.75,-216.79 5126.75,-216.79"/>
</g>
<!-- MgsSenderModule&#45;&gt;DiscountDomainModule -->
<g id="edge42" class="edge">
<title>MgsSenderModule&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M4762.05,-100C5011.22,-100 5925.74,-100 5925.74,-100 5925.74,-100 5925.74,-444.76 5925.74,-444.76"/>
<polygon fill="black" stroke="black" points="5922.24,-444.76 5925.74,-454.76 5929.24,-444.76 5922.24,-444.76"/>
</g>
<!-- MgsSenderModule&#45;&gt;DiscountQuerySideModule -->
<g id="edge50" class="edge">
<title>MgsSenderModule&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M4665.01,-122.33C4665.01,-122.33 4665.01,-368.68 4665.01,-368.68"/>
<polygon fill="black" stroke="black" points="4661.51,-368.68 4665.01,-378.68 4668.51,-368.68 4661.51,-368.68"/>
</g>
<!-- MgsSenderModule&#45;&gt;HandoverDomainModule -->
<g id="edge70" class="edge">
<title>MgsSenderModule&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M4761.98,-107C4955.93,-107 5538.33,-107 5538.33,-107 5538.33,-107 5538.33,-520.85 5538.33,-520.85"/>
<polygon fill="black" stroke="black" points="5534.83,-520.85 5538.33,-530.85 5541.83,-520.85 5534.83,-520.85"/>
</g>
<!-- MgsSenderModule&#45;&gt;HandoverQuerySideModule -->
<g id="edge78" class="edge">
<title>MgsSenderModule&#45;&gt;HandoverQuerySideModule</title>
<path fill="none" stroke="black" d="M4667.34,-122.01C4667.34,-181.2 4667.34,-366 4667.34,-366 4667.34,-366 6028,-366 6028,-366 6028,-366 6028,-368.97 6028,-368.97"/>
<polygon fill="black" stroke="black" points="6024.5,-368.97 6028,-378.97 6031.5,-368.97 6024.5,-368.97"/>
</g>
<!-- MgsSenderModule&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge107" class="edge">
<title>MgsSenderModule&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M4761.79,-113C4909.78,-113 5272.32,-113 5272.32,-113 5272.32,-113 5272.32,-520.82 5272.32,-520.82"/>
<polygon fill="black" stroke="black" points="5268.82,-520.82 5272.32,-530.82 5275.82,-520.82 5268.82,-520.82"/>
</g>
<!-- MgsSenderModule&#45;&gt;HandoverScheduleQuerySideModule -->
<g id="edge115" class="edge">
<title>MgsSenderModule&#45;&gt;HandoverScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M4761.93,-104C4995.22,-104 5807.78,-104 5807.78,-104 5807.78,-104 5807.78,-292.87 5807.78,-292.87"/>
<polygon fill="black" stroke="black" points="5804.28,-292.87 5807.78,-302.87 5811.28,-292.87 5804.28,-292.87"/>
</g>
<!-- MgsSenderModule&#45;&gt;LiquidationDomainModule -->
<g id="edge133" class="edge">
<title>MgsSenderModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M4647.29,-122.13C4647.29,-216.7 4647.29,-649 4647.29,-649 4647.29,-649 4963.33,-649 4963.33,-649 4963.33,-649 4963.33,-661.9 4963.33,-661.9"/>
<polygon fill="black" stroke="black" points="4959.83,-661.9 4963.33,-671.9 4966.83,-661.9 4959.83,-661.9"/>
</g>
<!-- MgsSenderModule&#45;&gt;LiquidationQuerySideModule -->
<g id="edge144" class="edge">
<title>MgsSenderModule&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M4761.73,-98C5030.42,-98 6084.58,-98 6084.58,-98 6084.58,-98 6084.58,-292.75 6084.58,-292.75"/>
<polygon fill="black" stroke="black" points="6081.08,-292.75 6084.58,-302.75 6088.08,-292.75 6081.08,-292.75"/>
</g>
<!-- MgsSenderModule&#45;&gt;LoggerModule -->
<g id="edge164" class="edge">
<title>MgsSenderModule&#45;&gt;LoggerModule</title>
<path fill="none" stroke="black" d="M4738.66,-122.05C4738.66,-136.37 4738.66,-154 4738.66,-154 4738.66,-154 5398.92,-154 5398.92,-154"/>
<polygon fill="black" stroke="black" points="5398.92,-157.5 5408.92,-154 5398.92,-150.5 5398.92,-157.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;PolicyDomainModule -->
<g id="edge202" class="edge">
<title>MgsSenderModule&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M4761.7,-120C4821.11,-120 4902,-120 4902,-120 4902,-120 4902,-444.52 4902,-444.52"/>
<polygon fill="black" stroke="black" points="4898.5,-444.52 4902,-454.52 4905.5,-444.52 4898.5,-444.52"/>
</g>
<!-- MgsSenderModule&#45;&gt;PolicyQuerySideModule -->
<g id="edge214" class="edge">
<title>MgsSenderModule&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M4761.78,-109C4933.34,-109 5403.08,-109 5403.08,-109 5403.08,-109 5403.08,-292.92 5403.08,-292.92"/>
<polygon fill="black" stroke="black" points="5399.58,-292.92 5403.08,-302.92 5406.58,-292.93 5399.58,-292.92"/>
</g>
<!-- MgsSenderModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge229" class="edge">
<title>MgsSenderModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M4649.07,-122.03C4649.07,-213.18 4649.07,-619 4649.07,-619 4649.07,-619 4831.76,-619 4831.76,-619"/>
<polygon fill="black" stroke="black" points="4831.76,-622.5 4841.76,-619 4831.76,-615.5 4831.76,-622.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge245" class="edge">
<title>MgsSenderModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M4761.67,-118C4866.56,-118 5068.46,-118 5068.46,-118 5068.46,-118 5068.46,-444.91 5068.46,-444.91"/>
<polygon fill="black" stroke="black" points="5064.96,-444.91 5068.46,-454.91 5071.96,-444.91 5064.96,-444.91"/>
</g>
<!-- MgsSenderModule&#45;&gt;ProposalDomainModule -->
<g id="edge260" class="edge">
<title>MgsSenderModule&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M4643.71,-122.28C4643.71,-220.7 4643.71,-683 4643.71,-683 4643.71,-683 4647.97,-683 4647.97,-683"/>
<polygon fill="black" stroke="black" points="4647.97,-686.5 4657.97,-683 4647.97,-679.5 4647.97,-686.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;ProposalQuerySideModule -->
<g id="edge271" class="edge">
<title>MgsSenderModule&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M4650.86,-122.06C4650.86,-201.74 4650.86,-518 4650.86,-518 4650.86,-518 5793.54,-518 5793.54,-518 5793.54,-518 5793.54,-520.97 5793.54,-520.97"/>
<polygon fill="black" stroke="black" points="5790.04,-520.97 5793.54,-530.97 5797.04,-520.97 5790.04,-520.97"/>
</g>
<!-- MgsSenderModule&#45;&gt;ScheduleDomainModule -->
<g id="edge283" class="edge">
<title>MgsSenderModule&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M4654.44,-122.17C4654.44,-198.08 4654.44,-486 4654.44,-486 4654.44,-486 4654.79,-486 4654.79,-486"/>
<polygon fill="black" stroke="black" points="4647.94,-489.5 4657.94,-486 4647.94,-482.5 4647.94,-489.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;ScheduleQuerySideModule -->
<g id="edge293" class="edge">
<title>MgsSenderModule&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M4761.82,-111C4924.86,-111 5354.04,-111 5354.04,-111 5354.04,-111 5354.04,-368.67 5354.04,-368.67"/>
<polygon fill="black" stroke="black" points="5350.54,-368.67 5354.04,-378.67 5357.54,-368.67 5350.54,-368.67"/>
</g>
<!-- MgsSenderModule&#45;&gt;StatushistoryModule -->
<g id="edge311" class="edge">
<title>MgsSenderModule&#45;&gt;StatushistoryModule</title>
<path fill="none" stroke="black" d="M4727.16,-122.13C4727.16,-122.13 4727.16,-292.77 4727.16,-292.77"/>
<polygon fill="black" stroke="black" points="4723.66,-292.77 4727.16,-302.77 4730.66,-292.77 4723.66,-292.77"/>
</g>
<!-- TransferHistoryModule -->
<g id="node23" class="node">
<title>TransferHistoryModule</title>
<polygon fill="#8dd3c7" stroke="black" points="5922.45,-415 5919.45,-419 5898.45,-419 5895.45,-415 5773.55,-415 5773.55,-379 5922.45,-379 5922.45,-415"/>
<text text-anchor="middle" x="5848" y="-392.8" font-family="Times,serif" font-size="14.00">TransferHistoryModule</text>
</g>
<!-- MgsSenderModule&#45;&gt;TransferHistoryModule -->
<g id="edge317" class="edge">
<title>MgsSenderModule&#45;&gt;TransferHistoryModule</title>
<path fill="none" stroke="black" d="M4761.95,-102C5007.54,-102 5899.31,-102 5899.31,-102 5899.31,-102 5899.31,-368.99 5899.31,-368.99"/>
<polygon fill="black" stroke="black" points="5895.81,-368.99 5899.31,-378.99 5902.81,-368.99 5895.81,-368.99"/>
</g>
<!-- MgsSenderModule&#45;&gt;ApplicationModule -->
<g id="edge13" class="edge">
<title>MgsSenderModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M4761.71,-95C5049.96,-95 6249.84,-95 6249.84,-95 6249.84,-95 6249.84,-726.66 6249.84,-726.66"/>
<polygon fill="black" stroke="black" points="6246.34,-726.66 6249.84,-736.66 6253.34,-726.66 6246.34,-726.66"/>
</g>
<!-- MgsSenderModule&#45;&gt;EmployeeQuerySideModule -->
<g id="edge59" class="edge">
<title>MgsSenderModule&#45;&gt;EmployeeQuerySideModule</title>
<path fill="none" stroke="black" d="M4641.92,-122.19C4641.92,-172.36 4641.92,-309 4641.92,-309 4641.92,-309 4542.25,-309 4542.25,-309"/>
<polygon fill="black" stroke="black" points="4542.25,-305.5 4532.25,-309 4542.25,-312.5 4542.25,-305.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;HandoverRequestDomainModule -->
<g id="edge89" class="edge">
<title>MgsSenderModule&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M4761.78,-89C5456.9,-89 11784,-89 11784,-89 11784,-89 11784,-464 11784,-464 11784,-464 11800.42,-464 11800.42,-464"/>
<polygon fill="black" stroke="black" points="11800.42,-467.5 11810.42,-464 11800.42,-460.5 11800.42,-467.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;HandoverRequestQuerySideModule -->
<g id="edge95" class="edge">
<title>MgsSenderModule&#45;&gt;HandoverRequestQuerySideModule</title>
<path fill="none" stroke="black" d="M4761.85,-93C5436.75,-93 11407.28,-93 11407.28,-93 11407.28,-93 11407.28,-292.73 11407.28,-292.73"/>
<polygon fill="black" stroke="black" points="11403.78,-292.73 11407.28,-302.73 11410.78,-292.73 11403.78,-292.73"/>
</g>
<!-- CareClient  -->
<g id="node97" class="node">
<title>CareClient </title>
<polygon fill="#fb8072" stroke="black" points="2916.15,-187 2835.85,-187 2835.85,-151 2916.15,-151 2916.15,-187"/>
<text text-anchor="middle" x="2876" y="-164.8" font-family="Times,serif" font-size="14.00">CareClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;CareClient  -->
<g id="edge168" class="edge">
<title>MgsSenderModule&#45;&gt;CareClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.27,-106C4319.65,-106 2876,-106 2876,-106 2876,-106 2876,-140.78 2876,-140.78"/>
<polygon fill="black" stroke="black" points="2872.5,-140.78 2876,-150.78 2879.5,-140.78 2872.5,-140.78"/>
</g>
<!-- CustomerClient  -->
<g id="node98" class="node">
<title>CustomerClient </title>
<polygon fill="#fb8072" stroke="black" points="4598.16,-187 4489.84,-187 4489.84,-151 4598.16,-151 4598.16,-187"/>
<text text-anchor="middle" x="4544" y="-164.8" font-family="Times,serif" font-size="14.00">CustomerClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;CustomerClient  -->
<g id="edge169" class="edge">
<title>MgsSenderModule&#45;&gt;CustomerClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4640.13,-122.24C4640.13,-137.57 4640.13,-157 4640.13,-157 4640.13,-157 4608.25,-157 4608.25,-157"/>
<polygon fill="black" stroke="black" points="4608.25,-153.5 4598.25,-157 4608.25,-160.5 4608.25,-153.5"/>
</g>
<!-- EmployeeClient  -->
<g id="node99" class="node">
<title>EmployeeClient </title>
<polygon fill="#fb8072" stroke="black" points="4472.49,-187 4361.51,-187 4361.51,-151 4472.49,-151 4472.49,-187"/>
<text text-anchor="middle" x="4417" y="-164.8" font-family="Times,serif" font-size="14.00">EmployeeClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;EmployeeClient  -->
<g id="edge170" class="edge">
<title>MgsSenderModule&#45;&gt;EmployeeClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.24,-121C4554.97,-121 4417,-121 4417,-121 4417,-121 4417,-140.85 4417,-140.85"/>
<polygon fill="black" stroke="black" points="4413.5,-140.85 4417,-150.85 4420.5,-140.85 4413.5,-140.85"/>
</g>
<!-- LoggerClient  -->
<g id="node100" class="node">
<title>LoggerClient </title>
<polygon fill="#fb8072" stroke="black" points="4344.15,-187 4249.85,-187 4249.85,-151 4344.15,-151 4344.15,-187"/>
<text text-anchor="middle" x="4297" y="-164.8" font-family="Times,serif" font-size="14.00">LoggerClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;LoggerClient  -->
<g id="edge171" class="edge">
<title>MgsSenderModule&#45;&gt;LoggerClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4637.91,-120C4524.87,-120 4297,-120 4297,-120 4297,-120 4297,-140.75 4297,-140.75"/>
<polygon fill="black" stroke="black" points="4293.5,-140.75 4297,-150.75 4300.5,-140.75 4293.5,-140.75"/>
</g>
<!-- MailerClient  -->
<g id="node101" class="node">
<title>MailerClient </title>
<polygon fill="#fb8072" stroke="black" points="4231.54,-187 4140.46,-187 4140.46,-151 4231.54,-151 4231.54,-187"/>
<text text-anchor="middle" x="4186" y="-164.8" font-family="Times,serif" font-size="14.00">MailerClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;MailerClient  -->
<g id="edge172" class="edge">
<title>MgsSenderModule&#45;&gt;MailerClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.26,-119C4501.68,-119 4186,-119 4186,-119 4186,-119 4186,-140.95 4186,-140.95"/>
<polygon fill="black" stroke="black" points="4182.5,-140.95 4186,-150.95 4189.5,-140.95 4182.5,-140.95"/>
</g>
<!-- MsgSenderService  -->
<g id="node102" class="node">
<title>MsgSenderService </title>
<polygon fill="#fb8072" stroke="black" points="4122.74,-187 3997.26,-187 3997.26,-151 4122.74,-151 4122.74,-187"/>
<text text-anchor="middle" x="4060" y="-164.8" font-family="Times,serif" font-size="14.00">MsgSenderService </text>
</g>
<!-- MgsSenderModule&#45;&gt;MsgSenderService  -->
<g id="edge173" class="edge">
<title>MgsSenderModule&#45;&gt;MsgSenderService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.34,-118C4477.94,-118 4060,-118 4060,-118 4060,-118 4060,-140.88 4060,-140.88"/>
<polygon fill="black" stroke="black" points="4056.5,-140.88 4060,-150.88 4063.5,-140.88 4056.5,-140.88"/>
</g>
<!-- NotificationClient  -->
<g id="node103" class="node">
<title>NotificationClient </title>
<polygon fill="#fb8072" stroke="black" points="3979.87,-187 3858.13,-187 3858.13,-151 3979.87,-151 3979.87,-187"/>
<text text-anchor="middle" x="3919" y="-164.8" font-family="Times,serif" font-size="14.00">NotificationClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;NotificationClient  -->
<g id="edge174" class="edge">
<title>MgsSenderModule&#45;&gt;NotificationClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.06,-116C4453.34,-116 3919,-116 3919,-116 3919,-116 3919,-140.76 3919,-140.76"/>
<polygon fill="black" stroke="black" points="3915.5,-140.76 3919,-150.76 3922.5,-140.76 3915.5,-140.76"/>
</g>
<!-- NotifierClient  -->
<g id="node104" class="node">
<title>NotifierClient </title>
<polygon fill="#fb8072" stroke="black" points="3840.04,-187 3741.96,-187 3741.96,-151 3840.04,-151 3840.04,-187"/>
<text text-anchor="middle" x="3791" y="-164.8" font-family="Times,serif" font-size="14.00">NotifierClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;NotifierClient  -->
<g id="edge175" class="edge">
<title>MgsSenderModule&#45;&gt;NotifierClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.24,-115C4433.64,-115 3791,-115 3791,-115 3791,-115 3791,-140.72 3791,-140.72"/>
<polygon fill="black" stroke="black" points="3787.5,-140.72 3791,-150.72 3794.5,-140.72 3787.5,-140.72"/>
</g>
<!-- OrgchartClient  -->
<g id="node105" class="node">
<title>OrgchartClient </title>
<polygon fill="#fb8072" stroke="black" points="3723.97,-187 3620.03,-187 3620.03,-151 3723.97,-151 3723.97,-187"/>
<text text-anchor="middle" x="3672" y="-164.8" font-family="Times,serif" font-size="14.00">OrgchartClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;OrgchartClient  -->
<g id="edge176" class="edge">
<title>MgsSenderModule&#45;&gt;OrgchartClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.01,-114C4415.75,-114 3672,-114 3672,-114 3672,-114 3672,-140.99 3672,-140.99"/>
<polygon fill="black" stroke="black" points="3668.5,-140.99 3672,-150.99 3675.5,-140.99 3668.5,-140.99"/>
</g>
<!-- PropertyClient  -->
<g id="node106" class="node">
<title>PropertyClient </title>
<polygon fill="#fb8072" stroke="black" points="3601.93,-187 3500.07,-187 3500.07,-151 3601.93,-151 3601.93,-187"/>
<text text-anchor="middle" x="3551" y="-164.8" font-family="Times,serif" font-size="14.00">PropertyClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;PropertyClient  -->
<g id="edge177" class="edge">
<title>MgsSenderModule&#45;&gt;PropertyClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.22,-113C4399.57,-113 3551,-113 3551,-113 3551,-113 3551,-140.97 3551,-140.97"/>
<polygon fill="black" stroke="black" points="3547.5,-140.97 3551,-150.97 3554.5,-140.97 3547.5,-140.97"/>
</g>
<!-- SocialClient  -->
<g id="node107" class="node">
<title>SocialClient </title>
<polygon fill="#fb8072" stroke="black" points="3482.22,-187 3393.78,-187 3393.78,-151 3482.22,-151 3482.22,-187"/>
<text text-anchor="middle" x="3438" y="-164.8" font-family="Times,serif" font-size="14.00">SocialClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;SocialClient  -->
<g id="edge178" class="edge">
<title>MgsSenderModule&#45;&gt;SocialClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.28,-112C4384.91,-112 3438,-112 3438,-112 3438,-112 3438,-140.96 3438,-140.96"/>
<polygon fill="black" stroke="black" points="3434.5,-140.96 3438,-150.96 3441.5,-140.96 3434.5,-140.96"/>
</g>
<!-- StsClient  -->
<g id="node108" class="node">
<title>StsClient </title>
<polygon fill="#fb8072" stroke="black" points="3375.34,-187 3304.66,-187 3304.66,-151 3375.34,-151 3375.34,-187"/>
<text text-anchor="middle" x="3340" y="-164.8" font-family="Times,serif" font-size="14.00">StsClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;StsClient  -->
<g id="edge179" class="edge">
<title>MgsSenderModule&#45;&gt;StsClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.03,-110C4371.97,-110 3340,-110 3340,-110 3340,-110 3340,-140.98 3340,-140.98"/>
<polygon fill="black" stroke="black" points="3336.5,-140.98 3340,-150.98 3343.5,-140.98 3336.5,-140.98"/>
</g>
<!-- SyncErpClient  -->
<g id="node109" class="node">
<title>SyncErpClient </title>
<polygon fill="#fb8072" stroke="black" points="3286.93,-187 3185.07,-187 3185.07,-151 3286.93,-151 3286.93,-187"/>
<text text-anchor="middle" x="3236" y="-164.8" font-family="Times,serif" font-size="14.00">SyncErpClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;SyncErpClient  -->
<g id="edge180" class="edge">
<title>MgsSenderModule&#45;&gt;SyncErpClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.03,-109C4359.38,-109 3236,-109 3236,-109 3236,-109 3236,-141 3236,-141"/>
<polygon fill="black" stroke="black" points="3232.5,-141 3236,-151 3239.5,-141 3232.5,-141"/>
</g>
<!-- TransactionClient  -->
<g id="node110" class="node">
<title>TransactionClient </title>
<polygon fill="#fb8072" stroke="black" points="3166.81,-187 3047.19,-187 3047.19,-151 3166.81,-151 3166.81,-187"/>
<text text-anchor="middle" x="3107" y="-164.8" font-family="Times,serif" font-size="14.00">TransactionClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;TransactionClient  -->
<g id="edge181" class="edge">
<title>MgsSenderModule&#45;&gt;TransactionClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.04,-108C4344.4,-108 3107,-108 3107,-108 3107,-108 3107,-140.7 3107,-140.7"/>
<polygon fill="black" stroke="black" points="3103.5,-140.7 3107,-150.7 3110.5,-140.7 3103.5,-140.7"/>
</g>
<!-- UploadClient  -->
<g id="node111" class="node">
<title>UploadClient </title>
<polygon fill="#fb8072" stroke="black" points="3029.43,-187 2934.57,-187 2934.57,-151 3029.43,-151 3029.43,-187"/>
<text text-anchor="middle" x="2982" y="-164.8" font-family="Times,serif" font-size="14.00">UploadClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;UploadClient  -->
<g id="edge182" class="edge">
<title>MgsSenderModule&#45;&gt;UploadClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4638.09,-107C4330.58,-107 2982,-107 2982,-107 2982,-107 2982,-140.73 2982,-140.73"/>
<polygon fill="black" stroke="black" points="2978.5,-140.73 2982,-150.73 2985.5,-140.73 2978.5,-140.73"/>
</g>
<!-- MgsSenderModule&#45;&gt;StatushistoryExportModule -->
<g id="edge304" class="edge">
<title>MgsSenderModule&#45;&gt;StatushistoryExportModule</title>
<path fill="none" stroke="black" d="M4761.92,-91C5455.28,-91 11738.49,-91 11738.49,-91 11738.49,-91 11738.49,-312 11738.49,-312 11738.49,-312 12693.48,-312 12693.48,-312"/>
<polygon fill="black" stroke="black" points="12693.48,-315.5 12703.48,-312 12693.48,-308.5 12693.48,-315.5"/>
</g>
<!-- PolicyDomainModule&#45;&gt;ApplicationModule -->
<g id="edge14" class="edge">
<title>PolicyDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M4957.75,-491.21C4957.75,-496.55 4957.75,-501 4957.75,-501 4957.75,-501 6180.04,-501 6180.04,-501 6180.04,-501 6180.04,-726.73 6180.04,-726.73"/>
<polygon fill="black" stroke="black" points="6176.54,-726.73 6180.04,-736.73 6183.54,-726.73 6176.54,-726.73"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;DiscountDomainModule -->
<g id="edge43" class="edge">
<title>PolicyQuerySideModule&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M5524.58,-339.2C5524.58,-378.33 5524.58,-467 5524.58,-467 5524.58,-467 5795.23,-467 5795.23,-467"/>
<polygon fill="black" stroke="black" points="5795.23,-470.5 5805.23,-467 5795.23,-463.5 5795.23,-470.5"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;DiscountQuerySideModule -->
<g id="edge51" class="edge">
<title>PolicyQuerySideModule&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M5373.19,-311C5204.78,-311 4821.57,-311 4821.57,-311 4821.57,-311 4821.57,-368.76 4821.57,-368.76"/>
<polygon fill="black" stroke="black" points="4818.07,-368.76 4821.57,-378.76 4825.07,-368.76 4818.07,-368.76"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge158" class="edge">
<title>PolicyQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5504.49,-339.08C5504.49,-385.63 5504.49,-506 5504.49,-506 5504.49,-506 5119.54,-506 5119.54,-506 5119.54,-506 5119.54,-726.69 5119.54,-726.69"/>
<polygon fill="black" stroke="black" points="5116.04,-726.69 5119.54,-736.69 5123.04,-726.69 5116.04,-726.69"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;PolicyDomainModule -->
<g id="edge203" class="edge">
<title>PolicyQuerySideModule&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M5373.16,-318C5226.85,-318 4925.41,-318 4925.41,-318 4925.41,-318 4925.41,-444.92 4925.41,-444.92"/>
<polygon fill="black" stroke="black" points="4921.91,-444.92 4925.41,-454.92 4928.91,-444.92 4921.91,-444.92"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge230" class="edge">
<title>PolicyQuerySideModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5373.37,-325C5241.33,-325 4987.18,-325 4987.18,-325 4987.18,-325 4987.18,-596.69 4987.18,-596.69"/>
<polygon fill="black" stroke="black" points="4983.68,-596.69 4987.18,-606.69 4990.68,-596.69 4983.68,-596.69"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;ScheduleDomainModule -->
<g id="edge284" class="edge">
<title>PolicyQuerySideModule&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M5500.47,-339.16C5500.47,-371.1 5500.47,-434 5500.47,-434 5500.47,-434 4736,-434 4736,-434 4736,-434 4736,-444.87 4736,-444.87"/>
<polygon fill="black" stroke="black" points="4732.5,-444.87 4736,-454.87 4739.5,-444.87 4732.5,-444.87"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;ScheduleQuerySideModule -->
<g id="edge294" class="edge">
<title>PolicyQuerySideModule&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M5424.88,-339.01C5424.88,-339.01 5424.88,-368.85 5424.88,-368.85"/>
<polygon fill="black" stroke="black" points="5421.38,-368.85 5424.88,-378.85 5428.38,-368.85 5421.38,-368.85"/>
</g>
<!-- PolicyQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge15" class="edge">
<title>PolicyQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5528.65,-318C5545.83,-318 5559.03,-318 5559.03,-318 5559.03,-318 5559.03,-746 5559.03,-746 5559.03,-746 6121.09,-746 6121.09,-746"/>
<polygon fill="black" stroke="black" points="6121.09,-749.5 6131.09,-746 6121.09,-742.5 6121.09,-749.5"/>
</g>
<!-- PolicyQueryRepository  -->
<g id="node131" class="node">
<title>PolicyQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="9359.7,-415 9208.3,-415 9208.3,-379 9359.7,-379 9359.7,-415"/>
<text text-anchor="middle" x="9284" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyQueryRepository </text>
</g>
<!-- PolicyQuerySideModule&#45;&gt;PolicyQueryRepository  -->
<g id="edge216" class="edge">
<title>PolicyQuerySideModule&#45;&gt;PolicyQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5480.39,-339.17C5480.39,-343.05 5480.39,-346 5480.39,-346 5480.39,-346 9284,-346 9284,-346 9284,-346 9284,-368.88 9284,-368.88"/>
<polygon fill="black" stroke="black" points="9280.5,-368.88 9284,-378.88 9287.5,-368.88 9280.5,-368.88"/>
</g>
<!-- PolicyQueryService  -->
<g id="node132" class="node">
<title>PolicyQueryService </title>
<polygon fill="#fb8072" stroke="black" points="9686.24,-415 9553.76,-415 9553.76,-379 9686.24,-379 9686.24,-415"/>
<text text-anchor="middle" x="9620" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyQueryService </text>
</g>
<!-- PolicyQuerySideModule&#45;&gt;PolicyQueryService  -->
<g id="edge217" class="edge">
<title>PolicyQuerySideModule&#45;&gt;PolicyQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5488.42,-339.17C5488.42,-341.45 5488.42,-343 5488.42,-343 5488.42,-343 9620,-343 9620,-343 9620,-343 9620,-368.72 9620,-368.72"/>
<polygon fill="black" stroke="black" points="9616.5,-368.72 9620,-378.72 9623.5,-368.72 9616.5,-368.72"/>
</g>
<!-- PolicyQuerySideModule  -->
<g id="node133" class="node">
<title>PolicyQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="9536.2,-415 9377.8,-415 9377.8,-379 9536.2,-379 9536.2,-415"/>
<text text-anchor="middle" x="9457" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyQuerySideModule </text>
</g>
<!-- PolicyQuerySideModule&#45;&gt;PolicyQuerySideModule  -->
<g id="edge218" class="edge">
<title>PolicyQuerySideModule&#45;&gt;PolicyQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5484.4,-339.12C5484.4,-341.97 5484.4,-344 5484.4,-344 5484.4,-344 9457,-344 9457,-344 9457,-344 9457,-368.76 9457,-368.76"/>
<polygon fill="black" stroke="black" points="9453.5,-368.76 9457,-378.76 9460.5,-368.76 9453.5,-368.76"/>
</g>
<!-- PrimaryContractDomainModule&#45;&gt;LiquidationDomainModule -->
<g id="edge134" class="edge">
<title>PrimaryContractDomainModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M4991.66,-643.11C4991.66,-643.11 4991.66,-661.99 4991.66,-661.99"/>
<polygon fill="black" stroke="black" points="4988.16,-661.99 4991.66,-671.99 4995.16,-661.99 4988.16,-661.99"/>
</g>
<!-- PrimaryContractDomainModule&#45;&gt;ListenerModule -->
<g id="edge159" class="edge">
<title>PrimaryContractDomainModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5029.99,-643.11C5029.99,-676.87 5029.99,-746 5029.99,-746 5029.99,-746 5045.86,-746 5045.86,-746"/>
<polygon fill="black" stroke="black" points="5045.86,-749.5 5055.86,-746 5045.86,-742.5 5045.86,-749.5"/>
</g>
<!-- PrimaryContractDomainModule&#45;&gt;ProposalDomainModule -->
<g id="edge261" class="edge">
<title>PrimaryContractDomainModule&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M4846.01,-643.29C4846.01,-665.21 4846.01,-699 4846.01,-699 4846.01,-699 4821.8,-699 4821.8,-699"/>
<polygon fill="black" stroke="black" points="4821.8,-695.5 4811.8,-699 4821.8,-702.5 4821.8,-695.5"/>
</g>
<!-- PrimaryContractDomainModule&#45;&gt;ApplicationModule -->
<g id="edge16" class="edge">
<title>PrimaryContractDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5040.11,-638C5329.36,-638 6152.12,-638 6152.12,-638 6152.12,-638 6152.12,-726.98 6152.12,-726.98"/>
<polygon fill="black" stroke="black" points="6148.62,-726.98 6152.12,-736.98 6155.62,-726.98 6148.62,-726.98"/>
</g>
<!-- PrimaryContractCustomerDomainService  -->
<g id="node136" class="node">
<title>PrimaryContractCustomerDomainService </title>
<polygon fill="#fb8072" stroke="black" points="3162.33,-567 2907.67,-567 2907.67,-531 3162.33,-531 3162.33,-567"/>
<text text-anchor="middle" x="3035" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractCustomerDomainService </text>
</g>
<!-- PrimaryContractDomainModule&#45;&gt;PrimaryContractCustomerDomainService  -->
<g id="edge233" class="edge">
<title>PrimaryContractDomainModule&#45;&gt;PrimaryContractCustomerDomainService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4870.95,-606.94C4870.95,-579.13 4870.95,-529 4870.95,-529 4870.95,-529 3128.02,-529 3128.02,-529 3128.02,-529 3128.02,-529.18 3128.02,-529.18"/>
<polygon fill="black" stroke="black" points="3124.52,-520.85 3128.02,-530.85 3131.52,-520.85 3124.52,-520.85"/>
</g>
<!-- PrimaryContractDomainService  -->
<g id="node137" class="node">
<title>PrimaryContractDomainService </title>
<polygon fill="#fb8072" stroke="black" points="2889.89,-567 2690.11,-567 2690.11,-531 2889.89,-531 2889.89,-567"/>
<text text-anchor="middle" x="2790" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractDomainService </text>
</g>
<!-- PrimaryContractDomainModule&#45;&gt;PrimaryContractDomainService  -->
<g id="edge234" class="edge">
<title>PrimaryContractDomainModule&#45;&gt;PrimaryContractDomainService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4885.42,-606.76C4885.42,-578.66 4885.42,-528 4885.42,-528 4885.42,-528 2863.12,-528 2863.12,-528 2863.12,-528 2863.12,-528.29 2863.12,-528.29"/>
<polygon fill="black" stroke="black" points="2859.62,-520.92 2863.12,-530.92 2866.62,-520.92 2859.62,-520.92"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;HandoverDomainModule -->
<g id="edge71" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M5222.42,-483C5325.38,-483 5465.96,-483 5465.96,-483 5465.96,-483 5465.96,-520.97 5465.96,-520.97"/>
<polygon fill="black" stroke="black" points="5462.46,-520.97 5465.96,-530.97 5469.46,-520.97 5462.46,-520.97"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge108" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M5202.71,-491.01C5202.71,-491.01 5202.71,-520.85 5202.71,-520.85"/>
<polygon fill="black" stroke="black" points="5199.21,-520.85 5202.71,-530.85 5206.21,-520.85 5199.21,-520.85"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;LiquidationDomainModule -->
<g id="edge135" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M5048.14,-491.1C5048.14,-541.03 5048.14,-677 5048.14,-677 5048.14,-677 5030.17,-677 5030.17,-677"/>
<polygon fill="black" stroke="black" points="5030.17,-673.5 5020.17,-677 5030.17,-680.5 5030.17,-673.5"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge160" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5098.46,-491.07C5098.46,-491.07 5098.46,-726.72 5098.46,-726.72"/>
<polygon fill="black" stroke="black" points="5094.96,-726.72 5098.46,-736.72 5101.96,-726.72 5094.96,-726.72"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge231" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5033.91,-491.03C5033.91,-491.03 5033.91,-596.99 5033.91,-596.99"/>
<polygon fill="black" stroke="black" points="5030.41,-596.99 5033.91,-606.99 5037.41,-596.99 5030.41,-596.99"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;ProposalDomainModule -->
<g id="edge262" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M5027.84,-491.19C5027.84,-499.28 5027.84,-507 5027.84,-507 5027.84,-507 4719.64,-507 4719.64,-507 4719.64,-507 4719.64,-661.78 4719.64,-661.78"/>
<polygon fill="black" stroke="black" points="4716.14,-661.78 4719.64,-671.78 4723.14,-661.78 4716.14,-661.78"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;ProposalQuerySideModule -->
<g id="edge272" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M5222.24,-479C5413.46,-479 5795.91,-479 5795.91,-479 5795.91,-479 5795.91,-520.97 5795.91,-520.97"/>
<polygon fill="black" stroke="black" points="5792.41,-520.97 5795.91,-530.97 5799.41,-520.97 5792.41,-520.97"/>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge17" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5222.3,-487C5295.35,-487 5379.21,-487 5379.21,-487 5379.21,-487 5379.21,-754 5379.21,-754 5379.21,-754 6121.08,-754 6121.08,-754"/>
<polygon fill="black" stroke="black" points="6121.08,-757.5 6131.08,-754 6121.08,-750.5 6121.08,-757.5"/>
</g>
<!-- FileGenerationService  -->
<g id="node142" class="node">
<title>FileGenerationService </title>
<polygon fill="#fb8072" stroke="black" points="4597.95,-567 4452.05,-567 4452.05,-531 4597.95,-531 4597.95,-567"/>
<text text-anchor="middle" x="4525" y="-544.8" font-family="Times,serif" font-size="14.00">FileGenerationService </text>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;FileGenerationService  -->
<g id="edge250" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;FileGenerationService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5030.88,-491.31C5030.88,-511.15 5030.88,-540 5030.88,-540 5030.88,-540 4607.75,-540 4607.75,-540"/>
<polygon fill="black" stroke="black" points="4607.75,-536.5 4597.75,-540 4607.75,-543.5 4607.75,-536.5"/>
</g>
<!-- PrimaryContractQueryRepository  -->
<g id="node143" class="node">
<title>PrimaryContractQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="4434.73,-567 4225.27,-567 4225.27,-531 4434.73,-531 4434.73,-567"/>
<text text-anchor="middle" x="4330" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractQueryRepository </text>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;PrimaryContractQueryRepository  -->
<g id="edge251" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;PrimaryContractQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5024.81,-491.24C5024.81,-498.43 5024.81,-505 5024.81,-505 5024.81,-505 4395.26,-505 4395.26,-505 4395.26,-505 4395.26,-520.85 4395.26,-520.85"/>
<polygon fill="black" stroke="black" points="4391.76,-520.85 4395.26,-530.85 4398.76,-520.85 4391.76,-520.85"/>
</g>
<!-- PrimaryContractQueryService  -->
<g id="node144" class="node">
<title>PrimaryContractQueryService </title>
<polygon fill="#fb8072" stroke="black" points="4207.77,-567 4018.23,-567 4018.23,-531 4207.77,-531 4207.77,-567"/>
<text text-anchor="middle" x="4113" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractQueryService </text>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;PrimaryContractQueryService  -->
<g id="edge252" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;PrimaryContractQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5021.77,-491.03C5021.77,-497.85 5021.77,-504 5021.77,-504 5021.77,-504 4183.08,-504 4183.08,-504 4183.08,-504 4183.08,-520.96 4183.08,-520.96"/>
<polygon fill="black" stroke="black" points="4179.58,-520.96 4183.08,-530.96 4186.58,-520.96 4179.58,-520.96"/>
</g>
<!-- PrimaryContractQuerySideModule  -->
<g id="node145" class="node">
<title>PrimaryContractQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="4000.23,-567 3783.77,-567 3783.77,-531 4000.23,-531 4000.23,-567"/>
<text text-anchor="middle" x="3892" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractQuerySideModule </text>
</g>
<!-- PrimaryContractQuerySideModule&#45;&gt;PrimaryContractQuerySideModule  -->
<g id="edge253" class="edge">
<title>PrimaryContractQuerySideModule&#45;&gt;PrimaryContractQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5018.74,-491.2C5018.74,-497.03 5018.74,-502 5018.74,-502 5018.74,-502 3986.94,-502 3986.94,-502 3986.94,-502 3986.94,-520.96 3986.94,-520.96"/>
<polygon fill="black" stroke="black" points="3983.44,-520.96 3986.94,-530.96 3990.44,-520.96 3983.44,-520.96"/>
</g>
<!-- ProposalDomainModule&#45;&gt;ApplicationModule -->
<g id="edge18" class="edge">
<title>ProposalDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M4735,-708.04C4735,-715.75 4735,-723 4735,-723 4735,-723 6138.16,-723 6138.16,-723 6138.16,-723 6138.16,-726.76 6138.16,-726.76"/>
<polygon fill="black" stroke="black" points="6134.66,-726.76 6138.16,-736.76 6141.66,-726.76 6134.66,-726.76"/>
</g>
<!-- ProposalQuerySideModule&#45;&gt;LiquidationDomainModule -->
<g id="edge136" class="edge">
<title>ProposalQuerySideModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M5873,-567.19C5873,-604.46 5873,-686 5873,-686 5873,-686 5030.31,-686 5030.31,-686"/>
<polygon fill="black" stroke="black" points="5030.31,-682.5 5020.31,-686 5030.31,-689.5 5030.31,-682.5"/>
</g>
<!-- ProposalQuerySideModule&#45;&gt;ProposalDomainModule -->
<g id="edge263" class="edge">
<title>ProposalQuerySideModule&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M5803.03,-530.82C5803.03,-530.3 5803.03,-530 5803.03,-530 5803.03,-530 4781.09,-530 4781.09,-530 4781.09,-530 4781.09,-661.96 4781.09,-661.96"/>
<polygon fill="black" stroke="black" points="4777.59,-661.96 4781.09,-671.96 4784.59,-661.96 4777.59,-661.96"/>
</g>
<!-- ProposalQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge19" class="edge">
<title>ProposalQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5957.38,-563C6043.96,-563 6166.08,-563 6166.08,-563 6166.08,-563 6166.08,-726.94 6166.08,-726.94"/>
<polygon fill="black" stroke="black" points="6162.58,-726.94 6166.08,-736.94 6169.58,-726.94 6162.58,-726.94"/>
</g>
<!-- ProposalQueryRepository  -->
<g id="node151" class="node">
<title>ProposalQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="8111.41,-643 7946.59,-643 7946.59,-607 8111.41,-607 8111.41,-643"/>
<text text-anchor="middle" x="8029" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalQueryRepository </text>
</g>
<!-- ProposalQuerySideModule&#45;&gt;ProposalQueryRepository  -->
<g id="edge274" class="edge">
<title>ProposalQuerySideModule&#45;&gt;ProposalQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5957.3,-549C6347.3,-549 7957.64,-549 7957.64,-549 7957.64,-549 7957.64,-596.99 7957.64,-596.99"/>
<polygon fill="black" stroke="black" points="7954.14,-596.99 7957.64,-606.99 7961.14,-596.99 7954.14,-596.99"/>
</g>
<!-- ProposalQueryService  -->
<g id="node152" class="node">
<title>ProposalQueryService </title>
<polygon fill="#fb8072" stroke="black" points="8464.95,-643 8319.05,-643 8319.05,-607 8464.95,-607 8464.95,-643"/>
<text text-anchor="middle" x="8392" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalQueryService </text>
</g>
<!-- ProposalQuerySideModule&#45;&gt;ProposalQueryService  -->
<g id="edge275" class="edge">
<title>ProposalQuerySideModule&#45;&gt;ProposalQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5957.32,-540C6389.19,-540 8333.04,-540 8333.04,-540 8333.04,-540 8333.04,-596.97 8333.04,-596.97"/>
<polygon fill="black" stroke="black" points="8329.54,-596.97 8333.04,-606.97 8336.54,-596.97 8329.54,-596.97"/>
</g>
<!-- ProposalQuerySideModule  -->
<g id="node153" class="node">
<title>ProposalQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="8300.91,-643 8129.09,-643 8129.09,-607 8300.91,-607 8300.91,-643"/>
<text text-anchor="middle" x="8215" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalQuerySideModule </text>
</g>
<!-- ProposalQuerySideModule&#45;&gt;ProposalQuerySideModule  -->
<g id="edge276" class="edge">
<title>ProposalQuerySideModule&#45;&gt;ProposalQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5957.47,-545C6367.78,-545 8133.11,-545 8133.11,-545 8133.11,-545 8133.11,-596.85 8133.11,-596.85"/>
<polygon fill="black" stroke="black" points="8129.61,-596.85 8133.11,-606.85 8136.61,-596.85 8129.61,-596.85"/>
</g>
<!-- ScheduleDomainModule&#45;&gt;ApplicationModule -->
<g id="edge20" class="edge">
<title>ScheduleDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M4688.91,-491.06C4688.91,-502.44 4688.91,-515 4688.91,-515 4688.91,-515 6173.06,-515 6173.06,-515 6173.06,-515 6173.06,-726.96 6173.06,-726.96"/>
<polygon fill="black" stroke="black" points="6169.56,-726.96 6173.06,-736.96 6176.56,-726.96 6169.56,-726.96"/>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge161" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M5383.83,-415C5383.83,-488.25 5383.83,-760 5383.83,-760 5383.83,-760 5171.99,-760 5171.99,-760"/>
<polygon fill="black" stroke="black" points="5171.99,-756.5 5161.99,-760 5171.99,-763.5 5171.99,-756.5"/>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;PolicyDomainModule -->
<g id="edge204" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M5305.63,-397C5174.14,-397 4937.11,-397 4937.11,-397 4937.11,-397 4937.11,-444.99 4937.11,-444.99"/>
<polygon fill="black" stroke="black" points="4933.61,-444.99 4937.11,-454.99 4940.61,-444.99 4933.61,-444.99"/>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge247" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M5305.59,-406C5250.7,-406 5189.61,-406 5189.61,-406 5189.61,-406 5189.61,-444.69 5189.61,-444.69"/>
<polygon fill="black" stroke="black" points="5186.11,-444.69 5189.61,-454.69 5193.11,-444.69 5186.11,-444.69"/>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;ScheduleDomainModule -->
<g id="edge285" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M5313.29,-415.42C5313.29,-424.28 5313.29,-433 5313.29,-433 5313.29,-433 4697.01,-433 4697.01,-433 4697.01,-433 4697.01,-444.87 4697.01,-444.87"/>
<polygon fill="black" stroke="black" points="4693.51,-444.87 4697.01,-454.87 4700.51,-444.87 4693.51,-444.87"/>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge21" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5388.45,-415.04C5388.45,-487.18 5388.45,-751 5388.45,-751 5388.45,-751 6121.13,-751 6121.13,-751"/>
<polygon fill="black" stroke="black" points="6121.13,-754.5 6131.13,-751 6121.13,-747.5 6121.13,-754.5"/>
</g>
<!-- ScheduleQueryRepository  -->
<g id="node158" class="node">
<title>ScheduleQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="7281.74,-491 7114.26,-491 7114.26,-455 7281.74,-455 7281.74,-491"/>
<text text-anchor="middle" x="7198" y="-468.8" font-family="Times,serif" font-size="14.00">ScheduleQueryRepository </text>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;ScheduleQueryRepository  -->
<g id="edge296" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;ScheduleQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5434.72,-415.17C5434.72,-419.05 5434.72,-422 5434.72,-422 5434.72,-422 7122.25,-422 7122.25,-422 7122.25,-422 7122.25,-444.88 7122.25,-444.88"/>
<polygon fill="black" stroke="black" points="7118.75,-444.88 7122.25,-454.88 7125.75,-444.88 7118.75,-444.88"/>
</g>
<!-- ScheduleQueryService  -->
<g id="node159" class="node">
<title>ScheduleQueryService </title>
<polygon fill="#fb8072" stroke="black" points="7095.78,-491 6948.22,-491 6948.22,-455 7095.78,-455 7095.78,-491"/>
<text text-anchor="middle" x="7022" y="-468.8" font-family="Times,serif" font-size="14.00">ScheduleQueryService </text>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;ScheduleQueryService  -->
<g id="edge297" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;ScheduleQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5424.31,-415.06C5424.31,-419.49 5424.31,-423 5424.31,-423 5424.31,-423 6952.83,-423 6952.83,-423 6952.83,-423 6952.83,-444.95 6952.83,-444.95"/>
<polygon fill="black" stroke="black" points="6949.33,-444.95 6952.83,-454.95 6956.33,-444.95 6949.33,-444.95"/>
</g>
<!-- ScheduleQuerySideModule  -->
<g id="node160" class="node">
<title>ScheduleQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="6930.24,-491 6755.76,-491 6755.76,-455 6930.24,-455 6930.24,-491"/>
<text text-anchor="middle" x="6843" y="-468.8" font-family="Times,serif" font-size="14.00">ScheduleQuerySideModule </text>
</g>
<!-- ScheduleQuerySideModule&#45;&gt;ScheduleQuerySideModule  -->
<g id="edge298" class="edge">
<title>ScheduleQuerySideModule&#45;&gt;ScheduleQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5413.9,-415.16C5413.9,-420.04 5413.9,-424 5413.9,-424 5413.9,-424 6815.68,-424 6815.68,-424 6815.68,-424 6815.68,-444.75 6815.68,-444.75"/>
<polygon fill="black" stroke="black" points="6812.18,-444.75 6815.68,-454.75 6819.18,-444.75 6812.18,-444.75"/>
</g>
<!-- StatushistoryModule&#45;&gt;ListenerModule -->
<g id="edge162" class="edge">
<title>StatushistoryModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M4669.52,-321C4659.61,-321 4652.65,-321 4652.65,-321 4652.65,-321 4652.65,-755 4652.65,-755 4652.65,-755 5046.21,-755 5046.21,-755"/>
<polygon fill="black" stroke="black" points="5046.21,-758.5 5056.21,-755 5046.21,-751.5 5046.21,-758.5"/>
</g>
<!-- StatushistoryModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge248" class="edge">
<title>StatushistoryModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M4802.32,-332C4889.9,-332 5033.17,-332 5033.17,-332 5033.17,-332 5033.17,-444.86 5033.17,-444.86"/>
<polygon fill="black" stroke="black" points="5029.67,-444.86 5033.17,-454.86 5036.67,-444.86 5029.67,-444.86"/>
</g>
<!-- StatushistoryModule&#45;&gt;ApplicationModule -->
<g id="edge22" class="edge">
<title>StatushistoryModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M4736,-339.49C4736,-351.14 4736,-364 4736,-364 4736,-364 6207.96,-364 6207.96,-364 6207.96,-364 6207.96,-726.93 6207.96,-726.93"/>
<polygon fill="black" stroke="black" points="6204.46,-726.93 6207.96,-736.93 6211.46,-726.93 6204.46,-726.93"/>
</g>
<!-- StatushistoryService  -->
<g id="node167" class="node">
<title>StatushistoryService </title>
<polygon fill="#fb8072" stroke="black" points="4310.09,-263 4175.91,-263 4175.91,-227 4310.09,-227 4310.09,-263"/>
<text text-anchor="middle" x="4243" y="-240.8" font-family="Times,serif" font-size="14.00">StatushistoryService </text>
</g>
<!-- StatushistoryModule&#45;&gt;StatushistoryService  -->
<g id="edge313" class="edge">
<title>StatushistoryModule&#45;&gt;StatushistoryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4750.16,-302.81C4750.16,-294.72 4750.16,-287 4750.16,-287 4750.16,-287 4243,-287 4243,-287 4243,-287 4243,-273.06 4243,-273.06"/>
<polygon fill="black" stroke="black" points="4246.5,-273.06 4243,-263.06 4239.5,-273.06 4246.5,-273.06"/>
</g>
<!-- TransferHistoryModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge232" class="edge">
<title>TransferHistoryModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M5781.16,-415.13C5781.16,-469.58 5781.16,-628 5781.16,-628 5781.16,-628 5050.26,-628 5050.26,-628"/>
<polygon fill="black" stroke="black" points="5050.26,-624.5 5040.26,-628 5050.26,-631.5 5050.26,-624.5"/>
</g>
<!-- TransferHistoryModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge249" class="edge">
<title>TransferHistoryModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M5777.34,-415.03C5777.34,-434.58 5777.34,-463 5777.34,-463 5777.34,-463 5232.26,-463 5232.26,-463"/>
<polygon fill="black" stroke="black" points="5232.26,-459.5 5222.26,-463 5232.26,-466.5 5232.26,-459.5"/>
</g>
<!-- TransferHistoryModule&#45;&gt;ApplicationModule -->
<g id="edge23" class="edge">
<title>TransferHistoryModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M5784.98,-415.44C5784.98,-486.69 5784.98,-740 5784.98,-740 5784.98,-740 6120.99,-740 6120.99,-740"/>
<polygon fill="black" stroke="black" points="6120.99,-743.5 6130.99,-740 6120.99,-736.5 6120.99,-743.5"/>
</g>
<!-- TransferHistoryModule  -->
<g id="node171" class="node">
<title>TransferHistoryModule </title>
<polygon fill="#fb8072" stroke="black" points="8307.45,-491 8154.55,-491 8154.55,-455 8307.45,-455 8307.45,-491"/>
<text text-anchor="middle" x="8231" y="-468.8" font-family="Times,serif" font-size="14.00">TransferHistoryModule </text>
</g>
<!-- TransferHistoryModule&#45;&gt;TransferHistoryModule  -->
<g id="edge319" class="edge">
<title>TransferHistoryModule&#45;&gt;TransferHistoryModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5876.14,-378.58C5876.14,-369.72 5876.14,-361 5876.14,-361 5876.14,-361 8285.25,-361 8285.25,-361 8285.25,-361 8285.25,-444.51 8285.25,-444.51"/>
<polygon fill="black" stroke="black" points="8281.75,-444.51 8285.25,-454.51 8288.75,-444.51 8281.75,-444.51"/>
</g>
<!-- TransferHistoryRepository  -->
<g id="node172" class="node">
<title>TransferHistoryRepository </title>
<polygon fill="#fb8072" stroke="black" points="8137.34,-491 7966.66,-491 7966.66,-455 8137.34,-455 8137.34,-491"/>
<text text-anchor="middle" x="8052" y="-468.8" font-family="Times,serif" font-size="14.00">TransferHistoryRepository </text>
</g>
<!-- TransferHistoryModule&#45;&gt;TransferHistoryRepository  -->
<g id="edge320" class="edge">
<title>TransferHistoryModule&#45;&gt;TransferHistoryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5852.23,-415.08C5852.23,-416.85 5852.23,-418 5852.23,-418 5852.23,-418 7998.42,-418 7998.42,-418 7998.42,-418 7998.42,-444.99 7998.42,-444.99"/>
<polygon fill="black" stroke="black" points="7994.92,-444.99 7998.42,-454.99 8001.92,-444.99 7994.92,-444.99"/>
</g>
<!-- TransferHistoryService  -->
<g id="node173" class="node">
<title>TransferHistoryService </title>
<polygon fill="#fb8072" stroke="black" points="7948.38,-491 7797.62,-491 7797.62,-455 7948.38,-455 7948.38,-491"/>
<text text-anchor="middle" x="7873" y="-468.8" font-family="Times,serif" font-size="14.00">TransferHistoryService </text>
</g>
<!-- TransferHistoryModule&#45;&gt;TransferHistoryService  -->
<g id="edge321" class="edge">
<title>TransferHistoryModule&#45;&gt;TransferHistoryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5828.82,-415.17C5828.82,-417.45 5828.82,-419 5828.82,-419 5828.82,-419 7873,-419 7873,-419 7873,-419 7873,-444.72 7873,-444.72"/>
<polygon fill="black" stroke="black" points="7869.5,-444.72 7873,-454.72 7876.5,-444.72 7869.5,-444.72"/>
</g>
<!-- ConfigModule -->
<g id="node25" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="122.44,-187 119.44,-191 98.44,-191 95.44,-187 23.56,-187 23.56,-151 122.44,-151 122.44,-187"/>
<text text-anchor="middle" x="73" y="-164.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;AuthModule -->
<g id="edge24" class="edge">
<title>ConfigModule&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M56.59,-187.13C56.59,-197.71 56.59,-209 56.59,-209 56.59,-209 5092.73,-209 5092.73,-209 5092.73,-209 5092.73,-216.58 5092.73,-216.58"/>
<polygon fill="black" stroke="black" points="5089.23,-216.58 5092.73,-226.58 5096.23,-216.58 5089.23,-216.58"/>
</g>
<!-- ConfigModule&#45;&gt;LiquidationQuerySideModule -->
<g id="edge142" class="edge">
<title>ConfigModule&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M67.53,-187.06C67.53,-191.49 67.53,-195 67.53,-195 67.53,-195 6023,-195 6023,-195 6023,-195 6023,-292.95 6023,-292.95"/>
<polygon fill="black" stroke="black" points="6019.5,-292.95 6023,-302.95 6026.5,-292.95 6019.5,-292.95"/>
</g>
<!-- ConfigModule&#45;&gt;LoggerModule -->
<g id="edge163" class="edge">
<title>ConfigModule&#45;&gt;LoggerModule</title>
<path fill="none" stroke="black" d="M111.28,-187.17C111.28,-189.45 111.28,-191 111.28,-191 111.28,-191 5442.34,-191 5442.34,-191 5442.34,-191 5442.34,-190.62 5442.34,-190.62"/>
<polygon fill="black" stroke="black" points="5445.84,-197.17 5442.34,-187.17 5438.84,-197.17 5445.84,-197.17"/>
</g>
<!-- ConfigModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge240" class="edge">
<title>ConfigModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M100.34,-187.15C100.34,-199.27 100.34,-213 100.34,-213 100.34,-213 5056.7,-213 5056.7,-213 5056.7,-213 5056.7,-444.72 5056.7,-444.72"/>
<polygon fill="black" stroke="black" points="5053.2,-444.72 5056.7,-454.72 5060.2,-444.72 5053.2,-444.72"/>
</g>
<!-- ConfigModule&#45;&gt;ProposalQuerySideModule -->
<g id="edge268" class="edge">
<title>ConfigModule&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M78.47,-187.22C78.47,-258.82 78.47,-517 78.47,-517 78.47,-517 5856,-517 5856,-517 5856,-517 5856,-520.76 5856,-520.76"/>
<polygon fill="black" stroke="black" points="5852.5,-520.76 5856,-530.76 5859.5,-520.76 5852.5,-520.76"/>
</g>
<!-- ConfigModule  -->
<g id="node36" class="node">
<title>ConfigModule </title>
<polygon fill="#fb8072" stroke="black" points="4597.94,-263 4496.06,-263 4496.06,-227 4597.94,-227 4597.94,-263"/>
<text text-anchor="middle" x="4547" y="-240.8" font-family="Times,serif" font-size="14.00">ConfigModule </text>
</g>
<!-- ConfigModule&#45;&gt;ConfigModule  -->
<g id="edge36" class="edge">
<title>ConfigModule&#45;&gt;ConfigModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M45.66,-187.15C45.66,-200.74 45.66,-217 45.66,-217 45.66,-217 4547,-217 4547,-217 4547,-217 4547,-217.98 4547,-217.98"/>
<polygon fill="black" stroke="black" points="4543.5,-216.79 4547,-226.79 4550.5,-216.79 4543.5,-216.79"/>
</g>
<!-- ConfigService  -->
<g id="node37" class="node">
<title>ConfigService </title>
<polygon fill="#fb8072" stroke="black" points="4478.37,-263 4377.63,-263 4377.63,-227 4478.37,-227 4478.37,-263"/>
<text text-anchor="middle" x="4428" y="-240.8" font-family="Times,serif" font-size="14.00">ConfigService </text>
</g>
<!-- ConfigModule&#45;&gt;ConfigService  -->
<g id="edge37" class="edge">
<title>ConfigModule&#45;&gt;ConfigService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M34.72,-187.12C34.72,-201.78 34.72,-220 34.72,-220 34.72,-220 4425.03,-220 4425.03,-220 4425.03,-220 4425.03,-220.68 4425.03,-220.68"/>
<polygon fill="black" stroke="black" points="4421.53,-216.83 4425.03,-226.83 4428.53,-216.83 4421.53,-216.83"/>
</g>
<!-- ConfigModule&#45;&gt;StatushistoryExportModule -->
<g id="edge302" class="edge">
<title>ConfigModule&#45;&gt;StatushistoryExportModule</title>
<path fill="none" stroke="black" d="M89.41,-187.34C89.41,-216.21 89.41,-269 89.41,-269 89.41,-269 12741.51,-269 12741.51,-269 12741.51,-269 12741.51,-292.81 12741.51,-292.81"/>
<polygon fill="black" stroke="black" points="12738.01,-292.81 12741.51,-302.81 12745.01,-292.81 12738.01,-292.81"/>
</g>
<!-- AuthService -->
<g id="node27" class="node">
<title>AuthService</title>
<ellipse fill="#fdb462" stroke="black" cx="205" cy="-169" rx="59.11" ry="18"/>
<text text-anchor="middle" x="205" y="-164.8" font-family="Times,serif" font-size="14.00">AuthService</text>
</g>
<!-- AuthService&#45;&gt;AuthModule -->
<g id="edge28" class="edge">
<title>AuthService&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M205,-187.07C205,-196.46 205,-206 205,-206 205,-206 5105.24,-206 5105.24,-206 5105.24,-206 5105.24,-216.87 5105.24,-216.87"/>
<polygon fill="black" stroke="black" points="5101.74,-216.87 5105.24,-226.87 5108.74,-216.87 5101.74,-216.87"/>
</g>
<!-- JwtStrategy -->
<g id="node28" class="node">
<title>JwtStrategy</title>
<ellipse fill="#fdb462" stroke="black" cx="339" cy="-169" rx="56.76" ry="18"/>
<text text-anchor="middle" x="339" y="-164.8" font-family="Times,serif" font-size="14.00">JwtStrategy</text>
</g>
<!-- JwtStrategy&#45;&gt;AuthModule -->
<g id="edge29" class="edge">
<title>JwtStrategy&#45;&gt;AuthModule</title>
<path fill="none" stroke="black" d="M339,-187.04C339,-194.75 339,-202 339,-202 339,-202 5117.75,-202 5117.75,-202 5117.75,-202 5117.75,-216.51 5117.75,-216.51"/>
<polygon fill="black" stroke="black" points="5114.25,-216.51 5117.75,-226.51 5121.25,-216.51 5114.25,-216.51"/>
</g>
<!-- QueryDatabaseModule -->
<g id="node29" class="node">
<title>QueryDatabaseModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2553.83,-263 2550.83,-267 2529.83,-267 2526.83,-263 2408.17,-263 2408.17,-227 2553.83,-227 2553.83,-263"/>
<text text-anchor="middle" x="2481" y="-240.8" font-family="Times,serif" font-size="14.00">QueryDatabaseModule</text>
</g>
<!-- QueryDatabaseModule&#45;&gt;DiscountQuerySideModule -->
<g id="edge52" class="edge">
<title>QueryDatabaseModule&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M2464.02,-263.29C2464.02,-306.25 2464.02,-410 2464.02,-410 2464.02,-410 4647.82,-410 4647.82,-410"/>
<polygon fill="black" stroke="black" points="4647.82,-413.5 4657.82,-410 4647.82,-406.5 4647.82,-413.5"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;HandoverQuerySideModule -->
<g id="edge79" class="edge">
<title>QueryDatabaseModule&#45;&gt;HandoverQuerySideModule</title>
<path fill="none" stroke="black" d="M2545.74,-263C2545.74,-296.94 2545.74,-367 2545.74,-367 2545.74,-367 5984.35,-367 5984.35,-367 5984.35,-367 5984.35,-368.86 5984.35,-368.86"/>
<polygon fill="black" stroke="black" points="5980.85,-368.86 5984.35,-378.86 5987.85,-368.86 5980.85,-368.86"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;HandoverScheduleQuerySideModule -->
<g id="edge116" class="edge">
<title>QueryDatabaseModule&#45;&gt;HandoverScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M2504.88,-263.2C2504.88,-269.03 2504.88,-274 2504.88,-274 2504.88,-274 5672.22,-274 5672.22,-274 5672.22,-274 5672.22,-292.96 5672.22,-292.96"/>
<polygon fill="black" stroke="black" points="5668.72,-292.96 5672.22,-302.96 5675.72,-292.96 5668.72,-292.96"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;HistoryImportQuerySideModule -->
<g id="edge123" class="edge">
<title>QueryDatabaseModule&#45;&gt;HistoryImportQuerySideModule</title>
<path fill="none" stroke="black" d="M2496.71,-263.03C2496.71,-269.85 2496.71,-276 2496.71,-276 2496.71,-276 5585.19,-276 5585.19,-276 5585.19,-276 5585.19,-520.66 5585.19,-520.66"/>
<polygon fill="black" stroke="black" points="5581.69,-520.66 5585.19,-530.66 5588.69,-520.66 5581.69,-520.66"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;LiquidationQuerySideModule -->
<g id="edge145" class="edge">
<title>QueryDatabaseModule&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M2513.05,-263.21C2513.05,-268.55 2513.05,-273 2513.05,-273 2513.05,-273 5961.42,-273 5961.42,-273 5961.42,-273 5961.42,-292.85 5961.42,-292.85"/>
<polygon fill="black" stroke="black" points="5957.92,-292.85 5961.42,-302.85 5964.92,-292.85 5957.92,-292.85"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;PolicyQuerySideModule -->
<g id="edge215" class="edge">
<title>QueryDatabaseModule&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M2488.54,-263.04C2488.54,-270.75 2488.54,-278 2488.54,-278 2488.54,-278 5391.21,-278 5391.21,-278 5391.21,-278 5391.21,-292.51 5391.21,-292.51"/>
<polygon fill="black" stroke="black" points="5387.71,-292.51 5391.21,-302.51 5394.71,-292.51 5387.71,-292.51"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge246" class="edge">
<title>QueryDatabaseModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M2455.85,-263.11C2455.85,-272.9 2455.85,-283 2455.85,-283 2455.85,-283 5044.93,-283 5044.93,-283 5044.93,-283 5044.93,-444.78 5044.93,-444.78"/>
<polygon fill="black" stroke="black" points="5041.43,-444.78 5044.93,-454.78 5048.43,-444.78 5041.43,-444.78"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;ProposalQuerySideModule -->
<g id="edge273" class="edge">
<title>QueryDatabaseModule&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M2537.57,-263.2C2537.57,-323.96 2537.57,-516 2537.57,-516 2537.57,-516 5906.6,-516 5906.6,-516 5906.6,-516 5906.6,-520.96 5906.6,-520.96"/>
<polygon fill="black" stroke="black" points="5903.1,-520.96 5906.6,-530.96 5910.1,-520.96 5903.1,-520.96"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;ScheduleQuerySideModule -->
<g id="edge295" class="edge">
<title>QueryDatabaseModule&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M2480.36,-263.07C2480.36,-272.46 2480.36,-282 2480.36,-282 2480.36,-282 5334.68,-282 5334.68,-282 5334.68,-282 5334.68,-368.78 5334.68,-368.78"/>
<polygon fill="black" stroke="black" points="5331.18,-368.78 5334.68,-378.78 5338.18,-368.78 5331.18,-368.78"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;StatushistoryModule -->
<g id="edge312" class="edge">
<title>QueryDatabaseModule&#45;&gt;StatushistoryModule</title>
<path fill="none" stroke="black" d="M2447.67,-263.25C2447.67,-277.18 2447.67,-294 2447.67,-294 2447.67,-294 4681.17,-294 4681.17,-294 4681.17,-294 4681.17,-294.88 4681.17,-294.88"/>
<polygon fill="black" stroke="black" points="4677.67,-292.84 4681.17,-302.84 4684.67,-292.84 4677.67,-292.84"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;TransferHistoryModule -->
<g id="edge318" class="edge">
<title>QueryDatabaseModule&#45;&gt;TransferHistoryModule</title>
<path fill="none" stroke="black" d="M2472.19,-263.03C2472.19,-297.42 2472.19,-369 2472.19,-369 2472.19,-369 5783.45,-369 5783.45,-369 5783.45,-369 5783.45,-369.98 5783.45,-369.98"/>
<polygon fill="black" stroke="black" points="5779.95,-368.79 5783.45,-378.79 5786.95,-368.79 5779.95,-368.79"/>
</g>
<!-- CodeGenerateModule -->
<g id="node30" class="node">
<title>CodeGenerateModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2122.62,-339 2119.62,-343 2098.62,-343 2095.62,-339 1983.38,-339 1983.38,-303 2122.62,-303 2122.62,-339"/>
<text text-anchor="middle" x="2053" y="-316.8" font-family="Times,serif" font-size="14.00">CodeGenerateModule</text>
</g>
<!-- QueryDatabaseModule&#45;&gt;CodeGenerateModule -->
<g id="edge30" class="edge">
<title>QueryDatabaseModule&#45;&gt;CodeGenerateModule</title>
<path fill="none" stroke="black" d="M2423.79,-263.04C2423.79,-284.66 2423.79,-318 2423.79,-318 2423.79,-318 2132.74,-318 2132.74,-318"/>
<polygon fill="black" stroke="black" points="2132.74,-314.5 2122.74,-318 2132.74,-321.5 2132.74,-314.5"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;EmployeeQuerySideModule -->
<g id="edge60" class="edge">
<title>QueryDatabaseModule&#45;&gt;EmployeeQuerySideModule</title>
<path fill="none" stroke="black" d="M2528.06,-226.92C2528.06,-225.15 2528.06,-224 2528.06,-224 2528.06,-224 4374.61,-224 4374.61,-224 4374.61,-224 4374.61,-292.76 4374.61,-292.76"/>
<polygon fill="black" stroke="black" points="4371.11,-292.76 4374.61,-302.76 4378.11,-292.76 4371.11,-292.76"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;HandoverRequestQuerySideModule -->
<g id="edge96" class="edge">
<title>QueryDatabaseModule&#45;&gt;HandoverRequestQuerySideModule</title>
<path fill="none" stroke="black" d="M2521.23,-263.06C2521.23,-267.49 2521.23,-271 2521.23,-271 2521.23,-271 11343.86,-271 11343.86,-271 11343.86,-271 11343.86,-292.95 11343.86,-292.95"/>
<polygon fill="black" stroke="black" points="11340.36,-292.95 11343.86,-302.95 11347.36,-292.95 11340.36,-292.95"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;StatushistoryExportModule -->
<g id="edge305" class="edge">
<title>QueryDatabaseModule&#45;&gt;StatushistoryExportModule</title>
<path fill="none" stroke="black" d="M2529.4,-263.17C2529.4,-265.45 2529.4,-267 2529.4,-267 2529.4,-267 12774.73,-267 12774.73,-267 12774.73,-267 12774.73,-292.72 12774.73,-292.72"/>
<polygon fill="black" stroke="black" points="12771.23,-292.72 12774.73,-302.72 12778.23,-292.72 12771.23,-292.72"/>
</g>
<!-- CodeGenerateModule&#45;&gt;DiscountDomainModule -->
<g id="edge38" class="edge">
<title>CodeGenerateModule&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M2042.38,-339.19C2042.38,-354.18 2042.38,-373 2042.38,-373 2042.38,-373 5255.67,-373 5255.67,-373 5255.67,-373 5255.67,-471 5255.67,-471 5255.67,-471 5795.13,-471 5795.13,-471"/>
<polygon fill="black" stroke="black" points="5795.13,-474.5 5805.13,-471 5795.13,-467.5 5795.13,-474.5"/>
</g>
<!-- CodeGenerateModule&#45;&gt;HandoverDomainModule -->
<g id="edge65" class="edge">
<title>CodeGenerateModule&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M2115.3,-339.1C2115.3,-388.33 2115.3,-521 2115.3,-521 2115.3,-521 5403.49,-521 5403.49,-521 5403.49,-521 5403.49,-521.98 5403.49,-521.98"/>
<polygon fill="black" stroke="black" points="5399.99,-520.79 5403.49,-530.79 5406.99,-520.79 5399.99,-520.79"/>
</g>
<!-- CodeGenerateModule&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge102" class="edge">
<title>CodeGenerateModule&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M2113.34,-339.19C2113.34,-388.67 2113.34,-522 2113.34,-522 2113.34,-522 5183.07,-522 5183.07,-522 5183.07,-522 5183.07,-522.88 5183.07,-522.88"/>
<polygon fill="black" stroke="black" points="5179.57,-520.84 5183.07,-530.84 5186.57,-520.84 5179.57,-520.84"/>
</g>
<!-- CodeGenerateModule&#45;&gt;LiquidationDomainModule -->
<g id="edge129" class="edge">
<title>CodeGenerateModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M2105.49,-339.33C2105.49,-370.86 2105.49,-432 2105.49,-432 2105.49,-432 4819.58,-432 4819.58,-432 4819.58,-432 4819.58,-681 4819.58,-681 4819.58,-681 4839.91,-681 4839.91,-681"/>
<polygon fill="black" stroke="black" points="4839.91,-684.5 4849.91,-681 4839.91,-677.5 4839.91,-684.5"/>
</g>
<!-- CodeGenerateModule&#45;&gt;PolicyDomainModule -->
<g id="edge198" class="edge">
<title>CodeGenerateModule&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M2107.45,-339.01C2107.45,-355.05 2107.45,-376 2107.45,-376 2107.45,-376 4890.3,-376 4890.3,-376 4890.3,-376 4890.3,-444.76 4890.3,-444.76"/>
<polygon fill="black" stroke="black" points="4886.8,-444.76 4890.3,-454.76 4893.8,-444.76 4886.8,-444.76"/>
</g>
<!-- CodeGenerateModule&#45;&gt;PolicyQuerySideModule -->
<g id="edge211" class="edge">
<title>CodeGenerateModule&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M2076.27,-302.9C2076.27,-291.91 2076.27,-280 2076.27,-280 2076.27,-280 5385.27,-280 5385.27,-280 5385.27,-280 5385.27,-292.9 5385.27,-292.9"/>
<polygon fill="black" stroke="black" points="5381.77,-292.9 5385.27,-302.9 5388.77,-292.9 5381.77,-292.9"/>
</g>
<!-- CodeGenerateModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge221" class="edge">
<title>CodeGenerateModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M2111.37,-339.28C2111.37,-389.01 2111.37,-523 2111.37,-523 2111.37,-523 4943.28,-523 4943.28,-523 4943.28,-523 4943.28,-596.76 4943.28,-596.76"/>
<polygon fill="black" stroke="black" points="4939.78,-596.76 4943.28,-606.76 4946.78,-596.76 4939.78,-596.76"/>
</g>
<!-- CodeGenerateModule&#45;&gt;ProposalDomainModule -->
<g id="edge257" class="edge">
<title>CodeGenerateModule&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M2122.76,-325C2368.87,-325 3181.84,-325 3181.84,-325 3181.84,-325 3181.84,-688 3181.84,-688 3181.84,-688 4648.24,-688 4648.24,-688"/>
<polygon fill="black" stroke="black" points="4648.24,-691.5 4658.24,-688 4648.24,-684.5 4648.24,-691.5"/>
</g>
<!-- CodeGenerateModule&#45;&gt;ScheduleDomainModule -->
<g id="edge280" class="edge">
<title>CodeGenerateModule&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M2103.52,-339.02C2103.52,-376.83 2103.52,-461 2103.52,-461 2103.52,-461 4647.93,-461 4647.93,-461"/>
<polygon fill="black" stroke="black" points="4647.93,-464.5 4657.93,-461 4647.93,-457.5 4647.93,-464.5"/>
</g>
<!-- CodeGenerateModule&#45;&gt;ScheduleQuerySideModule -->
<g id="edge290" class="edge">
<title>CodeGenerateModule&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M2109.41,-339.12C2109.41,-353.78 2109.41,-372 2109.41,-372 2109.41,-372 5315.31,-372 5315.31,-372 5315.31,-372 5315.31,-372.68 5315.31,-372.68"/>
<polygon fill="black" stroke="black" points="5311.81,-368.83 5315.31,-378.83 5318.81,-368.83 5311.81,-368.83"/>
</g>
<!-- CodeGenerateModule  -->
<g id="node31" class="node">
<title>CodeGenerateModule </title>
<polygon fill="#fb8072" stroke="black" points="2101.62,-415 1958.38,-415 1958.38,-379 2101.62,-379 2101.62,-415"/>
<text text-anchor="middle" x="2030" y="-392.8" font-family="Times,serif" font-size="14.00">CodeGenerateModule </text>
</g>
<!-- CodeGenerateModule&#45;&gt;CodeGenerateModule  -->
<g id="edge31" class="edge">
<title>CodeGenerateModule&#45;&gt;CodeGenerateModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2012.78,-339.01C2012.78,-339.01 2012.78,-368.85 2012.78,-368.85"/>
<polygon fill="black" stroke="black" points="2009.28,-368.85 2012.78,-378.85 2016.28,-368.85 2009.28,-368.85"/>
</g>
<!-- CodeGenerateRepository  -->
<g id="node32" class="node">
<title>CodeGenerateRepository </title>
<polygon fill="#fb8072" stroke="black" points="2439.51,-415 2278.49,-415 2278.49,-379 2439.51,-379 2439.51,-415"/>
<text text-anchor="middle" x="2359" y="-392.8" font-family="Times,serif" font-size="14.00">CodeGenerateRepository </text>
</g>
<!-- CodeGenerateModule&#45;&gt;CodeGenerateRepository  -->
<g id="edge32" class="edge">
<title>CodeGenerateModule&#45;&gt;CodeGenerateRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2122.81,-332C2205.18,-332 2331.07,-332 2331.07,-332 2331.07,-332 2331.07,-368.89 2331.07,-368.89"/>
<polygon fill="black" stroke="black" points="2327.57,-368.89 2331.07,-378.89 2334.57,-368.89 2327.57,-368.89"/>
</g>
<!-- CodeGenerateService  -->
<g id="node33" class="node">
<title>CodeGenerateService </title>
<polygon fill="#fb8072" stroke="black" points="2260.55,-415 2119.45,-415 2119.45,-379 2260.55,-379 2260.55,-415"/>
<text text-anchor="middle" x="2190" y="-392.8" font-family="Times,serif" font-size="14.00">CodeGenerateService </text>
</g>
<!-- CodeGenerateModule&#45;&gt;CodeGenerateService  -->
<g id="edge33" class="edge">
<title>CodeGenerateModule&#45;&gt;CodeGenerateService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2117.26,-339.01C2117.26,-361.49 2117.26,-397 2117.26,-397 2117.26,-397 2117.47,-397 2117.47,-397"/>
<polygon fill="black" stroke="black" points="2109.36,-400.5 2119.36,-397 2109.36,-393.5 2109.36,-400.5"/>
</g>
<!-- CodeGenerateModule&#45;&gt;HandoverRequestDomainModule -->
<g id="edge85" class="edge">
<title>CodeGenerateModule&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M2071.97,-339.15C2071.97,-340.3 2071.97,-341 2071.97,-341 2071.97,-341 11764.25,-341 11764.25,-341 11764.25,-341 11764.25,-482 11764.25,-482 11764.25,-482 11800.5,-482 11800.5,-482"/>
<polygon fill="black" stroke="black" points="11800.5,-485.5 11810.5,-482 11800.5,-478.5 11800.5,-485.5"/>
</g>
<!-- CodeGenerateRepository -->
<g id="node34" class="node">
<title>CodeGenerateRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="2275" cy="-245" rx="108.79" ry="18"/>
<text text-anchor="middle" x="2275" y="-240.8" font-family="Times,serif" font-size="14.00">CodeGenerateRepository</text>
</g>
<!-- CodeGenerateRepository&#45;&gt;CodeGenerateModule -->
<g id="edge34" class="edge">
<title>CodeGenerateRepository&#45;&gt;CodeGenerateModule</title>
<path fill="none" stroke="black" d="M2213.56,-259.98C2213.56,-279.45 2213.56,-311 2213.56,-311 2213.56,-311 2132.96,-311 2132.96,-311"/>
<polygon fill="black" stroke="black" points="2132.96,-307.5 2122.96,-311 2132.96,-314.5 2132.96,-307.5"/>
</g>
<!-- CodeGenerateService -->
<g id="node35" class="node">
<title>CodeGenerateService</title>
<ellipse fill="#fdb462" stroke="black" cx="2053" cy="-245" rx="95.44" ry="18"/>
<text text-anchor="middle" x="2053" y="-240.8" font-family="Times,serif" font-size="14.00">CodeGenerateService</text>
</g>
<!-- CodeGenerateService&#45;&gt;CodeGenerateModule -->
<g id="edge35" class="edge">
<title>CodeGenerateService&#45;&gt;CodeGenerateModule</title>
<path fill="none" stroke="black" d="M2029.73,-262.63C2029.73,-262.63 2029.73,-292.94 2029.73,-292.94"/>
<polygon fill="black" stroke="black" points="2026.23,-292.94 2029.73,-302.94 2033.23,-292.94 2026.23,-292.94"/>
</g>
<!-- DomainDatabaseModule -->
<g id="node38" class="node">
<title>DomainDatabaseModule</title>
<polygon fill="#8dd3c7" stroke="black" points="7137.95,-415 7134.95,-419 7113.95,-419 7110.95,-415 6982.05,-415 6982.05,-379 7137.95,-379 7137.95,-415"/>
<text text-anchor="middle" x="7060" y="-392.8" font-family="Times,serif" font-size="14.00">DomainDatabaseModule</text>
</g>
<!-- DomainDatabaseModule&#45;&gt;DiscountDomainModule -->
<g id="edge40" class="edge">
<title>DomainDatabaseModule&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M7067.42,-415.03C7067.42,-431.4 7067.42,-453 7067.42,-453 7067.42,-453 5958.11,-453 5958.11,-453 5958.11,-453 5958.11,-453.18 5958.11,-453.18"/>
<polygon fill="black" stroke="black" points="5954.61,-444.85 5958.11,-454.85 5961.61,-444.85 5954.61,-444.85"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;HandoverDomainModule -->
<g id="edge66" class="edge">
<title>DomainDatabaseModule&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M7053.19,-415.19C7053.19,-430.18 7053.19,-449 7053.19,-449 7053.19,-449 5508.51,-449 5508.51,-449 5508.51,-449 5508.51,-520.66 5508.51,-520.66"/>
<polygon fill="black" stroke="black" points="5505.01,-520.66 5508.51,-530.66 5512.01,-520.66 5505.01,-520.66"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge103" class="edge">
<title>DomainDatabaseModule&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M7038.96,-415.15C7038.96,-428.74 7038.96,-445 7038.96,-445 7038.96,-445 5336.28,-445 5336.28,-445 5336.28,-445 5336.28,-520.88 5336.28,-520.88"/>
<polygon fill="black" stroke="black" points="5332.78,-520.88 5336.28,-530.88 5339.78,-520.88 5332.78,-520.88"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;LiquidationDomainModule -->
<g id="edge130" class="edge">
<title>DomainDatabaseModule&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M7081.66,-415.04C7081.66,-431.73 7081.66,-454 7081.66,-454 7081.66,-454 5994.98,-454 5994.98,-454 5994.98,-454 5994.98,-690 5994.98,-690 5994.98,-690 5030.4,-690 5030.4,-690"/>
<polygon fill="black" stroke="black" points="5030.4,-686.5 5020.4,-690 5030.4,-693.5 5030.4,-686.5"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;PolicyDomainModule -->
<g id="edge200" class="edge">
<title>DomainDatabaseModule&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M7010.49,-415.13C7010.49,-425.71 7010.49,-437 7010.49,-437 7010.49,-437 4960.51,-437 4960.51,-437 4960.51,-437 4960.51,-444.58 4960.51,-444.58"/>
<polygon fill="black" stroke="black" points="4957.01,-444.58 4960.51,-454.58 4964.01,-444.58 4957.01,-444.58"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;PrimaryContractDomainModule -->
<g id="edge222" class="edge">
<title>DomainDatabaseModule&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M7024.72,-415.1C7024.72,-426.09 7024.72,-438 7024.72,-438 7024.72,-438 5002.15,-438 5002.15,-438 5002.15,-438 5002.15,-596.73 5002.15,-596.73"/>
<polygon fill="black" stroke="black" points="4998.65,-596.73 5002.15,-606.73 5005.65,-596.73 4998.65,-596.73"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;ProposalDomainModule -->
<g id="edge258" class="edge">
<title>DomainDatabaseModule&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M7105.14,-415.33C7105.14,-446.86 7105.14,-508 7105.14,-508 7105.14,-508 4750.36,-508 4750.36,-508 4750.36,-508 4750.36,-661.88 4750.36,-661.88"/>
<polygon fill="black" stroke="black" points="4746.86,-661.88 4750.36,-671.88 4753.86,-661.88 4746.86,-661.88"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;ScheduleDomainModule -->
<g id="edge281" class="edge">
<title>DomainDatabaseModule&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M6996.26,-415.11C6996.26,-424.9 6996.26,-435 6996.26,-435 6996.26,-435 4774.99,-435 4774.99,-435 4774.99,-435 4774.99,-444.89 4774.99,-444.89"/>
<polygon fill="black" stroke="black" points="4771.49,-444.89 4774.99,-454.89 4778.49,-444.89 4771.49,-444.89"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;HandoverRequestDomainModule -->
<g id="edge86" class="edge">
<title>DomainDatabaseModule&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M7130.11,-415.15C7130.11,-416.3 7130.11,-417 7130.11,-417 7130.11,-417 11878.84,-417 11878.84,-417 11878.84,-417 11878.84,-444.97 11878.84,-444.97"/>
<polygon fill="black" stroke="black" points="11875.34,-444.97 11878.84,-454.97 11882.34,-444.97 11875.34,-444.97"/>
</g>
<!-- DiscountDomainSagas -->
<g id="node39" class="node">
<title>DiscountDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="6858" cy="-397" rx="99.58" ry="18"/>
<text text-anchor="middle" x="6858" y="-392.8" font-family="Times,serif" font-size="14.00">DiscountDomainSagas</text>
</g>
<!-- DiscountDomainSagas&#45;&gt;DiscountDomainModule -->
<g id="edge44" class="edge">
<title>DiscountDomainSagas&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M6872.9,-415.01C6872.9,-431.05 6872.9,-452 6872.9,-452 6872.9,-452 5955.62,-452 5955.62,-452 5955.62,-452 5955.62,-452.29 5955.62,-452.29"/>
<polygon fill="black" stroke="black" points="5952.12,-444.92 5955.62,-454.92 5959.12,-444.92 5952.12,-444.92"/>
</g>
<!-- DiscountDomainService -->
<g id="node40" class="node">
<title>DiscountDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="6395" cy="-397" rx="105.94" ry="18"/>
<text text-anchor="middle" x="6395" y="-392.8" font-family="Times,serif" font-size="14.00">DiscountDomainService</text>
</g>
<!-- DiscountDomainService&#45;&gt;DiscountDomainModule -->
<g id="edge45" class="edge">
<title>DiscountDomainService&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M6294.96,-391C6230.66,-391 6159.1,-391 6159.1,-391 6159.1,-391 6159.1,-467 6159.1,-467 6159.1,-467 5970.64,-467 5970.64,-467"/>
<polygon fill="black" stroke="black" points="5970.64,-463.5 5960.64,-467 5970.64,-470.5 5970.64,-463.5"/>
</g>
<!-- DiscountDomainService&#45;&gt;PolicyDomainModule -->
<g id="edge205" class="edge">
<title>DiscountDomainService&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M6355.83,-413.77C6355.83,-424.34 6355.83,-436 6355.83,-436 6355.83,-436 4948.81,-436 4948.81,-436 4948.81,-436 4948.81,-444.93 4948.81,-444.93"/>
<polygon fill="black" stroke="black" points="4945.31,-444.93 4948.81,-454.93 4952.31,-444.93 4945.31,-444.93"/>
</g>
<!-- DiscountEventRepository -->
<g id="node41" class="node">
<title>DiscountEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="6630" cy="-397" rx="110.62" ry="18"/>
<text text-anchor="middle" x="6630" y="-392.8" font-family="Times,serif" font-size="14.00">DiscountEventRepository</text>
</g>
<!-- DiscountEventRepository&#45;&gt;DiscountDomainModule -->
<g id="edge46" class="edge">
<title>DiscountEventRepository&#45;&gt;DiscountDomainModule</title>
<path fill="none" stroke="black" d="M6625.57,-415.28C6625.57,-430.95 6625.57,-451 6625.57,-451 6625.57,-451 5953.13,-451 5953.13,-451 5953.13,-451 5953.13,-451.38 5953.13,-451.38"/>
<polygon fill="black" stroke="black" points="5949.63,-444.83 5953.13,-454.83 5956.63,-444.83 5949.63,-444.83"/>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;DiscountQuerySideModule -->
<g id="edge48" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M4532.31,-327C4593.01,-327 4662.68,-327 4662.68,-327 4662.68,-327 4662.68,-368.97 4662.68,-368.97"/>
<polygon fill="black" stroke="black" points="4659.18,-368.97 4662.68,-378.97 4666.18,-368.97 4659.18,-368.97"/>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge152" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M4532.13,-333C4575.34,-333 4618.21,-333 4618.21,-333 4618.21,-333 4618.21,-764 4618.21,-764 4618.21,-764 5046.01,-764 5046.01,-764"/>
<polygon fill="black" stroke="black" points="5046.01,-767.5 5056.01,-764 5046.01,-760.5 5046.01,-767.5"/>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;PolicyQuerySideModule -->
<g id="edge212" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M4514.06,-302.58C4514.06,-293.72 4514.06,-285 4514.06,-285 4514.06,-285 5379.34,-285 5379.34,-285 5379.34,-285 5379.34,-292.58 5379.34,-292.58"/>
<polygon fill="black" stroke="black" points="5375.84,-292.58 5379.34,-302.58 5382.84,-292.58 5375.84,-292.58"/>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge241" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M4492.18,-339.28C4492.18,-354.95 4492.18,-375 4492.18,-375 4492.18,-375 5021.4,-375 5021.4,-375 5021.4,-375 5021.4,-444.57 5021.4,-444.57"/>
<polygon fill="black" stroke="black" points="5017.9,-444.57 5021.4,-454.57 5024.9,-444.57 5017.9,-444.57"/>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;ProposalQuerySideModule -->
<g id="edge269" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M4472.23,-339.27C4472.23,-388.29 4472.23,-519 4472.23,-519 4472.23,-519 5791.17,-519 5791.17,-519 5791.17,-519 5791.17,-520.86 5791.17,-520.86"/>
<polygon fill="black" stroke="black" points="5787.67,-520.86 5791.17,-530.86 5794.67,-520.86 5787.67,-520.86"/>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;ScheduleQuerySideModule -->
<g id="edge291" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M4512.14,-339.25C4512.14,-353.18 4512.14,-370 4512.14,-370 4512.14,-370 5324.99,-370 5324.99,-370 5324.99,-370 5324.99,-370.88 5324.99,-370.88"/>
<polygon fill="black" stroke="black" points="5321.49,-368.84 5324.99,-378.84 5328.49,-368.84 5321.49,-368.84"/>
</g>
<!-- EmployeeQueryRepository  -->
<g id="node48" class="node">
<title>EmployeeQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="3402.18,-263 3229.82,-263 3229.82,-227 3402.18,-227 3402.18,-263"/>
<text text-anchor="middle" x="3316" y="-240.8" font-family="Times,serif" font-size="14.00">EmployeeQueryRepository </text>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;EmployeeQueryRepository  -->
<g id="edge61" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;EmployeeQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4371.39,-302.88C4371.39,-300.03 4371.39,-298 4371.39,-298 4371.39,-298 3372.96,-298 3372.96,-298 3372.96,-298 3372.96,-273.24 3372.96,-273.24"/>
<polygon fill="black" stroke="black" points="3376.47,-273.24 3372.96,-263.24 3369.47,-273.24 3376.47,-273.24"/>
</g>
<!-- EmployeeService  -->
<g id="node49" class="node">
<title>EmployeeService </title>
<polygon fill="#fb8072" stroke="black" points="3212.24,-263 3093.76,-263 3093.76,-227 3212.24,-227 3212.24,-263"/>
<text text-anchor="middle" x="3153" y="-240.8" font-family="Times,serif" font-size="14.00">EmployeeService </text>
</g>
<!-- EmployeeQuerySideModule&#45;&gt;EmployeeService  -->
<g id="edge62" class="edge">
<title>EmployeeQuerySideModule&#45;&gt;EmployeeService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4368.18,-302.92C4368.18,-301.15 4368.18,-300 4368.18,-300 4368.18,-300 3206.82,-300 3206.82,-300 3206.82,-300 3206.82,-273.01 3206.82,-273.01"/>
<polygon fill="black" stroke="black" points="3210.32,-273.01 3206.82,-263.01 3203.32,-273.01 3210.32,-273.01"/>
</g>
<!-- DiscountQueryRepository -->
<g id="node46" class="node">
<title>DiscountQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="3456" cy="-321" rx="112.32" ry="18"/>
<text text-anchor="middle" x="3456" y="-316.8" font-family="Times,serif" font-size="14.00">DiscountQueryRepository</text>
</g>
<!-- DiscountQueryRepository&#45;&gt;DiscountQuerySideModule -->
<g id="edge56" class="edge">
<title>DiscountQueryRepository&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M3522.25,-335.63C3522.25,-359.7 3522.25,-405 3522.25,-405 3522.25,-405 4647.64,-405 4647.64,-405"/>
<polygon fill="black" stroke="black" points="4647.64,-408.5 4657.64,-405 4647.64,-401.5 4647.64,-408.5"/>
</g>
<!-- DiscountQueryService -->
<g id="node47" class="node">
<title>DiscountQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="3685" cy="-321" rx="98.98" ry="18"/>
<text text-anchor="middle" x="3685" y="-316.8" font-family="Times,serif" font-size="14.00">DiscountQueryService</text>
</g>
<!-- DiscountQueryService&#45;&gt;DiscountQuerySideModule -->
<g id="edge57" class="edge">
<title>DiscountQueryService&#45;&gt;DiscountQuerySideModule</title>
<path fill="none" stroke="black" d="M3771.9,-329.76C3771.9,-350.32 3771.9,-400 3771.9,-400 3771.9,-400 4647.67,-400 4647.67,-400"/>
<polygon fill="black" stroke="black" points="4647.67,-403.5 4657.67,-400 4647.67,-396.5 4647.67,-403.5"/>
</g>
<!-- EmployeeQueryRepository -->
<g id="node50" class="node">
<title>EmployeeQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="2953" cy="-245" rx="116.9" ry="18"/>
<text text-anchor="middle" x="2953" y="-240.8" font-family="Times,serif" font-size="14.00">EmployeeQueryRepository</text>
</g>
<!-- EmployeeQueryRepository&#45;&gt;EmployeeQuerySideModule -->
<g id="edge63" class="edge">
<title>EmployeeQueryRepository&#45;&gt;EmployeeQuerySideModule</title>
<path fill="none" stroke="black" d="M2988.77,-262.41C2988.77,-279.17 2988.77,-302 2988.77,-302 2988.77,-302 4364.97,-302 4364.97,-302 4364.97,-302 4364.97,-302.08 4364.97,-302.08"/>
<polygon fill="black" stroke="black" points="4361.47,-292.82 4364.97,-302.82 4368.47,-292.82 4361.47,-292.82"/>
</g>
<!-- EmployeeService -->
<g id="node51" class="node">
<title>EmployeeService</title>
<ellipse fill="#fdb462" stroke="black" cx="2739" cy="-245" rx="79.29" ry="18"/>
<text text-anchor="middle" x="2739" y="-240.8" font-family="Times,serif" font-size="14.00">EmployeeService</text>
</g>
<!-- EmployeeService&#45;&gt;EmployeeQuerySideModule -->
<g id="edge64" class="edge">
<title>EmployeeService&#45;&gt;EmployeeQuerySideModule</title>
<path fill="none" stroke="black" d="M2754.1,-262.84C2754.1,-277.54 2754.1,-296 2754.1,-296 2754.1,-296 4428,-296 4428,-296 4428,-296 4428,-296.68 4428,-296.68"/>
<polygon fill="black" stroke="black" points="4424.5,-292.83 4428,-302.83 4431.5,-292.83 4424.5,-292.83"/>
</g>
<!-- HandoverDomainSagas -->
<g id="node52" class="node">
<title>HandoverDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="11193" cy="-473" rx="102.99" ry="18"/>
<text text-anchor="middle" x="11193" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverDomainSagas</text>
</g>
<!-- HandoverDomainSagas&#45;&gt;HandoverDomainModule -->
<g id="edge72" class="edge">
<title>HandoverDomainSagas&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M11193,-491.13C11193,-501.71 11193,-513 11193,-513 11193,-513 5520.56,-513 5520.56,-513 5520.56,-513 5520.56,-520.58 5520.56,-520.58"/>
<polygon fill="black" stroke="black" points="5517.06,-520.58 5520.56,-530.58 5524.06,-520.58 5517.06,-520.58"/>
</g>
<!-- HandoverDomainService -->
<g id="node53" class="node">
<title>HandoverDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="10963" cy="-473" rx="109.35" ry="18"/>
<text text-anchor="middle" x="10963" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverDomainService</text>
</g>
<!-- HandoverDomainService&#45;&gt;HandoverDomainModule -->
<g id="edge73" class="edge">
<title>HandoverDomainService&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M10963,-491.13C10963,-501.31 10963,-512 10963,-512 10963,-512 5516.55,-512 5516.55,-512 5516.55,-512 5516.55,-520.93 5516.55,-520.93"/>
<polygon fill="black" stroke="black" points="5513.05,-520.93 5516.55,-530.93 5520.05,-520.93 5513.05,-520.93"/>
</g>
<!-- HandoverEventRepository -->
<g id="node54" class="node">
<title>HandoverEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="10722" cy="-473" rx="114.03" ry="18"/>
<text text-anchor="middle" x="10722" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverEventRepository</text>
</g>
<!-- HandoverEventRepository&#45;&gt;HandoverDomainModule -->
<g id="edge74" class="edge">
<title>HandoverEventRepository&#45;&gt;HandoverDomainModule</title>
<path fill="none" stroke="black" d="M10722,-491.11C10722,-500.9 10722,-511 10722,-511 10722,-511 5512.53,-511 5512.53,-511 5512.53,-511 5512.53,-520.89 5512.53,-520.89"/>
<polygon fill="black" stroke="black" points="5509.03,-520.89 5512.53,-530.89 5516.03,-520.89 5509.03,-520.89"/>
</g>
<!-- HandoverQueryRepository -->
<g id="node58" class="node">
<title>HandoverQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="8685" cy="-473" rx="115.74" ry="18"/>
<text text-anchor="middle" x="8685" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverQueryRepository</text>
</g>
<!-- HandoverQueryRepository&#45;&gt;HandoverQuerySideModule -->
<g id="edge83" class="edge">
<title>HandoverQueryRepository&#45;&gt;HandoverQuerySideModule</title>
<path fill="none" stroke="black" d="M8685,-491.08C8685,-492.85 8685,-494 8685,-494 8685,-494 6080.93,-494 6080.93,-494 6080.93,-494 6080.93,-425.24 6080.93,-425.24"/>
<polygon fill="black" stroke="black" points="6084.43,-425.24 6080.93,-415.24 6077.43,-425.24 6084.43,-425.24"/>
</g>
<!-- HandoverQueryService -->
<g id="node59" class="node">
<title>HandoverQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="8449" cy="-473" rx="102.39" ry="18"/>
<text text-anchor="middle" x="8449" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverQueryService</text>
</g>
<!-- HandoverQueryService&#45;&gt;HandoverQuerySideModule -->
<g id="edge84" class="edge">
<title>HandoverQueryService&#45;&gt;HandoverQuerySideModule</title>
<path fill="none" stroke="black" d="M8405.77,-489.52C8405.77,-491.59 8405.77,-493 8405.77,-493 8405.77,-493 6098.12,-493 6098.12,-493 6098.12,-493 6098.12,-425.06 6098.12,-425.06"/>
<polygon fill="black" stroke="black" points="6101.62,-425.06 6098.12,-415.06 6094.62,-425.06 6101.62,-425.06"/>
</g>
<!-- HandoverRequestDomainSagas -->
<g id="node61" class="node">
<title>HandoverRequestDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="12534" cy="-397" rx="133.71" ry="18"/>
<text text-anchor="middle" x="12534" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverRequestDomainSagas</text>
</g>
<!-- HandoverRequestDomainSagas&#45;&gt;HandoverRequestDomainModule -->
<g id="edge90" class="edge">
<title>HandoverRequestDomainSagas&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M12534,-415.2C12534,-439.36 12534,-479 12534,-479 12534,-479 12025.55,-479 12025.55,-479"/>
<polygon fill="black" stroke="black" points="12025.55,-475.5 12015.55,-479 12025.55,-482.5 12025.55,-475.5"/>
</g>
<!-- HandoverRequestDomainService -->
<g id="node62" class="node">
<title>HandoverRequestDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="12242" cy="-397" rx="140.07" ry="18"/>
<text text-anchor="middle" x="12242" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverRequestDomainService</text>
</g>
<!-- HandoverRequestDomainService&#45;&gt;HandoverRequestDomainModule -->
<g id="edge91" class="edge">
<title>HandoverRequestDomainService&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M12242,-415.03C12242,-435.77 12242,-467 12242,-467 12242,-467 12025.74,-467 12025.74,-467"/>
<polygon fill="black" stroke="black" points="12025.74,-463.5 12015.74,-467 12025.74,-470.5 12025.74,-463.5"/>
</g>
<!-- HandoverRequestEventRepository -->
<g id="node63" class="node">
<title>HandoverRequestEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="11939" cy="-397" rx="145.25" ry="18"/>
<text text-anchor="middle" x="11939" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverRequestEventRepository</text>
</g>
<!-- HandoverRequestEventRepository&#45;&gt;HandoverRequestDomainModule -->
<g id="edge92" class="edge">
<title>HandoverRequestEventRepository&#45;&gt;HandoverRequestDomainModule</title>
<path fill="none" stroke="black" d="M11947.16,-415.01C11947.16,-415.01 11947.16,-444.85 11947.16,-444.85"/>
<polygon fill="black" stroke="black" points="11943.66,-444.85 11947.16,-454.85 11950.66,-444.85 11943.66,-444.85"/>
</g>
<!-- HandoverRequestQueryRepository  -->
<g id="node65" class="node">
<title>HandoverRequestQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="11298.22,-415 11081.78,-415 11081.78,-379 11298.22,-379 11298.22,-415"/>
<text text-anchor="middle" x="11190" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverRequestQueryRepository </text>
</g>
<!-- HandoverRequestQuerySideModule&#45;&gt;HandoverRequestQueryRepository  -->
<g id="edge97" class="edge">
<title>HandoverRequestQuerySideModule&#45;&gt;HandoverRequestQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11318.19,-336C11265.39,-336 11213.4,-336 11213.4,-336 11213.4,-336 11213.4,-368.7 11213.4,-368.7"/>
<polygon fill="black" stroke="black" points="11209.9,-368.7 11213.4,-378.7 11216.9,-368.7 11209.9,-368.7"/>
</g>
<!-- HandoverRequestQueryService  -->
<g id="node66" class="node">
<title>HandoverRequestQueryService </title>
<polygon fill="#fb8072" stroke="black" points="11754.26,-415 11557.74,-415 11557.74,-379 11754.26,-379 11754.26,-415"/>
<text text-anchor="middle" x="11656" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverRequestQueryService </text>
</g>
<!-- HandoverRequestQuerySideModule&#45;&gt;HandoverRequestQueryService  -->
<g id="edge98" class="edge">
<title>HandoverRequestQuerySideModule&#45;&gt;HandoverRequestQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11537.86,-330C11586.18,-330 11632.17,-330 11632.17,-330 11632.17,-330 11632.17,-368.69 11632.17,-368.69"/>
<polygon fill="black" stroke="black" points="11628.67,-368.69 11632.17,-378.69 11635.67,-368.69 11628.67,-368.69"/>
</g>
<!-- HandoverRequestQuerySideModule  -->
<g id="node67" class="node">
<title>HandoverRequestQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="11539.72,-415 11316.28,-415 11316.28,-379 11539.72,-379 11539.72,-415"/>
<text text-anchor="middle" x="11428" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverRequestQuerySideModule </text>
</g>
<!-- HandoverRequestQuerySideModule&#45;&gt;HandoverRequestQuerySideModule  -->
<g id="edge99" class="edge">
<title>HandoverRequestQuerySideModule&#45;&gt;HandoverRequestQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11428,-339.01C11428,-339.01 11428,-368.85 11428,-368.85"/>
<polygon fill="black" stroke="black" points="11424.5,-368.85 11428,-378.85 11431.5,-368.85 11424.5,-368.85"/>
</g>
<!-- HandoverRequestQueryRepository -->
<g id="node68" class="node">
<title>HandoverRequestQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="11560" cy="-245" rx="146.96" ry="18"/>
<text text-anchor="middle" x="11560" y="-240.8" font-family="Times,serif" font-size="14.00">HandoverRequestQueryRepository</text>
</g>
<!-- HandoverRequestQueryRepository&#45;&gt;HandoverRequestQuerySideModule -->
<g id="edge100" class="edge">
<title>HandoverRequestQueryRepository&#45;&gt;HandoverRequestQuerySideModule</title>
<path fill="none" stroke="black" d="M11475.57,-260.02C11475.57,-260.02 11475.57,-292.8 11475.57,-292.8"/>
<polygon fill="black" stroke="black" points="11472.07,-292.8 11475.57,-302.8 11479.07,-292.8 11472.07,-292.8"/>
</g>
<!-- HandoverRequestQueryService -->
<g id="node69" class="node">
<title>HandoverRequestQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="11262" cy="-245" rx="133.11" ry="18"/>
<text text-anchor="middle" x="11262" y="-240.8" font-family="Times,serif" font-size="14.00">HandoverRequestQueryService</text>
</g>
<!-- HandoverRequestQueryService&#45;&gt;HandoverRequestQuerySideModule -->
<g id="edge101" class="edge">
<title>HandoverRequestQueryService&#45;&gt;HandoverRequestQuerySideModule</title>
<path fill="none" stroke="black" d="M11369.58,-255.89C11369.58,-255.89 11369.58,-292.79 11369.58,-292.79"/>
<polygon fill="black" stroke="black" points="11366.08,-292.79 11369.58,-302.79 11373.08,-292.79 11366.08,-292.79"/>
</g>
<!-- HandoverScheduleDomainSagas -->
<g id="node71" class="node">
<title>HandoverScheduleDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="10192" cy="-473" rx="138.29" ry="18"/>
<text text-anchor="middle" x="10192" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverScheduleDomainSagas</text>
</g>
<!-- HandoverScheduleDomainSagas&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge110" class="edge">
<title>HandoverScheduleDomainSagas&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M10106.02,-458.78C10106.02,-452.71 10106.02,-447 10106.02,-447 10106.02,-447 5351.6,-447 5351.6,-447 5351.6,-447 5351.6,-520.76 5351.6,-520.76"/>
<polygon fill="black" stroke="black" points="5348.1,-520.76 5351.6,-530.76 5355.1,-520.76 5348.1,-520.76"/>
</g>
<!-- HandoverScheduleDomainService -->
<g id="node72" class="node">
<title>HandoverScheduleDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="9891" cy="-473" rx="144.65" ry="18"/>
<text text-anchor="middle" x="9891" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverScheduleDomainService</text>
</g>
<!-- HandoverScheduleDomainService&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge111" class="edge">
<title>HandoverScheduleDomainService&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M9891,-491.07C9891,-500.46 9891,-510 9891,-510 9891,-510 5366.92,-510 5366.92,-510 5366.92,-510 5366.92,-520.87 5366.92,-520.87"/>
<polygon fill="black" stroke="black" points="5363.42,-520.87 5366.92,-530.87 5370.42,-520.87 5363.42,-520.87"/>
</g>
<!-- HandoverScheduleEventRepository -->
<g id="node73" class="node">
<title>HandoverScheduleEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="9579" cy="-473" rx="149.33" ry="18"/>
<text text-anchor="middle" x="9579" y="-468.8" font-family="Times,serif" font-size="14.00">HandoverScheduleEventRepository</text>
</g>
<!-- HandoverScheduleEventRepository&#45;&gt;HandoverScheduleDomainModule -->
<g id="edge112" class="edge">
<title>HandoverScheduleEventRepository&#45;&gt;HandoverScheduleDomainModule</title>
<path fill="none" stroke="black" d="M9482.97,-459.04C9482.97,-453.31 9482.97,-448 9482.97,-448 9482.97,-448 5359.26,-448 5359.26,-448 5359.26,-448 5359.26,-520.94 5359.26,-520.94"/>
<polygon fill="black" stroke="black" points="5355.76,-520.94 5359.26,-530.94 5362.76,-520.94 5355.76,-520.94"/>
</g>
<!-- HandoverScheduleQueryRepository -->
<g id="node77" class="node">
<title>HandoverScheduleQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="7879" cy="-397" rx="151.03" ry="18"/>
<text text-anchor="middle" x="7879" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverScheduleQueryRepository</text>
</g>
<!-- HandoverScheduleQueryRepository&#45;&gt;HandoverScheduleQuerySideModule -->
<g id="edge120" class="edge">
<title>HandoverScheduleQueryRepository&#45;&gt;HandoverScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M7879,-378.9C7879,-367.91 7879,-356 7879,-356 7879,-356 5803.32,-356 5803.32,-356 5803.32,-356 5803.32,-349.32 5803.32,-349.32"/>
<polygon fill="black" stroke="black" points="5806.82,-349.32 5803.32,-339.32 5799.82,-349.32 5806.82,-349.32"/>
</g>
<!-- HandoverScheduleQueryService -->
<g id="node78" class="node">
<title>HandoverScheduleQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="7572" cy="-397" rx="137.69" ry="18"/>
<text text-anchor="middle" x="7572" y="-392.8" font-family="Times,serif" font-size="14.00">HandoverScheduleQueryService</text>
</g>
<!-- HandoverScheduleQueryService&#45;&gt;HandoverScheduleQuerySideModule -->
<g id="edge121" class="edge">
<title>HandoverScheduleQueryService&#45;&gt;HandoverScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M7572,-378.87C7572,-368.69 7572,-358 7572,-358 7572,-358 5793.38,-358 5793.38,-358 5793.38,-358 5793.38,-349.07 5793.38,-349.07"/>
<polygon fill="black" stroke="black" points="5796.89,-349.07 5793.38,-339.07 5789.89,-349.07 5796.89,-349.07"/>
</g>
<!-- HistoryImportQueryRepository -->
<g id="node82" class="node">
<title>HistoryImportQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="6422" cy="-625" rx="132.59" ry="18"/>
<text text-anchor="middle" x="6422" y="-620.8" font-family="Times,serif" font-size="14.00">HistoryImportQueryRepository</text>
</g>
<!-- HistoryImportQueryRepository&#45;&gt;HistoryImportQuerySideModule -->
<g id="edge127" class="edge">
<title>HistoryImportQueryRepository&#45;&gt;HistoryImportQuerySideModule</title>
<path fill="none" stroke="black" d="M6299.55,-618C6104.69,-618 5745.82,-618 5745.82,-618 5745.82,-618 5745.82,-577.13 5745.82,-577.13"/>
<polygon fill="black" stroke="black" points="5749.32,-577.13 5745.82,-567.13 5742.32,-577.13 5749.32,-577.13"/>
</g>
<!-- HistoryImportQueryService -->
<g id="node83" class="node">
<title>HistoryImportQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="6692" cy="-625" rx="119.24" ry="18"/>
<text text-anchor="middle" x="6692" y="-620.8" font-family="Times,serif" font-size="14.00">HistoryImportQueryService</text>
</g>
<!-- HistoryImportQueryService&#45;&gt;HistoryImportQuerySideModule -->
<g id="edge128" class="edge">
<title>HistoryImportQueryService&#45;&gt;HistoryImportQuerySideModule</title>
<path fill="none" stroke="black" d="M6652.29,-607.81C6652.29,-603.97 6652.29,-601 6652.29,-601 6652.29,-601 5671,-601 5671,-601 5671,-601 5671,-577.19 5671,-577.19"/>
<polygon fill="black" stroke="black" points="5674.5,-577.19 5671,-567.19 5667.5,-577.19 5674.5,-577.19"/>
</g>
<!-- LiquidationDomainSagas -->
<g id="node85" class="node">
<title>LiquidationDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="3744" cy="-625" rx="109.97" ry="18"/>
<text text-anchor="middle" x="3744" y="-620.8" font-family="Times,serif" font-size="14.00">LiquidationDomainSagas</text>
</g>
<!-- LiquidationDomainSagas&#45;&gt;LiquidationDomainModule -->
<g id="edge138" class="edge">
<title>LiquidationDomainSagas&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M3744,-643.06C3744,-654.44 3744,-667 3744,-667 3744,-667 4878.34,-667 4878.34,-667 4878.34,-667 4878.34,-667.49 4878.34,-667.49"/>
<polygon fill="black" stroke="black" points="4874.84,-661.88 4878.34,-671.88 4881.84,-661.88 4874.84,-661.88"/>
</g>
<!-- LiquidationDomainService -->
<g id="node86" class="node">
<title>LiquidationDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="4248" cy="-625" rx="116.33" ry="18"/>
<text text-anchor="middle" x="4248" y="-620.8" font-family="Times,serif" font-size="14.00">LiquidationDomainService</text>
</g>
<!-- LiquidationDomainService&#45;&gt;LiquidationDomainModule -->
<g id="edge139" class="edge">
<title>LiquidationDomainService&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M4248,-643.14C4248,-649.46 4248,-655 4248,-655 4248,-655 4935,-655 4935,-655 4935,-655 4935,-661.68 4935,-661.68"/>
<polygon fill="black" stroke="black" points="4931.5,-661.68 4935,-671.68 4938.5,-661.68 4931.5,-661.68"/>
</g>
<!-- LiquidationEventRepository -->
<g id="node87" class="node">
<title>LiquidationEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="3993" cy="-625" rx="121.01" ry="18"/>
<text text-anchor="middle" x="3993" y="-620.8" font-family="Times,serif" font-size="14.00">LiquidationEventRepository</text>
</g>
<!-- LiquidationEventRepository&#45;&gt;LiquidationDomainModule -->
<g id="edge140" class="edge">
<title>LiquidationEventRepository&#45;&gt;LiquidationDomainModule</title>
<path fill="none" stroke="black" d="M3993,-643C3993,-652 3993,-661 3993,-661 3993,-661 4906.67,-661 4906.67,-661 4906.67,-661 4906.67,-662.08 4906.67,-662.08"/>
<polygon fill="black" stroke="black" points="4903.17,-661.8 4906.67,-671.8 4910.17,-661.8 4903.17,-661.8"/>
</g>
<!-- FileGenerationService -->
<g id="node91" class="node">
<title>FileGenerationService</title>
<ellipse fill="#fdb462" stroke="black" cx="9824" cy="-397" rx="97.83" ry="18"/>
<text text-anchor="middle" x="9824" y="-392.8" font-family="Times,serif" font-size="14.00">FileGenerationService</text>
</g>
<!-- FileGenerationService&#45;&gt;LiquidationQuerySideModule -->
<g id="edge149" class="edge">
<title>FileGenerationService&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M9824,-378.72C9824,-357.68 9824,-326 9824,-326 9824,-326 6125.54,-326 6125.54,-326"/>
<polygon fill="black" stroke="black" points="6125.54,-322.5 6115.54,-326 6125.54,-329.5 6125.54,-322.5"/>
</g>
<!-- FileGenerationService&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge254" class="edge">
<title>FileGenerationService&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M9734.42,-404.49C9734.42,-416.81 9734.42,-440 9734.42,-440 9734.42,-440 5211.45,-440 5211.45,-440 5211.45,-440 5211.45,-444.96 5211.45,-444.96"/>
<polygon fill="black" stroke="black" points="5207.95,-444.96 5211.45,-454.96 5214.95,-444.96 5207.95,-444.96"/>
</g>
<!-- FileGenerationService&#45;&gt;ProposalQuerySideModule -->
<g id="edge277" class="edge">
<title>FileGenerationService&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M9740.42,-406.55C9740.42,-437.96 9740.42,-536 9740.42,-536 9740.42,-536 5967.37,-536 5967.37,-536"/>
<polygon fill="black" stroke="black" points="5967.37,-532.5 5957.37,-536 5967.37,-539.5 5967.37,-532.5"/>
</g>
<!-- LiquidationQueryRepository -->
<g id="node92" class="node">
<title>LiquidationQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="10299" cy="-397" rx="122.72" ry="18"/>
<text text-anchor="middle" x="10299" y="-392.8" font-family="Times,serif" font-size="14.00">LiquidationQueryRepository</text>
</g>
<!-- LiquidationQueryRepository&#45;&gt;LiquidationQuerySideModule -->
<g id="edge150" class="edge">
<title>LiquidationQueryRepository&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M10299,-378.75C10299,-355.98 10299,-320 10299,-320 10299,-320 6125.29,-320 6125.29,-320"/>
<polygon fill="black" stroke="black" points="6125.29,-316.5 6115.29,-320 6125.29,-323.5 6125.29,-316.5"/>
</g>
<!-- LiquidationQueryService -->
<g id="node93" class="node">
<title>LiquidationQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="10049" cy="-397" rx="109.37" ry="18"/>
<text text-anchor="middle" x="10049" y="-392.8" font-family="Times,serif" font-size="14.00">LiquidationQueryService</text>
</g>
<!-- LiquidationQueryService&#45;&gt;LiquidationQuerySideModule -->
<g id="edge151" class="edge">
<title>LiquidationQueryService&#45;&gt;LiquidationQuerySideModule</title>
<path fill="none" stroke="black" d="M10049,-378.71C10049,-356.79 10049,-323 10049,-323 10049,-323 6125.62,-323 6125.62,-323"/>
<polygon fill="black" stroke="black" points="6125.62,-319.5 6115.62,-323 6125.62,-326.5 6125.62,-319.5"/>
</g>
<!-- MsxLoggerService -->
<g id="node96" class="node">
<title>MsxLoggerService</title>
<ellipse fill="#fdb462" stroke="black" cx="11879" cy="-245" rx="85.09" ry="18"/>
<text text-anchor="middle" x="11879" y="-240.8" font-family="Times,serif" font-size="14.00">MsxLoggerService</text>
</g>
<!-- MsxLoggerService&#45;&gt;LoggerModule -->
<g id="edge167" class="edge">
<title>MsxLoggerService&#45;&gt;LoggerModule</title>
<path fill="none" stroke="black" d="M11879,-226.97C11879,-201.49 11879,-158 11879,-158 11879,-158 5519.28,-158 5519.28,-158"/>
<polygon fill="black" stroke="black" points="5519.28,-154.5 5509.28,-158 5519.28,-161.5 5519.28,-154.5"/>
</g>
<!-- CareClient -->
<g id="node112" class="node">
<title>CareClient</title>
<ellipse fill="#fdb462" stroke="black" cx="603" cy="-169" rx="52.74" ry="18"/>
<text text-anchor="middle" x="603" y="-164.8" font-family="Times,serif" font-size="14.00">CareClient</text>
</g>
<!-- CareClient&#45;&gt;MgsSenderModule -->
<g id="edge183" class="edge">
<title>CareClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M603,-150.62C603,-126.72 603,-88 603,-88 603,-88 4628.1,-88 4628.1,-88"/>
<polygon fill="black" stroke="black" points="4628.1,-91.5 4638.1,-88 4628.1,-84.5 4628.1,-91.5"/>
</g>
<!-- CustomerClient -->
<g id="node113" class="node">
<title>CustomerClient</title>
<ellipse fill="#fdb462" stroke="black" cx="2740" cy="-169" rx="72.39" ry="18"/>
<text text-anchor="middle" x="2740" y="-164.8" font-family="Times,serif" font-size="14.00">CustomerClient</text>
</g>
<!-- CustomerClient&#45;&gt;MgsSenderModule -->
<g id="edge184" class="edge">
<title>CustomerClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M2740,-150.89C2740,-131.66 2740,-104 2740,-104 2740,-104 4628.1,-104 4628.1,-104"/>
<polygon fill="black" stroke="black" points="4628.1,-107.5 4638.1,-104 4628.1,-100.5 4628.1,-107.5"/>
</g>
<!-- EmployeeClient -->
<g id="node114" class="node">
<title>EmployeeClient</title>
<ellipse fill="#fdb462" stroke="black" cx="2576" cy="-169" rx="73.59" ry="18"/>
<text text-anchor="middle" x="2576" y="-164.8" font-family="Times,serif" font-size="14.00">EmployeeClient</text>
</g>
<!-- EmployeeClient&#45;&gt;MgsSenderModule -->
<g id="edge185" class="edge">
<title>EmployeeClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M2576,-150.97C2576,-131.42 2576,-103 2576,-103 2576,-103 4628.15,-103 4628.15,-103"/>
<polygon fill="black" stroke="black" points="4628.15,-106.5 4638.15,-103 4628.15,-99.5 4628.15,-106.5"/>
</g>
<!-- LoggerClient -->
<g id="node115" class="node">
<title>LoggerClient</title>
<ellipse fill="#fdb462" stroke="black" cx="2422" cy="-169" rx="62.56" ry="18"/>
<text text-anchor="middle" x="2422" y="-164.8" font-family="Times,serif" font-size="14.00">LoggerClient</text>
</g>
<!-- LoggerClient&#45;&gt;MgsSenderModule -->
<g id="edge186" class="edge">
<title>LoggerClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M2422,-150.69C2422,-130.85 2422,-102 2422,-102 2422,-102 4628.18,-102 4628.18,-102"/>
<polygon fill="black" stroke="black" points="4628.18,-105.5 4638.18,-102 4628.18,-98.5 4628.18,-105.5"/>
</g>
<!-- MailerClient -->
<g id="node116" class="node">
<title>MailerClient</title>
<ellipse fill="#fdb462" stroke="black" cx="2281" cy="-169" rx="60.27" ry="18"/>
<text text-anchor="middle" x="2281" y="-164.8" font-family="Times,serif" font-size="14.00">MailerClient</text>
</g>
<!-- MailerClient&#45;&gt;MgsSenderModule -->
<g id="edge187" class="edge">
<title>MailerClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M2281,-150.78C2281,-130.63 2281,-101 2281,-101 2281,-101 4628.08,-101 4628.08,-101"/>
<polygon fill="black" stroke="black" points="4628.08,-104.5 4638.08,-101 4628.08,-97.5 4628.08,-104.5"/>
</g>
<!-- MsgSenderService -->
<g id="node117" class="node">
<title>MsgSenderService</title>
<ellipse fill="#fdb462" stroke="black" cx="2119" cy="-169" rx="83.95" ry="18"/>
<text text-anchor="middle" x="2119" y="-164.8" font-family="Times,serif" font-size="14.00">MsgSenderService</text>
</g>
<!-- MsgSenderService&#45;&gt;MgsSenderModule -->
<g id="edge188" class="edge">
<title>MsgSenderService&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M2119,-150.87C2119,-130.43 2119,-100 2119,-100 2119,-100 4628.11,-100 4628.11,-100"/>
<polygon fill="black" stroke="black" points="4628.11,-103.5 4638.11,-100 4628.11,-96.5 4628.11,-103.5"/>
</g>
<!-- NotificationClient -->
<g id="node118" class="node">
<title>NotificationClient</title>
<ellipse fill="#fdb462" stroke="black" cx="1936" cy="-169" rx="81.11" ry="18"/>
<text text-anchor="middle" x="1936" y="-164.8" font-family="Times,serif" font-size="14.00">NotificationClient</text>
</g>
<!-- NotificationClient&#45;&gt;MgsSenderModule -->
<g id="edge189" class="edge">
<title>NotificationClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M1936,-150.72C1936,-129.68 1936,-98 1936,-98 1936,-98 4628.07,-98 4628.07,-98"/>
<polygon fill="black" stroke="black" points="4628.07,-101.5 4638.07,-98 4628.07,-94.5 4628.07,-101.5"/>
</g>
<!-- NotifierClient -->
<g id="node119" class="node">
<title>NotifierClient</title>
<ellipse fill="#fdb462" stroke="black" cx="1771" cy="-169" rx="65.42" ry="18"/>
<text text-anchor="middle" x="1771" y="-164.8" font-family="Times,serif" font-size="14.00">NotifierClient</text>
</g>
<!-- NotifierClient&#45;&gt;MgsSenderModule -->
<g id="edge190" class="edge">
<title>NotifierClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M1771,-150.83C1771,-129.5 1771,-97 1771,-97 1771,-97 4628.15,-97 4628.15,-97"/>
<polygon fill="black" stroke="black" points="4628.15,-100.5 4638.15,-97 4628.15,-93.5 4628.15,-100.5"/>
</g>
<!-- OrgchartClient -->
<g id="node120" class="node">
<title>OrgchartClient</title>
<ellipse fill="#fdb462" stroke="black" cx="1619" cy="-169" rx="68.92" ry="18"/>
<text text-anchor="middle" x="1619" y="-164.8" font-family="Times,serif" font-size="14.00">OrgchartClient</text>
</g>
<!-- OrgchartClient&#45;&gt;MgsSenderModule -->
<g id="edge191" class="edge">
<title>OrgchartClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M1619,-150.96C1619,-129.34 1619,-96 1619,-96 1619,-96 4628.11,-96 4628.11,-96"/>
<polygon fill="black" stroke="black" points="4628.11,-99.5 4638.11,-96 4628.11,-92.5 4628.11,-99.5"/>
</g>
<!-- PropertyClient -->
<g id="node121" class="node">
<title>PropertyClient</title>
<ellipse fill="#fdb462" stroke="black" cx="1464" cy="-169" rx="67.79" ry="18"/>
<text text-anchor="middle" x="1464" y="-164.8" font-family="Times,serif" font-size="14.00">PropertyClient</text>
</g>
<!-- PropertyClient&#45;&gt;MgsSenderModule -->
<g id="edge192" class="edge">
<title>PropertyClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M1464,-150.71C1464,-128.79 1464,-95 1464,-95 1464,-95 4628.25,-95 4628.25,-95"/>
<polygon fill="black" stroke="black" points="4628.25,-98.5 4638.25,-95 4628.25,-91.5 4628.25,-98.5"/>
</g>
<!-- SocialClient -->
<g id="node122" class="node">
<title>SocialClient</title>
<ellipse fill="#fdb462" stroke="black" cx="1319" cy="-169" rx="58.56" ry="18"/>
<text text-anchor="middle" x="1319" y="-164.8" font-family="Times,serif" font-size="14.00">SocialClient</text>
</g>
<!-- SocialClient&#45;&gt;MgsSenderModule -->
<g id="edge193" class="edge">
<title>SocialClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M1319,-150.84C1319,-128.65 1319,-94 1319,-94 1319,-94 4628.06,-94 4628.06,-94"/>
<polygon fill="black" stroke="black" points="4628.06,-97.5 4638.06,-94 4628.06,-90.5 4628.06,-97.5"/>
</g>
<!-- StsClient -->
<g id="node123" class="node">
<title>StsClient</title>
<ellipse fill="#fdb462" stroke="black" cx="1196" cy="-169" rx="46.4" ry="18"/>
<text text-anchor="middle" x="1196" y="-164.8" font-family="Times,serif" font-size="14.00">StsClient</text>
</g>
<!-- StsClient&#45;&gt;MgsSenderModule -->
<g id="edge194" class="edge">
<title>StsClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M1196,-150.75C1196,-127.98 1196,-92 1196,-92 1196,-92 4628.07,-92 4628.07,-92"/>
<polygon fill="black" stroke="black" points="4628.07,-95.5 4638.07,-92 4628.07,-88.5 4628.07,-95.5"/>
</g>
<!-- SyncErpClient -->
<g id="node124" class="node">
<title>SyncErpClient</title>
<ellipse fill="#fdb462" stroke="black" cx="1064" cy="-169" rx="67.8" ry="18"/>
<text text-anchor="middle" x="1064" y="-164.8" font-family="Times,serif" font-size="14.00">SyncErpClient</text>
</g>
<!-- SyncErpClient&#45;&gt;MgsSenderModule -->
<g id="edge195" class="edge">
<title>SyncErpClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M1064,-150.91C1064,-127.87 1064,-91 1064,-91 1064,-91 4628.04,-91 4628.04,-91"/>
<polygon fill="black" stroke="black" points="4628.04,-94.5 4638.04,-91 4628.04,-87.5 4628.04,-94.5"/>
</g>
<!-- TransactionClient -->
<g id="node125" class="node">
<title>TransactionClient</title>
<ellipse fill="#fdb462" stroke="black" cx="898" cy="-169" rx="80.45" ry="18"/>
<text text-anchor="middle" x="898" y="-164.8" font-family="Times,serif" font-size="14.00">TransactionClient</text>
</g>
<!-- TransactionClient&#45;&gt;MgsSenderModule -->
<g id="edge196" class="edge">
<title>TransactionClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M898,-150.68C898,-127.34 898,-90 898,-90 898,-90 4627.96,-90 4627.96,-90"/>
<polygon fill="black" stroke="black" points="4627.96,-93.5 4637.96,-90 4627.96,-86.5 4627.96,-93.5"/>
</g>
<!-- UploadClient -->
<g id="node126" class="node">
<title>UploadClient</title>
<ellipse fill="#fdb462" stroke="black" cx="737" cy="-169" rx="63.14" ry="18"/>
<text text-anchor="middle" x="737" y="-164.8" font-family="Times,serif" font-size="14.00">UploadClient</text>
</g>
<!-- UploadClient&#45;&gt;MgsSenderModule -->
<g id="edge197" class="edge">
<title>UploadClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M737,-150.85C737,-127.24 737,-89 737,-89 737,-89 4628.15,-89 4628.15,-89"/>
<polygon fill="black" stroke="black" points="4628.15,-92.5 4638.15,-89 4628.15,-85.5 4628.15,-92.5"/>
</g>
<!-- PolicyDomainSagas -->
<g id="node127" class="node">
<title>PolicyDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="1829" cy="-397" rx="89.21" ry="18"/>
<text text-anchor="middle" x="1829" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyDomainSagas</text>
</g>
<!-- PolicyDomainSagas&#45;&gt;PolicyDomainModule -->
<g id="edge206" class="edge">
<title>PolicyDomainSagas&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M1836.94,-415.24C1836.94,-422.43 1836.94,-429 1836.94,-429 1836.94,-429 4866.89,-429 4866.89,-429 4866.89,-429 4866.89,-444.85 4866.89,-444.85"/>
<polygon fill="black" stroke="black" points="4863.39,-444.85 4866.89,-454.85 4870.39,-444.85 4863.39,-444.85"/>
</g>
<!-- PolicyDomainService -->
<g id="node128" class="node">
<title>PolicyDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="1626" cy="-397" rx="95.56" ry="18"/>
<text text-anchor="middle" x="1626" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyDomainService</text>
</g>
<!-- PolicyDomainService&#45;&gt;PolicyDomainModule -->
<g id="edge207" class="edge">
<title>PolicyDomainService&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M1713.79,-389.71C1713.79,-384.35 1713.79,-378 1713.79,-378 1713.79,-378 4878.59,-378 4878.59,-378 4878.59,-378 4878.59,-444.68 4878.59,-444.68"/>
<polygon fill="black" stroke="black" points="4875.09,-444.68 4878.59,-454.68 4882.09,-444.68 4875.09,-444.68"/>
</g>
<!-- PolicyEventRepository -->
<g id="node129" class="node">
<title>PolicyEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="1412" cy="-397" rx="100.74" ry="18"/>
<text text-anchor="middle" x="1412" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyEventRepository</text>
</g>
<!-- PolicyEventRepository&#45;&gt;PolicyDomainModule -->
<g id="edge208" class="edge">
<title>PolicyEventRepository&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M1484.66,-409.66C1484.66,-418.97 1484.66,-430 1484.66,-430 1484.66,-430 4855.19,-430 4855.19,-430 4855.19,-430 4855.19,-444.51 4855.19,-444.51"/>
<polygon fill="black" stroke="black" points="4851.69,-444.51 4855.19,-454.51 4858.69,-444.51 4851.69,-444.51"/>
</g>
<!-- ScheduleDomainService -->
<g id="node130" class="node">
<title>ScheduleDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="1187" cy="-397" rx="106.5" ry="18"/>
<text text-anchor="middle" x="1187" y="-392.8" font-family="Times,serif" font-size="14.00">ScheduleDomainService</text>
</g>
<!-- ScheduleDomainService&#45;&gt;PolicyDomainModule -->
<g id="edge209" class="edge">
<title>ScheduleDomainService&#45;&gt;PolicyDomainModule</title>
<path fill="none" stroke="black" d="M1244.43,-412.41C1244.43,-421.4 1244.43,-431 1244.43,-431 1244.43,-431 4843.49,-431 4843.49,-431 4843.49,-431 4843.49,-444.94 4843.49,-444.94"/>
<polygon fill="black" stroke="black" points="4839.99,-444.94 4843.49,-454.94 4846.99,-444.94 4839.99,-444.94"/>
</g>
<!-- ScheduleDomainService&#45;&gt;ScheduleDomainModule -->
<g id="edge287" class="edge">
<title>ScheduleDomainService&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M1268.97,-408.7C1268.97,-428.39 1268.97,-466 1268.97,-466 1268.97,-466 4647.9,-466 4647.9,-466"/>
<polygon fill="black" stroke="black" points="4647.9,-469.5 4657.9,-466 4647.9,-462.5 4647.9,-469.5"/>
</g>
<!-- PolicyQueryRepository -->
<g id="node134" class="node">
<title>PolicyQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="9082" cy="-397" rx="102.45" ry="18"/>
<text text-anchor="middle" x="9082" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyQueryRepository</text>
</g>
<!-- PolicyQueryRepository&#45;&gt;PolicyQuerySideModule -->
<g id="edge219" class="edge">
<title>PolicyQueryRepository&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M9082,-378.95C9082,-364.63 9082,-347 9082,-347 9082,-347 5496.46,-347 5496.46,-347 5496.46,-347 5496.46,-346.21 5496.46,-346.21"/>
<polygon fill="black" stroke="black" points="5499.96,-349.06 5496.46,-339.06 5492.96,-349.06 5499.96,-349.06"/>
</g>
<!-- PolicyQueryService -->
<g id="node135" class="node">
<title>PolicyQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="8873" cy="-397" rx="88.6" ry="18"/>
<text text-anchor="middle" x="8873" y="-392.8" font-family="Times,serif" font-size="14.00">PolicyQueryService</text>
</g>
<!-- PolicyQueryService&#45;&gt;PolicyQuerySideModule -->
<g id="edge220" class="edge">
<title>PolicyQueryService&#45;&gt;PolicyQuerySideModule</title>
<path fill="none" stroke="black" d="M8873,-378.85C8873,-365.26 8873,-349 8873,-349 8873,-349 5492.44,-349 5492.44,-349 5492.44,-349 5492.44,-348.02 5492.44,-348.02"/>
<polygon fill="black" stroke="black" points="5495.94,-349.21 5492.44,-339.21 5488.94,-349.21 5495.94,-349.21"/>
</g>
<!-- PrimaryContractCustomerDomainService -->
<g id="node138" class="node">
<title>PrimaryContractCustomerDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="2493" cy="-549" rx="173.03" ry="18"/>
<text text-anchor="middle" x="2493" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractCustomerDomainService</text>
</g>
<!-- PrimaryContractCustomerDomainService&#45;&gt;PrimaryContractDomainModule -->
<g id="edge235" class="edge">
<title>PrimaryContractCustomerDomainService&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M2654.83,-542.52C2654.83,-536.03 2654.83,-527 2654.83,-527 2654.83,-527 4899.88,-527 4899.88,-527 4899.88,-527 4899.88,-596.57 4899.88,-596.57"/>
<polygon fill="black" stroke="black" points="4896.38,-596.57 4899.88,-606.57 4903.38,-596.57 4896.38,-596.57"/>
</g>
<!-- PrimaryContractDomainSagas -->
<g id="node139" class="node">
<title>PrimaryContractDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="2173" cy="-549" rx="129.07" ry="18"/>
<text text-anchor="middle" x="2173" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractDomainSagas</text>
</g>
<!-- PrimaryContractDomainSagas&#45;&gt;PrimaryContractDomainModule -->
<g id="edge236" class="edge">
<title>PrimaryContractDomainSagas&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M2290.27,-541.41C2290.27,-534.38 2290.27,-525 2290.27,-525 2290.27,-525 4914.35,-525 4914.35,-525 4914.35,-525 4914.35,-596.66 4914.35,-596.66"/>
<polygon fill="black" stroke="black" points="4910.85,-596.66 4914.35,-606.66 4917.85,-596.66 4910.85,-596.66"/>
</g>
<!-- PrimaryContractDomainService -->
<g id="node140" class="node">
<title>PrimaryContractDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="1891" cy="-549" rx="135.43" ry="18"/>
<text text-anchor="middle" x="1891" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractDomainService</text>
</g>
<!-- PrimaryContractDomainService&#45;&gt;PrimaryContractDomainModule -->
<g id="edge237" class="edge">
<title>PrimaryContractDomainService&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M1992.45,-536.79C1992.45,-530.45 1992.45,-524 1992.45,-524 1992.45,-524 4928.82,-524 4928.82,-524 4928.82,-524 4928.82,-596.94 4928.82,-596.94"/>
<polygon fill="black" stroke="black" points="4925.32,-596.94 4928.82,-606.94 4932.32,-596.94 4925.32,-596.94"/>
</g>
<!-- PrimaryContractEventRepository -->
<g id="node141" class="node">
<title>PrimaryContractEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="1597" cy="-549" rx="140.11" ry="18"/>
<text text-anchor="middle" x="1597" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractEventRepository</text>
</g>
<!-- PrimaryContractEventRepository&#45;&gt;PrimaryContractDomainModule -->
<g id="edge238" class="edge">
<title>PrimaryContractEventRepository&#45;&gt;PrimaryContractDomainModule</title>
<path fill="none" stroke="black" d="M1597,-567.17C1597,-571.05 1597,-574 1597,-574 1597,-574 4856.49,-574 4856.49,-574 4856.49,-574 4856.49,-596.88 4856.49,-596.88"/>
<polygon fill="black" stroke="black" points="4852.99,-596.88 4856.49,-606.88 4859.99,-596.88 4852.99,-596.88"/>
</g>
<!-- PrimaryContractQueryRepository -->
<g id="node146" class="node">
<title>PrimaryContractQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="3618" cy="-549" rx="141.81" ry="18"/>
<text text-anchor="middle" x="3618" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractQueryRepository</text>
</g>
<!-- PrimaryContractQueryRepository&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge255" class="edge">
<title>PrimaryContractQueryRepository&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M3672.96,-532.14C3672.96,-517.96 3672.96,-500 3672.96,-500 3672.96,-500 5015.71,-500 5015.71,-500 5015.71,-500 5015.71,-499.12 5015.71,-499.12"/>
<polygon fill="black" stroke="black" points="5019.21,-501.16 5015.71,-491.16 5012.21,-501.16 5019.21,-501.16"/>
</g>
<!-- PrimaryContractQueryService -->
<g id="node147" class="node">
<title>PrimaryContractQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="3330" cy="-549" rx="128.47" ry="18"/>
<text text-anchor="middle" x="3330" y="-544.8" font-family="Times,serif" font-size="14.00">PrimaryContractQueryService</text>
</g>
<!-- PrimaryContractQueryService&#45;&gt;PrimaryContractQuerySideModule -->
<g id="edge256" class="edge">
<title>PrimaryContractQueryService&#45;&gt;PrimaryContractQuerySideModule</title>
<path fill="none" stroke="black" d="M3401.04,-534C3401.04,-519.27 3401.04,-499 3401.04,-499 3401.04,-499 5012.67,-499 5012.67,-499 5012.67,-499 5012.67,-498.21 5012.67,-498.21"/>
<polygon fill="black" stroke="black" points="5016.17,-501.06 5012.67,-491.06 5009.17,-501.06 5016.17,-501.06"/>
</g>
<!-- ProposalDomainSagas -->
<g id="node148" class="node">
<title>ProposalDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="656" cy="-625" rx="98.44" ry="18"/>
<text text-anchor="middle" x="656" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalDomainSagas</text>
</g>
<!-- ProposalDomainSagas&#45;&gt;ProposalDomainModule -->
<g id="edge264" class="edge">
<title>ProposalDomainSagas&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M656,-643.09C656,-666.13 656,-703 656,-703 656,-703 4648.06,-703 4648.06,-703"/>
<polygon fill="black" stroke="black" points="4648.06,-706.5 4658.06,-703 4648.06,-699.5 4648.06,-706.5"/>
</g>
<!-- ProposalDomainService -->
<g id="node149" class="node">
<title>ProposalDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="1115" cy="-625" rx="104.79" ry="18"/>
<text text-anchor="middle" x="1115" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalDomainService</text>
</g>
<!-- ProposalDomainService&#45;&gt;ProposalDomainModule -->
<g id="edge265" class="edge">
<title>ProposalDomainService&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M1115,-643.22C1115,-663.37 1115,-693 1115,-693 1115,-693 4648.14,-693 4648.14,-693"/>
<polygon fill="black" stroke="black" points="4648.14,-696.5 4658.14,-693 4648.14,-689.5 4648.14,-696.5"/>
</g>
<!-- ProposalEventRepository -->
<g id="node150" class="node">
<title>ProposalEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="882" cy="-625" rx="109.97" ry="18"/>
<text text-anchor="middle" x="882" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalEventRepository</text>
</g>
<!-- ProposalEventRepository&#45;&gt;ProposalDomainModule -->
<g id="edge266" class="edge">
<title>ProposalEventRepository&#45;&gt;ProposalDomainModule</title>
<path fill="none" stroke="black" d="M882,-643.04C882,-664.66 882,-698 882,-698 882,-698 4648.03,-698 4648.03,-698"/>
<polygon fill="black" stroke="black" points="4648.03,-701.5 4658.03,-698 4648.03,-694.5 4648.03,-701.5"/>
</g>
<!-- ProposalQueryRepository -->
<g id="node154" class="node">
<title>ProposalQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="7598" cy="-625" rx="111.18" ry="18"/>
<text text-anchor="middle" x="7598" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalQueryRepository</text>
</g>
<!-- ProposalQueryRepository&#45;&gt;ProposalQuerySideModule -->
<g id="edge278" class="edge">
<title>ProposalQueryRepository&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M7506.16,-614.53C7506.16,-595.69 7506.16,-558 7506.16,-558 7506.16,-558 5967.37,-558 5967.37,-558"/>
<polygon fill="black" stroke="black" points="5967.37,-554.5 5957.37,-558 5967.37,-561.5 5967.37,-554.5"/>
</g>
<!-- ProposalQueryService -->
<g id="node155" class="node">
<title>ProposalQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="7825" cy="-625" rx="97.83" ry="18"/>
<text text-anchor="middle" x="7825" y="-620.8" font-family="Times,serif" font-size="14.00">ProposalQueryService</text>
</g>
<!-- ProposalQueryService&#45;&gt;ProposalQuerySideModule -->
<g id="edge279" class="edge">
<title>ProposalQueryService&#45;&gt;ProposalQuerySideModule</title>
<path fill="none" stroke="black" d="M7750.58,-613.28C7750.58,-593.11 7750.58,-554 7750.58,-554 7750.58,-554 5967.32,-554 5967.32,-554"/>
<polygon fill="black" stroke="black" points="5967.32,-550.5 5957.32,-554 5967.32,-557.5 5967.32,-550.5"/>
</g>
<!-- ScheduleDomainSagas -->
<g id="node156" class="node">
<title>ScheduleDomainSagas</title>
<ellipse fill="#fdb462" stroke="black" cx="836" cy="-397" rx="100.14" ry="18"/>
<text text-anchor="middle" x="836" y="-392.8" font-family="Times,serif" font-size="14.00">ScheduleDomainSagas</text>
</g>
<!-- ScheduleDomainSagas&#45;&gt;ScheduleDomainModule -->
<g id="edge286" class="edge">
<title>ScheduleDomainSagas&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M854.17,-414.91C854.17,-436.81 854.17,-471 854.17,-471 854.17,-471 4648.02,-471 4648.02,-471"/>
<polygon fill="black" stroke="black" points="4648.02,-474.5 4658.02,-471 4648.02,-467.5 4648.02,-474.5"/>
</g>
<!-- ScheduleEventRepository -->
<g id="node157" class="node">
<title>ScheduleEventRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="607" cy="-397" rx="111.18" ry="18"/>
<text text-anchor="middle" x="607" y="-392.8" font-family="Times,serif" font-size="14.00">ScheduleEventRepository</text>
</g>
<!-- ScheduleEventRepository&#45;&gt;ScheduleDomainModule -->
<g id="edge288" class="edge">
<title>ScheduleEventRepository&#45;&gt;ScheduleDomainModule</title>
<path fill="none" stroke="black" d="M637.94,-414.53C637.94,-437.81 637.94,-476 637.94,-476 637.94,-476 4647.8,-476 4647.8,-476"/>
<polygon fill="black" stroke="black" points="4647.8,-479.5 4657.8,-476 4647.8,-472.5 4647.8,-479.5"/>
</g>
<!-- ScheduleQueryRepository -->
<g id="node161" class="node">
<title>ScheduleQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="6619" cy="-473" rx="112.88" ry="18"/>
<text text-anchor="middle" x="6619" y="-468.8" font-family="Times,serif" font-size="14.00">ScheduleQueryRepository</text>
</g>
<!-- ScheduleQueryRepository&#45;&gt;ScheduleQuerySideModule -->
<g id="edge299" class="edge">
<title>ScheduleQueryRepository&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M6512.62,-466.72C6512.62,-453.71 6512.62,-425 6512.62,-425 6512.62,-425 5455.55,-425 5455.55,-425 5455.55,-425 5455.55,-424.02 5455.55,-424.02"/>
<polygon fill="black" stroke="black" points="5459.05,-425.21 5455.55,-415.21 5452.05,-425.21 5459.05,-425.21"/>
</g>
<!-- ScheduleQueryService -->
<g id="node162" class="node">
<title>ScheduleQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="6389" cy="-473" rx="99.54" ry="18"/>
<text text-anchor="middle" x="6389" y="-468.8" font-family="Times,serif" font-size="14.00">ScheduleQueryService</text>
</g>
<!-- ScheduleQueryService&#45;&gt;ScheduleQuerySideModule -->
<g id="edge300" class="edge">
<title>ScheduleQueryService&#45;&gt;ScheduleQuerySideModule</title>
<path fill="none" stroke="black" d="M6422.17,-455.77C6422.17,-442.35 6422.17,-426 6422.17,-426 6422.17,-426 5445.13,-426 5445.13,-426 5445.13,-426 5445.13,-424.92 5445.13,-424.92"/>
<polygon fill="black" stroke="black" points="5448.63,-425.2 5445.13,-415.2 5441.63,-425.2 5448.63,-425.2"/>
</g>
<!-- StatushistoryExportService  -->
<g id="node164" class="node">
<title>StatushistoryExportService </title>
<polygon fill="#fb8072" stroke="black" points="12880.19,-415 12707.81,-415 12707.81,-379 12880.19,-379 12880.19,-415"/>
<text text-anchor="middle" x="12794" y="-392.8" font-family="Times,serif" font-size="14.00">StatushistoryExportService </text>
</g>
<!-- StatushistoryExportModule&#45;&gt;StatushistoryExportService  -->
<g id="edge306" class="edge">
<title>StatushistoryExportModule&#45;&gt;StatushistoryExportService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M12791.02,-339.01C12791.02,-339.01 12791.02,-368.85 12791.02,-368.85"/>
<polygon fill="black" stroke="black" points="12787.52,-368.85 12791.02,-378.85 12794.52,-368.85 12787.52,-368.85"/>
</g>
<!-- StatushistoryExportRepository -->
<g id="node165" class="node">
<title>StatushistoryExportRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="13090" cy="-245" rx="130.26" ry="18"/>
<text text-anchor="middle" x="13090" y="-240.8" font-family="Times,serif" font-size="14.00">StatushistoryExportRepository</text>
</g>
<!-- StatushistoryExportRepository&#45;&gt;StatushistoryExportModule -->
<g id="edge307" class="edge">
<title>StatushistoryExportRepository&#45;&gt;StatushistoryExportModule</title>
<path fill="none" stroke="black" d="M13090,-263.01C13090,-285.49 13090,-321 13090,-321 13090,-321 12884.45,-321 12884.45,-321"/>
<polygon fill="black" stroke="black" points="12884.45,-317.5 12874.45,-321 12884.45,-324.5 12884.45,-317.5"/>
</g>
<!-- StatushistoryExportService -->
<g id="node166" class="node">
<title>StatushistoryExportService</title>
<ellipse fill="#fdb462" stroke="black" cx="12825" cy="-245" rx="116.91" ry="18"/>
<text text-anchor="middle" x="12825" y="-240.8" font-family="Times,serif" font-size="14.00">StatushistoryExportService</text>
</g>
<!-- StatushistoryExportService&#45;&gt;StatushistoryExportModule -->
<g id="edge308" class="edge">
<title>StatushistoryExportService&#45;&gt;StatushistoryExportModule</title>
<path fill="none" stroke="black" d="M12841.16,-263.01C12841.16,-263.01 12841.16,-292.85 12841.16,-292.85"/>
<polygon fill="black" stroke="black" points="12837.66,-292.85 12841.16,-302.85 12844.66,-292.85 12837.66,-292.85"/>
</g>
<!-- StatushistoryAuthService -->
<g id="node168" class="node">
<title>StatushistoryAuthService</title>
<ellipse fill="#fdb462" stroke="black" cx="3844" cy="-245" rx="109.97" ry="18"/>
<text text-anchor="middle" x="3844" y="-240.8" font-family="Times,serif" font-size="14.00">StatushistoryAuthService</text>
</g>
<!-- StatushistoryAuthService&#45;&gt;StatushistoryModule -->
<g id="edge314" class="edge">
<title>StatushistoryAuthService&#45;&gt;StatushistoryModule</title>
<path fill="none" stroke="black" d="M3881.12,-262.13C3881.12,-275.21 3881.12,-291 3881.12,-291 3881.12,-291 4704.16,-291 4704.16,-291 4704.16,-291 4704.16,-292.86 4704.16,-292.86"/>
<polygon fill="black" stroke="black" points="4700.66,-292.86 4704.16,-302.86 4707.66,-292.86 4700.66,-292.86"/>
</g>
<!-- StatushistoryRepository -->
<g id="node169" class="node">
<title>StatushistoryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="3612" cy="-245" rx="103.69" ry="18"/>
<text text-anchor="middle" x="3612" y="-240.8" font-family="Times,serif" font-size="14.00">StatushistoryRepository</text>
</g>
<!-- StatushistoryRepository&#45;&gt;StatushistoryModule -->
<g id="edge315" class="edge">
<title>StatushistoryRepository&#45;&gt;StatushistoryModule</title>
<path fill="none" stroke="black" d="M3650.93,-261.79C3650.93,-275.63 3650.93,-293 3650.93,-293 3650.93,-293 4692.67,-293 4692.67,-293 4692.67,-293 4692.67,-293.98 4692.67,-293.98"/>
<polygon fill="black" stroke="black" points="4689.17,-292.79 4692.67,-302.79 4696.17,-292.79 4689.17,-292.79"/>
</g>
<!-- StatushistoryService -->
<g id="node170" class="node">
<title>StatushistoryService</title>
<ellipse fill="#fdb462" stroke="black" cx="4062" cy="-245" rx="90.34" ry="18"/>
<text text-anchor="middle" x="4062" y="-240.8" font-family="Times,serif" font-size="14.00">StatushistoryService</text>
</g>
<!-- StatushistoryService&#45;&gt;StatushistoryModule -->
<g id="edge316" class="edge">
<title>StatushistoryService&#45;&gt;StatushistoryModule</title>
<path fill="none" stroke="black" d="M4146.33,-251.88C4146.33,-264.25 4146.33,-289 4146.33,-289 4146.33,-289 4715.66,-289 4715.66,-289 4715.66,-289 4715.66,-292.76 4715.66,-292.76"/>
<polygon fill="black" stroke="black" points="4712.16,-292.76 4715.66,-302.76 4719.16,-292.76 4712.16,-292.76"/>
</g>
<!-- TransferHistoryRepository -->
<g id="node174" class="node">
<title>TransferHistoryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="7659" cy="-473" rx="115.16" ry="18"/>
<text text-anchor="middle" x="7659" y="-468.8" font-family="Times,serif" font-size="14.00">TransferHistoryRepository</text>
</g>
<!-- TransferHistoryRepository&#45;&gt;TransferHistoryModule -->
<g id="edge322" class="edge">
<title>TransferHistoryRepository&#45;&gt;TransferHistoryModule</title>
<path fill="none" stroke="black" d="M7626.76,-455.65C7626.76,-440.19 7626.76,-420 7626.76,-420 7626.76,-420 5899.06,-420 5899.06,-420 5899.06,-420 5899.06,-419.51 5899.06,-419.51"/>
<polygon fill="black" stroke="black" points="5902.56,-425.12 5899.06,-415.12 5895.56,-425.12 5902.56,-425.12"/>
</g>
<!-- TransferHistoryService -->
<g id="node175" class="node">
<title>TransferHistoryService</title>
<ellipse fill="#fdb462" stroke="black" cx="7424" cy="-473" rx="101.82" ry="18"/>
<text text-anchor="middle" x="7424" y="-468.8" font-family="Times,serif" font-size="14.00">TransferHistoryService</text>
</g>
<!-- TransferHistoryService&#45;&gt;TransferHistoryModule -->
<g id="edge323" class="edge">
<title>TransferHistoryService&#45;&gt;TransferHistoryModule</title>
<path fill="none" stroke="black" d="M7378.37,-456.83C7378.37,-441.57 7378.37,-421 7378.37,-421 7378.37,-421 5875.65,-421 5875.65,-421 5875.65,-421 5875.65,-420.42 5875.65,-420.42"/>
<polygon fill="black" stroke="black" points="5879.15,-425.19 5875.65,-415.19 5872.15,-425.19 5879.15,-425.19"/>
</g>
</g>
</svg>
