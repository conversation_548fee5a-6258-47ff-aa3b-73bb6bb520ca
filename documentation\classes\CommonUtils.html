<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>CommonUtils</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/classes/class-utils.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#parseVND">parseVND</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                    <span class="modifier">Async</span>
                                <a href="#convertToJson">convertToJson</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#getCustomerAge">getCustomerAge</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#getCustomerMapping">getCustomerMapping</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#getDateDiffIgnoreTime">getDateDiffIgnoreTime</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#getTotalInterest">getTotalInterest</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#objectNotEmpty">objectNotEmpty</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#transformSort">transformSort</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#workdayCount">workdayCount</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="parseVND"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            parseVND</b>
                            <a href="#parseVND"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>() &#x3D;&gt; {...}</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="74" class="link-to-prism">src/modules/shared/classes/class-utils.ts:74</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="convertToJson"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            <span class="modifier">Async</span>
                            convertToJson
                        </b>
                        <a href="#convertToJson"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>convertToJson(files, sheetName: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, firstRow: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="97"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:97</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>files</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>sheetName</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;Template&#x27;</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>firstRow</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>2</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCustomerAge"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            getCustomerAge
                        </b>
                        <a href="#getCustomerAge"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCustomerAge(customer)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="25"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:25</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customer</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCustomerMapping"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            getCustomerMapping
                        </b>
                        <a href="#getCustomerMapping"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCustomerMapping(customer, employee?)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="34"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:34</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customer</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>employee</td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{ code: any; info: { gender: any; onlyYear: any; birthday: any; birthdayYear: any; address: any; ...</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getDateDiffIgnoreTime"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            getDateDiffIgnoreTime
                        </b>
                        <a href="#getDateDiffIgnoreTime"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getDateDiffIgnoreTime(start, end)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="89"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:89</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>start</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>end</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTotalInterest"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            getTotalInterest
                        </b>
                        <a href="#getTotalInterest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTotalInterest(totalAmount, rate, daysPerYear, totalDate)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="93"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:93</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>totalAmount</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>rate</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>daysPerYear</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>totalDate</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="objectNotEmpty"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            objectNotEmpty
                        </b>
                        <a href="#objectNotEmpty"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>objectNotEmpty(value: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="4"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:4</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>value</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="transformSort"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            transformSort
                        </b>
                        <a href="#transformSort"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>transformSort(paramSort?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="8"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:8</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>paramSort</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="workdayCount"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            workdayCount
                        </b>
                        <a href="#workdayCount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>workdayCount(start, end)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="78"
                            class="link-to-prism">src/modules/shared/classes/class-utils.ts:78</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>start</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>end</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import _ &#x3D; require(&quot;lodash&quot;);
const ExcelJS &#x3D; require(&#x27;exceljs&#x27;);
export class CommonUtils {
  static objectNotEmpty(value: any) {
    if (!value) return false;
    return Object.keys(value).length &gt; 0;
  }
  static transformSort(paramSort?: String) {
    let sort: any &#x3D; paramSort;
    if (_.isString(sort)) {
      sort &#x3D; sort.split(&quot;,&quot;);
    }
    if (Array.isArray(sort)) {
      let sortObj &#x3D; {};
      sort.forEach(s &#x3D;&gt; {
        if (s.startsWith(&quot;-&quot;))
          sortObj[s.slice(1)] &#x3D; -1;
        else
          sortObj[s] &#x3D; 1;
      });
      return sortObj;
    }
    return sort;
  }
  static getCustomerAge(customer) {
    if(_.get(customer, &#x27;onlyYear&#x27;) &amp;&amp; _.get(customer, &#x27;birthdayYear&#x27;)){
        return (new Date().getFullYear()) - parseInt(customer.birthdayYear);
    }
    if(!_.get(customer, &#x27;onlyYear&#x27;) &amp;&amp; _.get(customer, &#x27;birthday&#x27;)){
        return (new Date().getFullYear()) - (new Date(customer.birthday).getFullYear());
    }
    return null;
  }
  static getCustomerMapping(customer, employee?) {
    const age &#x3D; this.getCustomerAge(customer);
  return  {
    code: customer.code,
    info: {
      gender: customer.gender,
      onlyYear: customer.onlyYear,
      birthday: customer.onlyYear !&#x3D;&#x3D; true ? customer.birthday: null,
      birthdayYear: customer.onlyYear &#x3D;&#x3D;&#x3D; true ? customer.birthdayYear: null,
      address: customer.address,
      rootAddress: customer.rootAddress,
      taxCode: customer.taxCode,
      age: age,
    },
    bankInfo: customer.bankInfo,
    personalInfo: {
      email: customer.email,
      phone: customer.phone,
      name: customer.name,
      identities: [{
        value: customer.identityNumber,
        date: customer.identityIssueDate,
        place: customer.identityIssueLocation,
      }]
    },
    employee: customer.employeeTakeCare ? customer.employeeTakeCare: employee || null,
    identities: [{
      value: customer.identityNumber,
      date: customer.identityIssueDate,
      place: customer.identityIssueLocation,
    }],
    identityNumber: customer.identityNumber,
    identityDate: customer.identityIssueDate,
    identityPlace: customer.identityIssueLocation,
    email: customer.email,
    phone: customer.phone,
    name: customer.name,
    taxCode: customer.taxCode,
  }
}
  static parseVND &#x3D; (number) &#x3D;&gt; {
    return new Intl.NumberFormat(&#x27;vi-VN&#x27;).format(number);
  }

  static workdayCount(start, end) {
    var first &#x3D; start.clone().endOf(&#x27;week&#x27;); // end of first week
    var last &#x3D; end.clone().startOf(&#x27;week&#x27;); // start of last week
    var days &#x3D; last.diff(first,&#x27;days&#x27;) * 5 / 7; // this will always multiply of 7
    var wfirst &#x3D; first.day() - start.day(); // check first week
    if(start.day() &#x3D;&#x3D; 0) --wfirst; // -1 if start with sunday
    var wlast &#x3D; end.day() - last.day(); // check last week
    if(end.day() &#x3D;&#x3D; 6) --wlast; // -1 if end with saturday
    return wfirst + Math.floor(days) + wlast;
  }

  static getDateDiffIgnoreTime(start, end) {
    return end.startOf(&#x27;day&#x27;).diff(start.startOf(&#x27;day&#x27;),&#x27;days&#x27;)
  }

  static getTotalInterest(totalAmount, rate, daysPerYear, totalDate) {
    return ((totalAmount * rate) / 100 / daysPerYear) * totalDate
  }

  static async convertToJson(files, sheetName: any &#x3D; &#x27;Template&#x27;, firstRow: number &#x3D; 2) {
    const workbook &#x3D; new ExcelJS.Workbook();
    await workbook.xlsx.load(files[0].buffer)
    const worksheet &#x3D; workbook.getWorksheet(sheetName);
    if(!worksheet){
      return [];
    }
    let headers &#x3D; [];
    let json &#x3D; [];
    worksheet.eachRow(function (row, rowNumber) {
      if (rowNumber &#x3D;&#x3D;&#x3D; firstRow) {
        headers &#x3D; row.values.join().split(&#x27;,&#x27;);
        headers.shift();
      } else if (rowNumber &gt; firstRow) {
        const data &#x3D; {};
        for (var x &#x3D; 0; x &lt; headers.length; x++) {
          _.set(data, headers[x], row.getCell(x + 1).text)
        }
        json.push(data);
      }
    });
    return json;
  }
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'CommonUtils.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
