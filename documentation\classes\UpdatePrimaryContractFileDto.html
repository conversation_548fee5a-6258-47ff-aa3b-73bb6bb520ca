<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>UpdatePrimaryContractFileDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/primary-contract.domain/dto/primary-contract.dto.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#files">files</a>
                            </li>
                            <li>
                                <a href="#id">id</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="files"></a>
                        <span class="name">
                            <b>
                            files</b>
                            <a href="#files"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="111" class="link-to-prism">src/modules/primary-contract.domain/dto/primary-contract.dto.ts:111</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="id"></a>
                        <span class="name">
                            <b>
                            id</b>
                            <a href="#id"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @IsString()<br />@IsNotEmpty()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="109" class="link-to-prism">src/modules/primary-contract.domain/dto/primary-contract.dto.ts:109</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { IsEnum, IsString } from &quot;class-validator&quot;;
import { IsNotEmpty, IsNumber } from &quot;class-validator&quot;;
import {InterestCalculationStatusEnum} from &quot;src/modules/shared/enum/status.enum&quot;;
import { ClassBased } from &quot;../../shared/classes/class-based&quot;;
import { IInterestCalculation, IPrimaryContract } from &quot;../../shared/services/primary-contract/interfaces/interface&quot;;

export class PrimaryContractDto extends ClassBased implements IPrimaryContract {
  liquidation: any;
  liquidate: Date;
  id: string;
  name: string;
  code: string;
  status: string;
  startDate: Date;
  expiredDate: Date;
  signedDate:  Date;
  primaryTransactionId: string;
  policyPaymentId: string;
  policyDiscountId: string;
  policyDiscountIds: string[];

  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: any;

  interestCalculations: IInterestCalculation[];
  currency: string;
  customer2: any;
  type: string;
  transferType: string;
  description: string;
  policyPayment: any;
  reason: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean
  deposit: any;
  files: any;
  companyInformation: any;
  releaseStartDate: any;
  releaseEndDate: any;
}
export class InterestCalculationDto extends ClassBased implements IInterestCalculation {
  id: string;
  name: string;
  title: string;
  code: string;
  status: InterestCalculationStatusEnum;
  principalAmount: number &#x3D; 0;
  interestRate: number &#x3D; 0;
  interestAmount: number &#x3D; 0;
  interestAmountTransferred: number &#x3D; 0;
  interestReductionAmount: number &#x3D; 0;
  remainingAmount: number &#x3D; 0;
  createdDate: Date;
  startDate: Date;
  endDate: Date;
  dayOfLatePayment: number &#x3D; 0;
  installmentName: string;
  receipts: any;
  description: string;
  
}
export class DownloadInterestCalculationDto extends InterestCalculationDto {
  customerName: string;
  productCode: string;
  projectName: string;
  address: string;
  contractName: string;
  signedDate: Date;
  companyName: string;
  banks: any;
}
export class CreatePrimaryContractDto extends PrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  primaryTransactionId: string;
}
export class UpdatePrimaryContractDto extends CreatePrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  changeInstallment: any;
}
export class UpdateInterestCalculationDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  interestCalculation: InterestCalculationDto;
}
export class PrimaryContractStatusDto {
  @IsString()
  @IsNotEmpty()
  id: string;
  @IsString()
  @IsNotEmpty()
  status: string;
  reason: string;
}
export class UpdateDepositConfirmDto {
  @IsString()
  @IsNotEmpty()
  id: string;
}
export class UpdatePrimaryContractFileDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  files: any;
}
export class UpdatePrimaryContractDeliveryDateDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  deliveryDate: any;

  filesDelivery: any;
}
export class SendPrimaryContractDeliveryNotifyDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  contracts: any;
}
export class HandoverPrimaryContractDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNotEmpty()
  deliveryResult: any;

  @IsNotEmpty()
  deliveryItems: any;

  @IsString()
  @IsNotEmpty()
  handoverStatus: any;

  files: any;
  deliveryDate: any;
}

export class UpdateShowReceiptDto { 
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  receiptId: string;
  
  @IsNotEmpty()
  isShowedReceipt: boolean;
}

export class UpdateManyPrimaryContract {
  lstIdPrimaryContract: any[];
  startDate: any;
  endDate: any;
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'UpdatePrimaryContractFileDto.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
