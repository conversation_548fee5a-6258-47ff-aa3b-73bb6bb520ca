<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>ProposalQueryController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/proposal.queryside/controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/proposal</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#downloadProposal">downloadProposal</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#downloadProposalById">downloadProposalById</a>
                            </li>
                            <li>
                                <a href="#findAll">findAll</a>
                            </li>
                            <li>
                                <a href="#findById">findById</a>
                            </li>
                            <li>
                                <a href="#getProposalByContract">getProposalByContract</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadProposal"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            downloadProposal
                        </b>
                        <a href="#downloadProposal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>downloadProposal(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, res)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;download&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="41"
                            class="link-to-prism">src/modules/proposal.queryside/controller.ts:41</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>res</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadProposalById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            downloadProposalById
                        </b>
                        <a href="#downloadProposalById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>downloadProposalById(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, res)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;download/:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="77"
                            class="link-to-prism">src/modules/proposal.queryside/controller.ts:77</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>res</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <b>
                            findAll
                        </b>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findAll(user, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="36"
                            class="link-to-prism">src/modules/proposal.queryside/controller.ts:36</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findById"></a>
                    <span class="name">
                        <b>
                            findById
                        </b>
                        <a href="#findById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="67"
                            class="link-to-prism">src/modules/proposal.queryside/controller.ts:67</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getProposalByContract"></a>
                    <span class="name">
                        <b>
                            getProposalByContract
                        </b>
                        <a href="#getProposalByContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getProposalByContract(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/getByContract/:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="72"
                            class="link-to-prism">src/modules/proposal.queryside/controller.ts:72</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { CmdPatternConst } from &#x27;./../shared/constant/cmd-pattern.const&#x27;;
import { Controller, Get, NotFoundException, Param, Query, Res, UseGuards, ForbiddenException } from &quot;@nestjs/common&quot;;
import { ProposalQueryService } from &quot;./service&quot;;
import { LoggingInterceptor } from &quot;../../common/interceptors/logging.interceptor&quot;;
import { RolesGuard } from &quot;../../common/guards/roles.guard&quot;;
import { AuthGuard } from &quot;@nestjs/passport&quot;;
import { ACGuard, UseRoles } from &quot;nest-access-control&quot;;
import { Usr } from &quot;../shared/services/user/decorator/user.decorator&quot;;
import { PermissionEnum } from &quot;../shared/enum/permission.enum&quot;;
import { ValidationPipe } from &quot;../../common/pipes/validation.pipe&quot;;
import { existsSync } from &#x27;fs&#x27;;
import { join } from &#x27;path&#x27;;
import { unlinkSync } from &#x27;fs&#x27;;
import { FileGenerationService } from &quot;./file-generation.service&quot;;
import { ConfigService } from &#x27;./../config/config.service&#x27;;
import { EmployeeQueryRepository } from &#x27;../employee.query/repository/query.repository&#x27;;
const _ &#x3D; require(&quot;lodash&quot;);

@Controller(&quot;v1/proposal&quot;)
@UseGuards(AuthGuard(&quot;jwt&quot;))
export class ProposalQueryController {
  constructor(
    private readonly proposalService: ProposalQueryService,
    private readonly fileGenerationService: FileGenerationService,
    private readonly configService: ConfigService,
    private readonly employeeQueryRepository: EmployeeQueryRepository,
  ) {}

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_GET_ID, // &#x3D;&gt; feature name
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Get()
  findAll(@Usr() user, @Query() query: any) {
    return this.proposalService.findAll(query, user);
  }

  @Get(&#x27;download&#x27;)
  async downloadProposal(
    @Usr() user: any, @Query() query: any, @Res() res) {

    delete query.page;
    delete query.pageSize;
    const data &#x3D; await this.proposalService.findAll(query, user);
    const fileName &#x3D; &#x27;Danh_Sach_Proposal&#x27; + new Date().getTime();
    await this.fileGenerationService.exportProposal(user, fileName, data);
    const filePath &#x3D; join(this.configService.getUploadFolderPath(), &#x60;${fileName}.xlsx&#x60;);
    const isFileExisted &#x3D; existsSync(filePath);
    if (isFileExisted &#x3D;&#x3D;&#x3D; false) {
        throw new NotFoundException(&#x27;File not found&#x27;);
    }
    res.sendFile(&#x60;${fileName}.xlsx&#x60;, { root: this.configService.getUploadFolderPath() });
    setTimeout(() &#x3D;&gt; {
      unlinkSync(filePath);
    }, 5000);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_GET_ID, // &#x3D;&gt; feature name
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Get(&quot;:id&quot;)
  findById(@Param(&quot;id&quot;) id: string) {
    return this.proposalService.findById(id);
  }

  @Get(&quot;/getByContract/:id&quot;)
  getProposalByContract(@Param(&quot;id&quot;) id: string) {
    return this.proposalService.findByContract(id);
  }

  @Get(&#x27;download/:id&#x27;)
  async downloadProposalById(
    @Usr() user: any, @Param(&#x27;id&#x27;) id: string, @Query() query: any, @Res() res) {
      const data &#x3D; await this.proposalService.findById(id);
      const project &#x3D; await this.proposalService.getProjectById(data.escrowTicket.project.id);
      let url &#x3D; null;
      if(_.get(project , &#x27;setting.templateFileContract&#x27;, null)){
        const file &#x3D; project.setting.templateFileContract.find(item &#x3D;&gt; (item.type &#x3D;&#x3D;&#x3D; &#x27;DON_DE_NGHI&#x27;));
        url &#x3D; file ? file.file.absoluteUrl : null;
      }
      if(!url){
        throw new NotFoundException(&#x27;File template not found&#x27;);
      }

      const fileName &#x3D; &#x27;proposal&#x27;+ id + new Date().getTime();

      const employee &#x3D; await this.employeeQueryRepository.findOne({ id: data.createdBy });
      await this.fileGenerationService.downloadFile(employee, fileName, data, url, fileName);
      const filePath &#x3D; join(this.configService.getUploadFolderPath(), &#x60;${fileName}.docx&#x60;);
      const isFileExisted &#x3D; existsSync(filePath);
      if (isFileExisted &#x3D;&#x3D;&#x3D; false) {
          throw new NotFoundException(&#x27;File not found&#x27;);
      }
      res.sendFile(&#x60;${fileName}.docx&#x60;, { root: this.configService.getUploadFolderPath() });
      setTimeout(() &#x3D;&gt; {
        unlinkSync(filePath);
      }, 5000);
    }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'ProposalQueryController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
