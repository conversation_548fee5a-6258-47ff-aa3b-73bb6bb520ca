<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>ScheduleDomainController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/schedule.domain/controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/schedule</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#active">active</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createSchedule">createSchedule</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteSchedule">deleteSchedule</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateSchedule">updateSchedule</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="active"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            active
                        </b>
                        <a href="#active"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>active(user, dto: <a href="../classes/ActiveScheduleDto.html">ActiveScheduleDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;active&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="60"
                            class="link-to-prism">src/modules/schedule.domain/controller.ts:60</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/ActiveScheduleDto.html" target="_self" >ActiveScheduleDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createSchedule"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createSchedule
                        </b>
                        <a href="#createSchedule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createSchedule(user, dto: <a href="../classes/CreateScheduleDto.html">CreateScheduleDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="36"
                            class="link-to-prism">src/modules/schedule.domain/controller.ts:36</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/CreateScheduleDto.html" target="_self" >CreateScheduleDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteSchedule"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteSchedule
                        </b>
                        <a href="#deleteSchedule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteSchedule(user, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Delete(&#x27;:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="72"
                            class="link-to-prism">src/modules/schedule.domain/controller.ts:72</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateSchedule"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateSchedule
                        </b>
                        <a href="#updateSchedule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateSchedule(user, dto: <a href="../classes/UpdateScheduleDto.html">UpdateScheduleDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="48"
                            class="link-to-prism">src/modules/schedule.domain/controller.ts:48</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateScheduleDto.html" target="_self" >UpdateScheduleDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Controller,
  Post,
  Put,
  Body,
  Headers,
  UseGuards,
  UseInterceptors,
  Param,
  Delete,
} from &quot;@nestjs/common&quot;;
import { LoggingInterceptor } from &quot;../../common/interceptors/logging.interceptor&quot;;
import { ScheduleDomainService } from &quot;./service&quot;;
import { Action } from &quot;../shared/enum/action.enum&quot;;
import { RolesGuard } from &#x27;../../common/guards/roles.guard&#x27;;
import { ActiveScheduleDto, CreateScheduleDto, UpdateScheduleDto } from &quot;./dto/schedule.dto&quot;;
import { AuthGuard } from &#x27;@nestjs/passport&#x27;;
import { ACGuard, UseRoles } from &#x27;nest-access-control&#x27;;
import { Usr } from &#x27;../shared/services/user/decorator/user.decorator&#x27;;
import { PermissionEnum } from &quot;../shared/enum/permission.enum&quot;;
import { ValidationPipe } from &#x27;../../common/pipes/validation.pipe&#x27;;
@Controller(&quot;v1/schedule&quot;)
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard(&#x27;jwt&#x27;))
export class ScheduleDomainController {
  private actionName: string &#x3D; Action.NOTIFY;
  constructor(private readonly scheduleService: ScheduleDomainService) {}

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.SCHEDULE_CREATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Post()
  async createSchedule(@Usr() user, @Body(new ValidationPipe()) dto: CreateScheduleDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.scheduleService.createSchedule(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.SCHEDULE_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put()
  async updateSchedule(@Usr() user, @Body(new ValidationPipe()) dto: UpdateScheduleDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.scheduleService.updateSchedule(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.SCHEDULE_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put(&#x27;active&#x27;)
  async active(@Usr() user, @Body(new ValidationPipe()) dto: ActiveScheduleDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.scheduleService.activeSchedule(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.SCHEDULE_DELETE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Delete(&#x27;:id&#x27;)
  async deleteSchedule(@Usr() user, @Param(&#x27;id&#x27;) id: string, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.scheduleService.deleteSchedule(user, id, this.actionName);
  }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'ScheduleDomainController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
