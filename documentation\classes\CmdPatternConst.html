<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>CmdPatternConst</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/constant/cmd-pattern.const.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CARE">CARE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CHECK_USER_EXISTENCE">CHECK_USER_EXISTENCE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CMD_PATTERN">CMD_PATTERN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CREATE_USER_FROM_AGENCY">CREATE_USER_FROM_AGENCY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CUSTOMER">CUSTOMER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMPLOYEE">EMPLOYEE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMPLOYEE_UP_POINT">EMPLOYEE_UP_POINT</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LISTENER">LISTENER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LOGGER">LOGGER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#MAILER">MAILER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#NOTIFICATION">NOTIFICATION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#NOTIFY">NOTIFY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#ORGCHART">ORGCHART</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PRIMARY_CONTRACT">PRIMARY_CONTRACT</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROJECT">PROJECT</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROPERTY">PROPERTY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROPERTY_PRIMARY_TRANSACTION">PROPERTY_PRIMARY_TRANSACTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SALES_PROGRAM">SALES_PROGRAM</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SEND_ERROR">SEND_ERROR</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SEND_LOG">SEND_LOG</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SEND_REGISTER_AUTO_USER">SEND_REGISTER_AUTO_USER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SETUP_FEATURE">SETUP_FEATURE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SMS">SMS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SOCIAL">SOCIAL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SYNC_ERP">SYNC_ERP</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#TRANSACTION">TRANSACTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#UPLOAD">UPLOAD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#USER">USER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#USER_EXISTENCE_CMD">USER_EXISTENCE_CMD</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#VERIFY_USER">VERIFY_USER</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CARE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CARE</b>
                            <a href="#CARE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_CUSTOMER_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.care.get.customer.by.id&#x27;,
    GET_CUSTOMER_BY_IDENTITY: CmdPatternConst.CMD_PATTERN + &quot;.care.get.customer.by.identity&quot;,
    GET_TRANSACTION: CmdPatternConst.CMD_PATTERN + &quot;.care.get.transaction.by.contract&quot;,
    CHECK_EXISTENT_CUSTOMER: CmdPatternConst.CMD_PATTERN + &#x27;care.check.existent.customer&#x27;,
    CREATE_USER_CARE_AUTO: CmdPatternConst.CMD_PATTERN + &quot;.user.create.care.auto&quot;,
    ACTIVE_CUSTOMER: CmdPatternConst.CMD_PATTERN + &quot;.care.active.customer&quot;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="115" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:115</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CHECK_USER_EXISTENCE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CHECK_USER_EXISTENCE</b>
                            <a href="#CHECK_USER_EXISTENCE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsG.user.check-email-exist&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:19</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CMD_PATTERN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CMD_PATTERN</b>
                            <a href="#CMD_PATTERN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="12" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:12</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CREATE_USER_FROM_AGENCY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CREATE_USER_FROM_AGENCY</b>
                            <a href="#CREATE_USER_FROM_AGENCY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg.user.create-user-from-agency&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:20</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CUSTOMER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CUSTOMER</b>
                            <a href="#CUSTOMER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_INFO: CmdPatternConst.CMD_PATTERN + &quot;.customer.get.info&quot;,
    CREATE_CUSTOMER_FROM_LIQUIDATION_CONTRACT: CmdPatternConst.CMD_PATTERN + &#x27;.create.customer.from.liquidation.contract&#x27;,
    CHECK_CUSTOMER_EXIST_BY_PHONE_EMAIL: CmdPatternConst.CMD_PATTERN + &#x27;.check.customer.exist.by.phone.email&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="52" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:52</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMPLOYEE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMPLOYEE</b>
                            <a href="#EMPLOYEE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    EMPLOYEE_GET_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.employee.get.by.id&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="124" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:124</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMPLOYEE_UP_POINT"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMPLOYEE_UP_POINT</b>
                            <a href="#EMPLOYEE_UP_POINT"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg.employee.up.point.listener&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="10" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:10</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LISTENER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LISTENER</b>
                            <a href="#LISTENER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    PRIMARY_CONTRACT: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.listener&quot;,
    PRIMARY_CONTRACT_ONE_BY_QUERY: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.one.by.query&quot;,
    PRIMARY_CONTRACT_UPDATE_PATHDTT: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.update.pathdtt&quot;,
    PRIMARY_CONTRACT_UPDATE_RECEIPT_AMOUNT: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.update.receipt.amount&quot;,
    GET_POS_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.get.pos.by.query.listener&#x27;,
    EMPLOYEE: CmdPatternConst.CMD_PATTERN + &#x27;.employee.listener&#x27;,
    GET_PROJECT_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.id&#x27;,
    GET_PROJECT_BY_IDS: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.ids&#x27;,
    GET_PROJECT_BY_CUSTOMER_SERVICE: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.customer.service&#x27;,
    PRIMARY_CONTRACT_TIMEOUT: CmdPatternConst.CMD_PATTERN + &quot;.primary.contract.timeout&quot;,
    LIST_EMPLOYEE_HAND_OVER_BUSY: CmdPatternConst.CMD_PATTERN + &#x27;.list.employee.hand.over.busy&#x27;,
    PRIMARY_CONTRACT_QUERY_SEND_MAIL: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.query&#x27;,
    PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.update.deposit.confirm&#x27;,
    PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.update.transfer.confirm&#x27;,
    GET_PRIMARY_CONTRACT_BY_CUSTOMER: CmdPatternConst.CMD_PATTERN + &#x27;.get.primary.contract.by.customer&#x27;,
    GET_TRANSACTIONS_BY_TRANSACTION_CODES: CmdPatternConst.CMD_PATTERN + &#x27;.get.transactions.by.transaction.codes&#x27;,
    GET_CUSTOMER_IDENTITIES_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.primary-contract.get.customer.identities.by.project.id&#x27;,
    GET_PRIMARY_CONTRACT_BY_PROPERTY_UNIT_CODE_AND_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.primary.contract.by.property.unit.code.and.project.id&#x27;,
    GET_PRIMARY_CONTRACTS_BY_PROPERTY_UNIT_CODES_AND_PROJECT_IDS: CmdPatternConst.CMD_PATTERN + &#x27;.get.primary.contracts.by.property.unit.codes.and.project.ids&#x27;,
    UPDATE_PROPERTY_UNITS: CmdPatternConst.CMD_PATTERN + &#x27;.update.property.units&#x27;,
    REQUEST_APPROVE: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.request.approve&#x27;,
    SYNC_PROJECT: CmdPatternConst.CMD_PATTERN + &#x27;.sync.project&#x27;,
    SYNC_PRIMARY_TRANSACTION: CmdPatternConst.CMD_PATTERN + &#x27;.sync.primary.transaction&#x27;,
    GET_ALL_PROJECT_BY_USER: CmdPatternConst.CMD_PATTERN + &#x27;.property.get.all.project.by.user&#x27;,
    HANDOVER_SCHEDULE_NOTIFY_CUSTOMER: &#x27;.handover.schedule.notify.customer&#x27;,
    UPDATE_HISTORIES_PROPERTY_UNIT: CmdPatternConst.CMD_PATTERN + &#x27;.update.histories.property.unit&#x27;,
    PRIMARY_CONTRACT_CREATE_TRANSFERRED_TICKET_LISTENER: &#x27;.primary.contract.create.transferred.ticket.listener&#x27;,
    UPDATE_TRADEHISTORY_SYNC_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.update.trade.history.sync.erp&#x27;,
    SEARCH_CONFIG_ERP_BY_CAMPAIGN: CmdPatternConst.CMD_PATTERN + &#x27;.search.config.erp.by.campaign&#x27;,
    CREATE_PAYMENT_POLICY_SYNC_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.create.payment.policy.sync.erp&#x27;,
    GET_HANDED_CONTRACT_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.handed.contract.by.project.id&#x27;,
    CHECK_DELIVERY_SCHEDULE_END_DATE: CmdPatternConst.CMD_PATTERN + &#x27;.check.delivery.schedule.end.date&#x27;,
    SEND_NOTICE_DELIVERY_BEFORE_DEADLINE: CmdPatternConst.CMD_PATTERN + &#x27;.send.notice.delivery.before.deadline&#x27;,
    GET_ONE_HANDOVER_SETTING: CmdPatternConst.CMD_PATTERN + &#x27;.get.one.handover.setting&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="57" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:57</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LOGGER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LOGGER</b>
                            <a href="#LOGGER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    SEND_LOG: CmdPatternConst.CMD_PATTERN + &quot;.logger.log&quot;,
    SEND_ERROR: CmdPatternConst.CMD_PATTERN + &quot;.logger.error&quot;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:22</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="MAILER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            MAILER</b>
                            <a href="#MAILER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    CREATED_USER: CmdPatternConst.CMD_PATTERN + &quot;.mailer.pwdUser&quot;,
    USER_FORGOT_PWD: CmdPatternConst.CMD_PATTERN + &quot;.user.forgot.pwd&quot;,
    USER_CHANGE_PWD: CmdPatternConst.CMD_PATTERN + &quot;.user.change.pwd&quot;,
    SEND_MAIL_DELIVERY: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.send.mail.delivery&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="41" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:41</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NOTIFICATION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            NOTIFICATION</b>
                            <a href="#NOTIFICATION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    MESSAGE: CmdPatternConst.CMD_PATTERN + &quot;.notification.message&quot;,
    CARE: CmdPatternConst.CMD_PATTERN + &quot;.notification.care&quot;,
    NOTIFICATION_MULTIPLE: CmdPatternConst.CMD_PATTERN + &#x27;.notification.multiple&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:35</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NOTIFY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            NOTIFY</b>
                            <a href="#NOTIFY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    INFO: CmdPatternConst.CMD_PATTERN + &quot;.notification.info&quot;,
    ERROR: CmdPatternConst.CMD_PATTERN + &quot;.notification.error&quot;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="13" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:13</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ORGCHART"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            ORGCHART</b>
                            <a href="#ORGCHART"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_TEMPLATE_DEPT_REPORT_FILE_BY_POS_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.template.dept.report.file.by.pos.id&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="132" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:132</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PRIMARY_CONTRACT"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PRIMARY_CONTRACT</b>
                            <a href="#PRIMARY_CONTRACT"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_PRIMARY_CONTRACT_BY_EMPLOYEE:
      CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.by.employee&quot;,
    CREATE_CONTRACT_SYNC_ERP: CmdPatternConst.CMD_PATTERN + &quot;.create.contract.sync.erp&quot;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="47" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:47</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROJECT"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROJECT</b>
                            <a href="#PROJECT"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_PROJECT_BY_ACCOUNTANT: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.accountant&#x27;,
    RATING: CmdPatternConst.CMD_PATTERN + &#x27;.project.rating&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="108" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:108</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROPERTY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROPERTY</b>
                            <a href="#PROPERTY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_PROPERTY_UNIT_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.property.unit.by.project.id&#x27;,
    FIND_ONE_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.find.one.property.unit.by.query&#x27;,
    UPDATE_ONE_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.update.one.property.unit.by.query&#x27;,
    UPDATE_MANY_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.update.many.property.unit.by.query&#x27;,
    LIQUIDATE_PROPERTY_UNIT_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.liquidate.property.unit.erp&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="145" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:145</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROPERTY_PRIMARY_TRANSACTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROPERTY_PRIMARY_TRANSACTION</b>
                            <a href="#PROPERTY_PRIMARY_TRANSACTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.property-primary-transaction.id&#x27;,
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_CODE: CmdPatternConst.CMD_PATTERN + &#x27;.get.property-primary-transaction.code&#x27;,
    UPDATE_CONTRACT_IN_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.update.contract-in-transaction.id&#x27;,
    REMOVE_CONTRACT_IN_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.remove.contract-in-transaction.id&#x27;,
    LIQUIDATION_PRIMARY_TRANSACTION: CmdPatternConst.CMD_PATTERN + &#x27;.liquidation.primary.transaction&#x27;,
    UPDATE_CONTRACT_IN_PROPERTY_UNIT: CmdPatternConst.CMD_PATTERN + &#x27;.update.contract.in.property.unit&#x27;,
    LIQUIDATION_PROPOSAL_APPROVED: CmdPatternConst.CMD_PATTERN + &#x27;.liquidation.proposal.approved&#x27;,
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.get.property-primary-transaction.by.query&#x27;,
    UPDATE_CUSTOMER_FROM_CONTRACT: CmdPatternConst.CMD_PATTERN + &#x27;.update.customer.from.contract&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="97" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:97</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SALES_PROGRAM"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SALES_PROGRAM</b>
                            <a href="#SALES_PROGRAM"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_SALES_PROGRAM_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.sales.program.id&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="128" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:128</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SEND_ERROR"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SEND_ERROR</b>
                            <a href="#SEND_ERROR"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg.logger.error&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="9" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:9</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SEND_LOG"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SEND_LOG</b>
                            <a href="#SEND_LOG"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg.logger.log&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="8" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:8</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SEND_REGISTER_AUTO_USER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SEND_REGISTER_AUTO_USER</b>
                            <a href="#SEND_REGISTER_AUTO_USER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg-mailer-pwdUser&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="7" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:7</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SETUP_FEATURE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SETUP_FEATURE</b>
                            <a href="#SETUP_FEATURE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg.feature.setup&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="4" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:4</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SMS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SMS</b>
                            <a href="#SMS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    PRIMARY_CONTRACT_PAYMENT_REMINDER: CmdPatternConst.CMD_PATTERN + &#x27;.sms.primary.contract.payment.reminder&#x27;,
    PRIMARY_CONTRACT_DELIVERY_REMINDER: CmdPatternConst.CMD_PATTERN + &#x27;.sms.primary.contract.delivery.reminder&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="93" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:93</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SOCIAL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SOCIAL</b>
                            <a href="#SOCIAL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    CREATE_MARKET_PLACE_GROUP: CmdPatternConst.CMD_PATTERN + &#x27;.social.create.market.place.group&#x27;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="153" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:153</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SYNC_ERP"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SYNC_ERP</b>
                            <a href="#SYNC_ERP"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    GET_CAMPAIGN_ERP_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.campaign.erp.by.project.id&#x27;,
    SEND_REQUEST_TO_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.send.request.to.erp&#x27;,
}</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="140" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:140</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TRANSACTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            TRANSACTION</b>
                            <a href="#TRANSACTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    CLONE_RECEIPTS: CmdPatternConst.CMD_PATTERN + &#x27;.clone.receipts&#x27;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="112" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:112</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="UPLOAD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            UPLOAD</b>
                            <a href="#UPLOAD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    STREAMBUFFER: CmdPatternConst.CMD_PATTERN + &#x27;.uploader.streambuffer&#x27;
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="136" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:136</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="USER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            USER</b>
                            <a href="#USER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    SIGNED: CmdPatternConst.CMD_PATTERN + &quot;.user.signed&quot;,
    RESET_ROLE: CmdPatternConst.CMD_PATTERN + &quot;.user.role.reset&quot;,
    CREATE_USER_WITH_PASSWORD:
      CmdPatternConst.CMD_PATTERN + &quot;.user.create.with.password&quot;,
    GET_ROLE: CmdPatternConst.CMD_PATTERN + &quot;.user.get.role&quot;,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="27" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:27</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="USER_EXISTENCE_CMD"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            USER_EXISTENCE_CMD</b>
                            <a href="#USER_EXISTENCE_CMD"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsg.user.existence&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="2" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:2</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="VERIFY_USER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            VERIFY_USER</b>
                            <a href="#VERIFY_USER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-adsm.sts.findUserByMsx&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="5" class="link-to-prism">src/modules/shared/constant/cmd-pattern.const.ts:5</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">export class CmdPatternConst {
  static USER_EXISTENCE_CMD &#x3D; &quot;msx-adsg.user.existence&quot;;

  static SETUP_FEATURE &#x3D; &quot;msx-adsg.feature.setup&quot;;
  static VERIFY_USER &#x3D; &quot;msx-adsm.sts.findUserByMsx&quot;;

  static SEND_REGISTER_AUTO_USER &#x3D; &quot;msx-adsg-mailer-pwdUser&quot;;
  static SEND_LOG &#x3D; &quot;msx-adsg.logger.log&quot;;
  static SEND_ERROR &#x3D; &quot;msx-adsg.logger.error&quot;;
  static EMPLOYEE_UP_POINT &#x3D; &quot;msx-adsg.employee.up.point.listener&quot;;

  static CMD_PATTERN &#x3D; &quot;msx-adsg&quot;;
  static NOTIFY &#x3D; {
    INFO: CmdPatternConst.CMD_PATTERN + &quot;.notification.info&quot;,
    ERROR: CmdPatternConst.CMD_PATTERN + &quot;.notification.error&quot;,
  };

  //
  static CHECK_USER_EXISTENCE &#x3D; &quot;msx-adsG.user.check-email-exist&quot;;
  static CREATE_USER_FROM_AGENCY &#x3D; &quot;msx-adsg.user.create-user-from-agency&quot;;

  static LOGGER &#x3D; {
    SEND_LOG: CmdPatternConst.CMD_PATTERN + &quot;.logger.log&quot;,
    SEND_ERROR: CmdPatternConst.CMD_PATTERN + &quot;.logger.error&quot;,
  };

  static USER &#x3D; {
    SIGNED: CmdPatternConst.CMD_PATTERN + &quot;.user.signed&quot;,
    RESET_ROLE: CmdPatternConst.CMD_PATTERN + &quot;.user.role.reset&quot;,
    CREATE_USER_WITH_PASSWORD:
      CmdPatternConst.CMD_PATTERN + &quot;.user.create.with.password&quot;,
    GET_ROLE: CmdPatternConst.CMD_PATTERN + &quot;.user.get.role&quot;,
  };

  static NOTIFICATION &#x3D; {
    MESSAGE: CmdPatternConst.CMD_PATTERN + &quot;.notification.message&quot;,
    CARE: CmdPatternConst.CMD_PATTERN + &quot;.notification.care&quot;,
    NOTIFICATION_MULTIPLE: CmdPatternConst.CMD_PATTERN + &#x27;.notification.multiple&#x27;,
  };

  static MAILER &#x3D; {
    CREATED_USER: CmdPatternConst.CMD_PATTERN + &quot;.mailer.pwdUser&quot;,
    USER_FORGOT_PWD: CmdPatternConst.CMD_PATTERN + &quot;.user.forgot.pwd&quot;,
    USER_CHANGE_PWD: CmdPatternConst.CMD_PATTERN + &quot;.user.change.pwd&quot;,
    SEND_MAIL_DELIVERY: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.send.mail.delivery&#x27;,
  };
  static PRIMARY_CONTRACT &#x3D; {
    GET_PRIMARY_CONTRACT_BY_EMPLOYEE:
      CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.by.employee&quot;,
    CREATE_CONTRACT_SYNC_ERP: CmdPatternConst.CMD_PATTERN + &quot;.create.contract.sync.erp&quot;,
  };
  static CUSTOMER &#x3D; {
    GET_INFO: CmdPatternConst.CMD_PATTERN + &quot;.customer.get.info&quot;,
    CREATE_CUSTOMER_FROM_LIQUIDATION_CONTRACT: CmdPatternConst.CMD_PATTERN + &#x27;.create.customer.from.liquidation.contract&#x27;,
    CHECK_CUSTOMER_EXIST_BY_PHONE_EMAIL: CmdPatternConst.CMD_PATTERN + &#x27;.check.customer.exist.by.phone.email&#x27;,
  };
  static LISTENER &#x3D; {
    PRIMARY_CONTRACT: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.listener&quot;,
    PRIMARY_CONTRACT_ONE_BY_QUERY: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.one.by.query&quot;,
    PRIMARY_CONTRACT_UPDATE_PATHDTT: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.update.pathdtt&quot;,
    PRIMARY_CONTRACT_UPDATE_RECEIPT_AMOUNT: CmdPatternConst.CMD_PATTERN + &quot;.primaryContract.update.receipt.amount&quot;,
    GET_POS_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.get.pos.by.query.listener&#x27;,
    EMPLOYEE: CmdPatternConst.CMD_PATTERN + &#x27;.employee.listener&#x27;,
    GET_PROJECT_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.id&#x27;,
    GET_PROJECT_BY_IDS: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.ids&#x27;,
    GET_PROJECT_BY_CUSTOMER_SERVICE: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.customer.service&#x27;,
    PRIMARY_CONTRACT_TIMEOUT: CmdPatternConst.CMD_PATTERN + &quot;.primary.contract.timeout&quot;,
    LIST_EMPLOYEE_HAND_OVER_BUSY: CmdPatternConst.CMD_PATTERN + &#x27;.list.employee.hand.over.busy&#x27;,
    PRIMARY_CONTRACT_QUERY_SEND_MAIL: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.query&#x27;,
    PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.update.deposit.confirm&#x27;,
    PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.update.transfer.confirm&#x27;,
    GET_PRIMARY_CONTRACT_BY_CUSTOMER: CmdPatternConst.CMD_PATTERN + &#x27;.get.primary.contract.by.customer&#x27;,
    GET_TRANSACTIONS_BY_TRANSACTION_CODES: CmdPatternConst.CMD_PATTERN + &#x27;.get.transactions.by.transaction.codes&#x27;,
    GET_CUSTOMER_IDENTITIES_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.primary-contract.get.customer.identities.by.project.id&#x27;,
    GET_PRIMARY_CONTRACT_BY_PROPERTY_UNIT_CODE_AND_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.primary.contract.by.property.unit.code.and.project.id&#x27;,
    GET_PRIMARY_CONTRACTS_BY_PROPERTY_UNIT_CODES_AND_PROJECT_IDS: CmdPatternConst.CMD_PATTERN + &#x27;.get.primary.contracts.by.property.unit.codes.and.project.ids&#x27;,
    UPDATE_PROPERTY_UNITS: CmdPatternConst.CMD_PATTERN + &#x27;.update.property.units&#x27;,
    REQUEST_APPROVE: CmdPatternConst.CMD_PATTERN + &#x27;.primary.contract.request.approve&#x27;,
    SYNC_PROJECT: CmdPatternConst.CMD_PATTERN + &#x27;.sync.project&#x27;,
    SYNC_PRIMARY_TRANSACTION: CmdPatternConst.CMD_PATTERN + &#x27;.sync.primary.transaction&#x27;,
    GET_ALL_PROJECT_BY_USER: CmdPatternConst.CMD_PATTERN + &#x27;.property.get.all.project.by.user&#x27;,
    HANDOVER_SCHEDULE_NOTIFY_CUSTOMER: &#x27;.handover.schedule.notify.customer&#x27;,
    UPDATE_HISTORIES_PROPERTY_UNIT: CmdPatternConst.CMD_PATTERN + &#x27;.update.histories.property.unit&#x27;,
    PRIMARY_CONTRACT_CREATE_TRANSFERRED_TICKET_LISTENER: &#x27;.primary.contract.create.transferred.ticket.listener&#x27;,
    UPDATE_TRADEHISTORY_SYNC_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.update.trade.history.sync.erp&#x27;,
    SEARCH_CONFIG_ERP_BY_CAMPAIGN: CmdPatternConst.CMD_PATTERN + &#x27;.search.config.erp.by.campaign&#x27;,
    CREATE_PAYMENT_POLICY_SYNC_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.create.payment.policy.sync.erp&#x27;,
    GET_HANDED_CONTRACT_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.handed.contract.by.project.id&#x27;,
    CHECK_DELIVERY_SCHEDULE_END_DATE: CmdPatternConst.CMD_PATTERN + &#x27;.check.delivery.schedule.end.date&#x27;,
    SEND_NOTICE_DELIVERY_BEFORE_DEADLINE: CmdPatternConst.CMD_PATTERN + &#x27;.send.notice.delivery.before.deadline&#x27;,
    GET_ONE_HANDOVER_SETTING: CmdPatternConst.CMD_PATTERN + &#x27;.get.one.handover.setting&#x27;,
  };
  static SMS &#x3D; {
    PRIMARY_CONTRACT_PAYMENT_REMINDER: CmdPatternConst.CMD_PATTERN + &#x27;.sms.primary.contract.payment.reminder&#x27;,
    PRIMARY_CONTRACT_DELIVERY_REMINDER: CmdPatternConst.CMD_PATTERN + &#x27;.sms.primary.contract.delivery.reminder&#x27;,
  }
  static  PROPERTY_PRIMARY_TRANSACTION &#x3D; {
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.property-primary-transaction.id&#x27;,
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_CODE: CmdPatternConst.CMD_PATTERN + &#x27;.get.property-primary-transaction.code&#x27;,
    UPDATE_CONTRACT_IN_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.update.contract-in-transaction.id&#x27;,
    REMOVE_CONTRACT_IN_TRANSACTION_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.remove.contract-in-transaction.id&#x27;,
    LIQUIDATION_PRIMARY_TRANSACTION: CmdPatternConst.CMD_PATTERN + &#x27;.liquidation.primary.transaction&#x27;,
    UPDATE_CONTRACT_IN_PROPERTY_UNIT: CmdPatternConst.CMD_PATTERN + &#x27;.update.contract.in.property.unit&#x27;,
    LIQUIDATION_PROPOSAL_APPROVED: CmdPatternConst.CMD_PATTERN + &#x27;.liquidation.proposal.approved&#x27;,
    GET_PROPERTY_PRIMARY_TRANSACTION_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.get.property-primary-transaction.by.query&#x27;,
    UPDATE_CUSTOMER_FROM_CONTRACT: CmdPatternConst.CMD_PATTERN + &#x27;.update.customer.from.contract&#x27;,
  };
  static PROJECT &#x3D; {
    GET_PROJECT_BY_ACCOUNTANT: CmdPatternConst.CMD_PATTERN + &#x27;.get.project.accountant&#x27;,
    RATING: CmdPatternConst.CMD_PATTERN + &#x27;.project.rating&#x27;,
  };
  static TRANSACTION &#x3D; {
    CLONE_RECEIPTS: CmdPatternConst.CMD_PATTERN + &#x27;.clone.receipts&#x27;
  };
  static CARE &#x3D; {
    GET_CUSTOMER_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.care.get.customer.by.id&#x27;,
    GET_CUSTOMER_BY_IDENTITY: CmdPatternConst.CMD_PATTERN + &quot;.care.get.customer.by.identity&quot;,
    GET_TRANSACTION: CmdPatternConst.CMD_PATTERN + &quot;.care.get.transaction.by.contract&quot;,
    CHECK_EXISTENT_CUSTOMER: CmdPatternConst.CMD_PATTERN + &#x27;care.check.existent.customer&#x27;,
    CREATE_USER_CARE_AUTO: CmdPatternConst.CMD_PATTERN + &quot;.user.create.care.auto&quot;,
    ACTIVE_CUSTOMER: CmdPatternConst.CMD_PATTERN + &quot;.care.active.customer&quot;
  }

  static EMPLOYEE &#x3D; {
    EMPLOYEE_GET_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.employee.get.by.id&#x27;,
  }

  static SALES_PROGRAM &#x3D; {
    GET_SALES_PROGRAM_BY_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.sales.program.id&#x27;,
  };

  static ORGCHART &#x3D; {
    GET_TEMPLATE_DEPT_REPORT_FILE_BY_POS_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.template.dept.report.file.by.pos.id&#x27;,
  }

  static UPLOAD &#x3D; {
    STREAMBUFFER: CmdPatternConst.CMD_PATTERN + &#x27;.uploader.streambuffer&#x27;
  }

  static SYNC_ERP &#x3D; {
    GET_CAMPAIGN_ERP_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.campaign.erp.by.project.id&#x27;,
    SEND_REQUEST_TO_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.send.request.to.erp&#x27;,
}

  static PROPERTY &#x3D; {
    GET_PROPERTY_UNIT_BY_PROJECT_ID: CmdPatternConst.CMD_PATTERN + &#x27;.get.property.unit.by.project.id&#x27;,
    FIND_ONE_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.find.one.property.unit.by.query&#x27;,
    UPDATE_ONE_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.update.one.property.unit.by.query&#x27;,
    UPDATE_MANY_PROPERTY_UNIT_BY_QUERY: CmdPatternConst.CMD_PATTERN + &#x27;.update.many.property.unit.by.query&#x27;,
    LIQUIDATE_PROPERTY_UNIT_ERP: CmdPatternConst.CMD_PATTERN + &#x27;.liquidate.property.unit.erp&#x27;,
  }

  static SOCIAL &#x3D; {
    CREATE_MARKET_PLACE_GROUP: CmdPatternConst.CMD_PATTERN + &#x27;.social.create.market.place.group&#x27;,
  }

}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'CmdPatternConst.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
