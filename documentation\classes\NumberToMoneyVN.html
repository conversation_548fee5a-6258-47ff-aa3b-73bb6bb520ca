<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>NumberToMoneyVN</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/classes/number-to-money-vn.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#getSubText">getSubText</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#parse">parse</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#parseNumberToString">parseNumberToString</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#parseNumberToStringNumber">parseNumberToStringNumber</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#summary">summary</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>



            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getSubText"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            getSubText
                        </b>
                        <a href="#getSubText"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getSubText(balance)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="39"
                            class="link-to-prism">src/modules/shared/classes/number-to-money-vn.ts:39</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>balance</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parse"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            parse
                        </b>
                        <a href="#parse"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parse(number, returnNull?)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="4"
                            class="link-to-prism">src/modules/shared/classes/number-to-money-vn.ts:4</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>number</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>returnNull</td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseNumberToString"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            parseNumberToString
                        </b>
                        <a href="#parseNumberToString"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseNumberToString(number)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="58"
                            class="link-to-prism">src/modules/shared/classes/number-to-money-vn.ts:58</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>number</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>&quot;một&quot; | &quot;hai&quot; | &quot;ba&quot; | &quot;bốn&quot; | &quot;năm&quot; | &quot;sáu&quot; | &quot;bảy&quot; | &quot;tám&quot; | &quot;chín&quot; | &quot;lẻ&quot;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseNumberToStringNumber"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            parseNumberToStringNumber
                        </b>
                        <a href="#parseNumberToStringNumber"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseNumberToStringNumber(number)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="83"
                            class="link-to-prism">src/modules/shared/classes/number-to-money-vn.ts:83</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>number</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>&quot;1&quot; | &quot;2&quot; | &quot;3&quot; | &quot;4&quot; | &quot;5&quot; | &quot;6&quot; | &quot;7&quot; | &quot;8&quot; | &quot;9&quot; | &quot;10&quot;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="summary"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            summary
                        </b>
                        <a href="#summary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>summary(price, isFull?)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="109"
                            class="link-to-prism">src/modules/shared/classes/number-to-money-vn.ts:109</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>price</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>isFull</td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">declare let $: any;
export class NumberToMoneyVN {
  static parse(number, returnNull?) {
    let balance: number &#x3D; parseInt(number);
    let value;
    let balanceText &#x3D; &#x27;&#x27;;
    value &#x3D; balance/(1*1000*1000*1000);
    if (value &gt;&#x3D; 1) {
        balanceText +&#x3D; this.getSubText(Math.floor(value));
        balanceText+&#x3D; value &gt;&#x3D; 1 ? &#x27;tỷ &#x27; : &#x27;&#x27;;
        balance &#x3D; balance - Math.floor(value)*1000*1000*1000
    }
    value &#x3D; balance/(1*1000*1000);
    if (value &gt;&#x3D; 1) {
      balanceText +&#x3D; this.getSubText(Math.floor(value));
      balanceText+&#x3D; value &gt;&#x3D; 1  ? &#x27;triệu &#x27; : &#x27;&#x27;;
      balance &#x3D; balance - Math.floor(value)*1000*1000
    }
    value &#x3D; balance/(1*1000);
    if (value &gt;&#x3D; 1) {
        balanceText +&#x3D; this.getSubText(Math.floor(value));
        balanceText+&#x3D; value &gt;&#x3D; 1 ? &#x27;nghìn &#x27; : &#x27;&#x27;;
        balance &#x3D; balance - Math.floor(value)*1000;
    }
    value &#x3D; balance;
    if (value &gt;&#x3D; 1) {
      balanceText +&#x3D; this.getSubText(Math.floor(value));
      balance &#x3D; balance - Math.floor(value)
    }
    balanceText &#x3D; balanceText.replace(/một mươi/g, &#x27;mười&#x27;)
    balanceText &#x3D; balanceText.replace(/lẻ lẻ/g, &#x27;&#x27;);
    balanceText &#x3D; balanceText.replace(/trăm một/g, &#x27;trăm mốt&#x27;);
    balanceText &#x3D; balanceText.replace(/mươi một/g, &#x27;mươi mốt&#x27;);
    balanceText &#x3D; balanceText.substring(0,1).toUpperCase() + balanceText.substring(1, balanceText.length)
    balanceText +&#x3D; &#x27;đồng&#x27;;
    return balanceText;
  }
  static getSubText(balance) {
    let text&#x3D;&#x27;&#x27;, isHundred &#x3D; false;
    if (balance/(1*100) &gt;&#x3D; 1) {
      text +&#x3D; this.parseNumberToString(Math.floor(balance/(1*100))) + &#x27; trăm &#x27;
      balance &#x3D; balance - Math.floor(balance/(1*100))*100;
      isHundred &#x3D; true;
    }
    if (balance/(1*10) &gt;&#x3D; 1) {
        text +&#x3D; this.parseNumberToString(Math.floor(balance/(1*10))) + &#x27; mươi &#x27;
        balance &#x3D; balance - Math.floor(balance/(1*10))*10
    } else if (isHundred &amp;&amp; balance !&#x3D; 0) {
      text +&#x3D; &#x27;lẻ &#x27;
    }
    if (balance &lt; 10 &amp;&amp; balance !&#x3D; 0) {
      text +&#x3D; this.parseNumberToString(Math.floor(balance)) + &#x27; &#x27;
      balance &#x3D; balance - Math.floor(balance)
    }
    return text;
  }
  static parseNumberToString(number) {
    number &#x3D; parseInt(number);
    switch (number) {
      case 1 :
        return &#x27;một&#x27;
      case 2 :
        return &#x27;hai&#x27;
      case 3 :
        return &#x27;ba&#x27;
      case 4 :
        return &#x27;bốn&#x27;
      case 5 :
        return &#x27;năm&#x27;
      case 6 :
        return &#x27;sáu&#x27;
      case 7 :
        return &#x27;bảy&#x27;
      case 8 :
        return &#x27;tám&#x27;
      case 9 :
        return &#x27;chín&#x27;
      case 0 :
        return &#x27;lẻ&#x27;
    }
  }
  static parseNumberToStringNumber(number) {
    number &#x3D; parseInt(number);
    switch (number) {
      case 1 :
        return &#x27;1&#x27;
      case 2 :
        return &#x27;2&#x27;
      case 3 :
        return &#x27;3&#x27;
      case 4 :
        return &#x27;4&#x27;
      case 5 :
        return &#x27;5&#x27;
      case 6 :
        return &#x27;6&#x27;
      case 7 :
        return &#x27;7&#x27;
      case 8 :
        return &#x27;8&#x27;
      case 9 :
        return &#x27;9&#x27;
      case 0 :
        return &#x27;10&#x27;
    }
  }
  
  static summary(price, isFull?) {
    let text &#x3D; &#x27;&#x27;;
    if (price &gt;&#x3D; 1000*1000*1000) {
      text &#x3D; (price/(1000*1000*1000)).toString().substring(0, 4) + &#x27; tỷ&#x27;
    } else if (price &gt;&#x3D; 1000*1000) {
        text &#x3D; (price/(1000*1000)).toString().substring(0, 4) + ( !isFull ?  &#x27; triệu&#x27; : &#x27;Tr&#x27;)
    }
    return text;
  }
}</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'NumberToMoneyVN.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
