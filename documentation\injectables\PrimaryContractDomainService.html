<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>PrimaryContractDomainService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/primary-contract.domain/service.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#commandId">commandId</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#addHistoriesHandover">addHistoriesHandover</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approveContract">approveContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approveInterestCalculation">approveInterestCalculation</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approveLiquidationContract">approveLiquidationContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approvePurchaseContract">approvePurchaseContract</a>
                            </li>
                            <li>
                                <a href="#calPaymentPercentage">calPaymentPercentage</a>
                            </li>
                            <li>
                                <a href="#checkErrorCustomer">checkErrorCustomer</a>
                            </li>
                            <li>
                                <a href="#checkErrorCustomer2SyncErp">checkErrorCustomer2SyncErp</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#cloneContract">cloneContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createContract">createContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createdContractSyncErp">createdContractSyncErp</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createPurchaseContract">createPurchaseContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteContract">deleteContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteInterestCalculation">deleteInterestCalculation</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#executeCommand">executeCommand</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#getTransferredReceiptList">getTransferredReceiptList</a>
                            </li>
                            <li>
                                <a href="#getTransferType">getTransferType</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#importFiles">importFiles</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#liquidationContract">liquidationContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#requestApproveContract">requestApproveContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#sendDeliveryNotify">sendDeliveryNotify</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#syncContractTransaction">syncContractTransaction</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#syncTotalPaymentToProperty">syncTotalPaymentToProperty</a>
                            </li>
                            <li>
                                    <span class="modifier">Public</span>
                                <a href="#transformPolicyPayment">transformPolicyPayment</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateContract">updateContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateContractFiles">updateContractFiles</a>
                            </li>
                            <li>
                                <a href="#updateContractInTracsaction">updateContractInTracsaction</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateDeliveryDate">updateDeliveryDate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateDepositConfirm">updateDepositConfirm</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateInterestCalculation">updateInterestCalculation</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateMany">updateMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateManyPrimaryContract">updateManyPrimaryContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updatePropertyUnits">updatePropertyUnits</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updatePurchaseContract">updatePurchaseContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateShowReceipt">updateShowReceipt</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTransferConfirm">updateTransferConfirm</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(commandBus: CommandBus, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>, codeGenerateService: <a href="../injectables/CodeGenerateService.html">CodeGenerateService</a>, queryRepository: <a href="../injectables/PrimaryContractQueryRepository.html">PrimaryContractQueryRepository</a>, propertyClient: <a href="../injectables/PropertyClient.html">PropertyClient</a>, careClient: <a href="../injectables/CareClient.html">CareClient</a>, policyQueryService: <a href="../injectables/PolicyQueryService.html">PolicyQueryService</a>, notificationClient: <a href="../injectables/NotificationClient.html">NotificationClient</a>, handoverQueryService: <a href="../injectables/HandoverQueryService.html">HandoverQueryService</a>, handoverScheduleQueryService: <a href="../injectables/HandoverScheduleQueryService.html">HandoverScheduleQueryService</a>, liquidationQueryRepository: <a href="../injectables/LiquidationQueryRepository.html">LiquidationQueryRepository</a>, mailerClient: <a href="../injectables/MailerClient.html">MailerClient</a>, transferHistoryRepository: <a href="../injectables/TransferHistoryRepository.html">TransferHistoryRepository</a>, historyRepository: <a href="../injectables/HistoryImportQueryRepository.html">HistoryImportQueryRepository</a>, uploadClient: <a href="../injectables/UploadClient.html">UploadClient</a>, transactionClient: <a href="../injectables/TransactionClient.html">TransactionClient</a>, syncErpClient: <a href="../injectables/SyncErpClient.html">SyncErpClient</a>, socialClient: <a href="../injectables/SocialClient.html">SocialClient</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="59" class="link-to-prism">src/modules/primary-contract.domain/service.ts:59</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>commandBus</td>
                                                  
                                                        <td>
                                                                    <code>CommandBus</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>codeGenerateService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CodeGenerateService.html" target="_self" >CodeGenerateService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>queryRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PrimaryContractQueryRepository.html" target="_self" >PrimaryContractQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>propertyClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PropertyClient.html" target="_self" >PropertyClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>careClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CareClient.html" target="_self" >CareClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>policyQueryService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PolicyQueryService.html" target="_self" >PolicyQueryService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>notificationClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/NotificationClient.html" target="_self" >NotificationClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>handoverQueryService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/HandoverQueryService.html" target="_self" >HandoverQueryService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>handoverScheduleQueryService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/HandoverScheduleQueryService.html" target="_self" >HandoverScheduleQueryService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>liquidationQueryRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LiquidationQueryRepository.html" target="_self" >LiquidationQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>mailerClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MailerClient.html" target="_self" >MailerClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transferHistoryRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TransferHistoryRepository.html" target="_self" >TransferHistoryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>historyRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/HistoryImportQueryRepository.html" target="_self" >HistoryImportQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>uploadClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/UploadClient.html" target="_self" >UploadClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>transactionClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/TransactionClient.html" target="_self" >TransactionClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>syncErpClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/SyncErpClient.html" target="_self" >SyncErpClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>socialClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/SocialClient.html" target="_self" >SocialClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addHistoriesHandover"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            addHistoriesHandover
                        </b>
                        <a href="#addHistoriesHandover"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addHistoriesHandover(contract, user, handoverStartTime, propertyStatusName, actionName)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="664"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:664</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>contract</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>handoverStartTime</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>propertyStatusName</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approveContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approveContract
                        </b>
                        <a href="#approveContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approveContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/PrimaryContractStatusDto.html">PrimaryContractStatusDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="412"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:412</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/PrimaryContractStatusDto.html" target="_self" >PrimaryContractStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approveInterestCalculation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approveInterestCalculation
                        </b>
                        <a href="#approveInterestCalculation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approveInterestCalculation(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdateInterestCalculationDto.html">UpdateInterestCalculationDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1052"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1052</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateInterestCalculationDto.html" target="_self" >UpdateInterestCalculationDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approveLiquidationContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approveLiquidationContract
                        </b>
                        <a href="#approveLiquidationContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approveLiquidationContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/PrimaryContractStatusDto.html">PrimaryContractStatusDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="513"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:513</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/PrimaryContractStatusDto.html" target="_self" >PrimaryContractStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approvePurchaseContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approvePurchaseContract
                        </b>
                        <a href="#approvePurchaseContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approvePurchaseContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/PrimaryContractStatusDto.html">PrimaryContractStatusDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="567"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:567</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/PrimaryContractStatusDto.html" target="_self" >PrimaryContractStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="calPaymentPercentage"></a>
                    <span class="name">
                        <b>
                            calPaymentPercentage
                        </b>
                        <a href="#calPaymentPercentage"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>calPaymentPercentage(params: literal type, contract?)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1634"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1634</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>params</td>
                                    <td>
                                            <code>literal type</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>contract</td>
                                    <td>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkErrorCustomer"></a>
                    <span class="name">
                        <b>
                            checkErrorCustomer
                        </b>
                        <a href="#checkErrorCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>checkErrorCustomer(history, ticket, customer, unit)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2284"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:2284</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>history</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>ticket</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>customer</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>unit</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkErrorCustomer2SyncErp"></a>
                    <span class="name">
                        <b>
                            checkErrorCustomer2SyncErp
                        </b>
                        <a href="#checkErrorCustomer2SyncErp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>checkErrorCustomer2SyncErp(history, customer2, unit, isCustomer2)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2414"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:2414</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>history</td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>customer2</td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>unit</td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isCustomer2</td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="cloneContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            cloneContract
                        </b>
                        <a href="#cloneContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>cloneContract(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, liquidationDto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, newTicket)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="350"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:350</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>liquidationDto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>newTicket</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createContract
                        </b>
                        <a href="#createContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/CreatePrimaryContractDto.html">CreatePrimaryContractDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="81"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:81</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/CreatePrimaryContractDto.html" target="_self" >CreatePrimaryContractDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createdContractSyncErp"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createdContractSyncErp
                        </b>
                        <a href="#createdContractSyncErp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createdContractSyncErp(contract)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2481"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:2481</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>contract</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createPurchaseContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createPurchaseContract
                        </b>
                        <a href="#createPurchaseContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createPurchaseContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="216"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:216</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteContract
                        </b>
                        <a href="#deleteContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1390"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1390</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteInterestCalculation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteInterestCalculation
                        </b>
                        <a href="#deleteInterestCalculation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteInterestCalculation(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdateInterestCalculationDto.html">UpdateInterestCalculationDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1410"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1410</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateInterestCalculationDto.html" target="_self" >UpdateInterestCalculationDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="executeCommand"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            executeCommand
                        </b>
                        <a href="#executeCommand"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>executeCommand(action: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, commandId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, item: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2371"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:2371</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>action</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>commandId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>item</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTransferredReceiptList"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            getTransferredReceiptList
                        </b>
                        <a href="#getTransferredReceiptList"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTransferredReceiptList(contract)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1371"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1371</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>contract</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTransferType"></a>
                    <span class="name">
                        <b>
                            getTransferType
                        </b>
                        <a href="#getTransferType"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTransferType(type)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2271"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:2271</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>type</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>&quot;&quot; | &quot;CASH&quot; | &quot;TRANSFER&quot; | &quot;OTHER&quot;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="importFiles"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            importFiles
                        </b>
                        <a href="#importFiles"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>importFiles(user, dto, files, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1844"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1844</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>files</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="liquidationContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            liquidationContract
                        </b>
                        <a href="#liquidationContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>liquidationContract(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, liquidation: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="326"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:326</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>liquidation</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requestApproveContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            requestApproveContract
                        </b>
                        <a href="#requestApproveContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>requestApproveContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/PrimaryContractStatusDto.html">PrimaryContractStatusDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="293"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:293</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/PrimaryContractStatusDto.html" target="_self" >PrimaryContractStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="sendDeliveryNotify"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            sendDeliveryNotify
                        </b>
                        <a href="#sendDeliveryNotify"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>sendDeliveryNotify(user, dto: <a href="../classes/SendPrimaryContractDeliveryNotifyDto.html">SendPrimaryContractDeliveryNotifyDto</a>, actionName)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1697"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1697</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/SendPrimaryContractDeliveryNotifyDto.html" target="_self" >SendPrimaryContractDeliveryNotifyDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="syncContractTransaction"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            syncContractTransaction
                        </b>
                        <a href="#syncContractTransaction"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>syncContractTransaction(transaction: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1434"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1434</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>transaction</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="syncTotalPaymentToProperty"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            syncTotalPaymentToProperty
                        </b>
                        <a href="#syncTotalPaymentToProperty"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>syncTotalPaymentToProperty(totalPercentage, contract)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1688"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1688</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>totalPercentage</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>contract</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="transformPolicyPayment"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Public</span>
                            transformPolicyPayment
                        </b>
                        <a href="#transformPolicyPayment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>transformPolicyPayment(contract, price, projectSetting, signedDate, receiptList, newPolicyPayment: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, housePrice: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, landPrice: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, maintenanceFee: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1145"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1145</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>contract</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>price</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>projectSetting</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>signedDate</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>receiptList</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>newPolicyPayment</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>housePrice</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>0</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>landPrice</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>0</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>maintenanceFee</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateContract
                        </b>
                        <a href="#updateContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdatePrimaryContractDto.html">UpdatePrimaryContractDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="679"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:679</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdatePrimaryContractDto.html" target="_self" >UpdatePrimaryContractDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateContractFiles"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateContractFiles
                        </b>
                        <a href="#updateContractFiles"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateContractFiles(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdatePrimaryContractFileDto.html">UpdatePrimaryContractFileDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="596"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:596</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdatePrimaryContractFileDto.html" target="_self" >UpdatePrimaryContractFileDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateContractInTracsaction"></a>
                    <span class="name">
                        <b>
                            updateContractInTracsaction
                        </b>
                        <a href="#updateContractInTracsaction"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>updateContractInTracsaction(event)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2249"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:2249</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>event</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateDeliveryDate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateDeliveryDate
                        </b>
                        <a href="#updateDeliveryDate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateDeliveryDate(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdatePrimaryContractDeliveryDateDto.html">UpdatePrimaryContractDeliveryDateDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="610"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:610</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdatePrimaryContractDeliveryDateDto.html" target="_self" >UpdatePrimaryContractDeliveryDateDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateDepositConfirm"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateDepositConfirm
                        </b>
                        <a href="#updateDepositConfirm"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateDepositConfirm(data)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1796"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1796</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>data</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateInterestCalculation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateInterestCalculation
                        </b>
                        <a href="#updateInterestCalculation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateInterestCalculation(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdateInterestCalculationDto.html">UpdateInterestCalculationDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1016"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1016</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateInterestCalculationDto.html" target="_self" >UpdateInterestCalculationDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateMany
                        </b>
                        <a href="#updateMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateMany(query, updateQuery)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1792"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1792</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>updateQuery</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateManyPrimaryContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateManyPrimaryContract
                        </b>
                        <a href="#updateManyPrimaryContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateManyPrimaryContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdateManyPrimaryContract.html">UpdateManyPrimaryContract</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1130"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1130</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateManyPrimaryContract.html" target="_self" >UpdateManyPrimaryContract</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatePropertyUnits"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updatePropertyUnits
                        </b>
                        <a href="#updatePropertyUnits"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updatePropertyUnits(units: any[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1077"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1077</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>units</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatePurchaseContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updatePurchaseContract
                        </b>
                        <a href="#updatePurchaseContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updatePurchaseContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="835"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:835</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateShowReceipt"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateShowReceipt
                        </b>
                        <a href="#updateShowReceipt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateShowReceipt(dto: <a href="../classes/UpdateShowReceiptDto.html">UpdateShowReceiptDto</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1820"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1820</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateShowReceiptDto.html" target="_self" >UpdateShowReceiptDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTransferConfirm"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateTransferConfirm
                        </b>
                        <a href="#updateTransferConfirm"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTransferConfirm(data)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1808"
                            class="link-to-prism">src/modules/primary-contract.domain/service.ts:1808</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>data</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="commandId"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                            commandId</b>
                            <a href="#commandId"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="59" class="link-to-prism">src/modules/primary-contract.domain/service.ts:59</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>PrimaryContractDomainService.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="58" class="link-to-prism">src/modules/primary-contract.domain/service.ts:58</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { LiquidationStatusEnum, LiquidationTypeEnum } from &#x27;./../shared/enum/liquidation.enum&#x27;;
import {BadRequestException, Injectable} from &quot;@nestjs/common&quot;;
import {CommandBus} from &quot;@nestjs/cqrs&quot;;
import {CreatePrimaryContractCommand} from &quot;./commands/impl/create-primary-contract.cmd&quot;;
import {UpdatePrimaryContractCommand} from &quot;./commands/impl/update-primary-contract.cmd&quot;;
import {DeletePrimaryContractCommand} from &quot;./commands/impl/delete-primary-contract.cmd&quot;;
import {PrimaryContractQueryRepository} from &quot;../primary-contract.queryside/repository/primary-contract-query.repository&quot;;
import {MsxLoggerService} from &quot;../logger/logger.service&quot;;
import {CodeGenerateService} from &quot;../code-generate/service&quot;;
import {CmdPatternConst, CommonConst} from &quot;../shared/constant&quot;;
import {Action} from &quot;../shared/enum/action.enum&quot;;
import {
  CreatePrimaryContractDto,
  PrimaryContractStatusDto, SendPrimaryContractDeliveryNotifyDto,
  UpdateDepositConfirmDto,
  UpdateInterestCalculationDto, UpdateManyPrimaryContract, UpdatePrimaryContractDeliveryDateDto,
  UpdatePrimaryContractDto, UpdatePrimaryContractFileDto, UpdateShowReceiptDto
} from &quot;./dto/primary-contract.dto&quot;;
import {PropertyClient} from &quot;../mgs-sender/property.client&quot;;
import {ErrorConst} from &quot;../shared/constant/error.const&quot;;
import {PolicyQueryService} from &quot;../policy.queryside/service&quot;;
import {
  ActionContractName,
  ContractEnum,
  ContractTypeEnum,
  DiscountTypeEnum,
  DiscountTypeRealEstateEnum,
  PolicyTypeEnum,
  ScheduleInstallmentEnum,
  StatusContractEnum
} from &quot;../shared/enum/primary-contract.enum&quot;;
import {CommonUtils} from &quot;../shared/classes/class-utils&quot;;
import {HandoverStatusEnum, InterestCalculationStatusEnum, TransactionStatusEnum} from &quot;../shared/enum/status.enum&quot;;
import moment &#x3D; require(&quot;moment&quot;);
const momentTz &#x3D; require(&#x27;moment-timezone&#x27;);
import {NotificationClient} from &quot;../mgs-sender/notification.client&quot;;
import {CareClient} from &quot;../mgs-sender/care.client&quot;;
import {HandoverQueryService} from &quot;../handover.queryside/service&quot;;
import {MailerClient} from &quot;../mgs-sender/mailer.client&quot;;
import { LiquidationQueryRepository } from &quot;../liquidation.queryside/repository/liquidation.query.repository&quot;;
import { TransferHistoryRepository } from &quot;../transfer-history/repository/transfer-history.query.repository&quot;;
import * as _ from &quot;lodash&quot;;
import { PermissionEnum } from &#x27;../shared/enum/permission.enum&#x27;;
import {UploadClient} from &quot;../mgs-sender/uploader.client&quot;;
import {HistoryImportQueryRepository} from &quot;../import-history.queryside/repository/query.repository&quot;;
import {TransactionClient} from &quot;../mgs-sender/transaction.client&quot;;
import { SyncErpClient } from &#x27;../mgs-sender/syncErp.client&#x27;;
import {SocialClient} from &quot;../mgs-sender/social.client&quot;;
import { HandoverScheduleQueryService } from &quot;../handover-schedule.queryside/service&quot;;
import { HandoverScheduleActionNameConst, HandoverScheduleStatusNameConst } from &#x27;../shared/constant/handover.const&#x27;;
import { expiredDateType } from &#x27;../shared/enum/policies.enum&#x27;;

const uuid &#x3D; require(&quot;uuid&quot;);
const clc &#x3D; require(&quot;cli-color&quot;);

@Injectable()
export class PrimaryContractDomainService {
  private readonly context &#x3D; PrimaryContractDomainService.name;
  private commandId: string;
  constructor(
    private readonly commandBus: CommandBus,
    private readonly loggerService: MsxLoggerService,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly queryRepository: PrimaryContractQueryRepository,
    private readonly propertyClient: PropertyClient,
    private readonly careClient: CareClient,
    private readonly policyQueryService: PolicyQueryService,
    private readonly notificationClient: NotificationClient,
    private readonly handoverQueryService: HandoverQueryService,
    private readonly handoverScheduleQueryService: HandoverScheduleQueryService,
    private readonly liquidationQueryRepository: LiquidationQueryRepository,
    private readonly mailerClient: MailerClient,
    private readonly transferHistoryRepository: TransferHistoryRepository,
    private readonly historyRepository: HistoryImportQueryRepository,
    private readonly uploadClient: UploadClient,
    private readonly transactionClient: TransactionClient,
    private readonly syncErpClient: SyncErpClient,
    private readonly socialClient: SocialClient,

  ) {}
  async createContract(user: any, dto: CreatePrimaryContractDto, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;create service&#x27;));
    let amount, price &#x3D; 0, landPrice &#x3D; 0, housePrice &#x3D; 0;
    let receipt &#x3D; [];
    let projectSetting &#x3D; {};
    const model : any &#x3D; {...dto};
    this.commandId &#x3D; uuid.v4();
    model.modifiedBy &#x3D; user.id;
    model.createdBy &#x3D; user.id;
    model.createdDate &#x3D; new Date();
    let primaryTransaction;
    if (dto.primaryTransactionId) {
      primaryTransaction &#x3D; await this.propertyClient.sendDataPromise({
        action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
        id: dto.primaryTransactionId
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID);
      if (!primaryTransaction) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;transactionId&#x27;, &#x27;id&#x27;, dto.primaryTransactionId)
        });
      }
      if(primaryTransaction.contract){
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.EXISTED, &#x27;Hợp đồng&#x27;, &#x27;id&#x27;, dto.primaryTransactionId)
        });
      }
      if(dto.customer2 &amp;&amp; dto.customer2.name){
        primaryTransaction.customer2 &#x3D; CommonUtils.getCustomerMapping(dto.customer2);
      }
      model.primaryTransaction &#x3D; primaryTransaction;


      if (dto.calcContractPrice) {
        price &#x3D; primaryTransaction.propertyUnit.contractPrice;
        housePrice &#x3D; 0;
        landPrice &#x3D; 0;
      } else {
        price &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
        housePrice &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
        landPrice &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee &#x3D; {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      receipt &#x3D; primaryTransaction.reciept ? primaryTransaction.reciept : [] ;
      receipt &#x3D; receipt.filter(r &#x3D;&gt; r.status &#x3D;&#x3D;&#x3D; &#x27;TRANSFERED&#x27;);

      amount &#x3D; primaryTransaction.amount;
      projectSetting &#x3D; primaryTransaction.project.setting;
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;transactionId&#x27;, &#x27;id&#x27;, &#x27;null&#x27;)
      });
    }

    if (dto.policyDiscountIds &amp;&amp; dto.policyDiscountIds.length) {
      const policyDiscounts : any[] &#x3D; await this.policyQueryService.findByIds(dto.policyDiscountIds);
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
        });
      }
      model.policyDiscounts &#x3D; policyDiscounts;
      

      // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà &amp; đất
      if (price &gt; 0 &amp;&amp; !housePrice &amp;&amp; !landPrice) {
        if (
          policyDiscounts.some(
            e &#x3D;&gt; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.HOUSE ||
            e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.LAND)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
          });
        }
      } else {
        if (
          policyDiscounts.some(e &#x3D;&gt; !e.discount.typeRealEstate || e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.DEFAULT)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
          });
        }
      }
    } else {
      model.policyDiscounts &#x3D; [];
    }
    if (dto.policyPaymentId) {
      const policyPayment &#x3D; await this.policyQueryService.findOne({ id: dto.policyPaymentId, type: PolicyTypeEnum.PAYMENT });
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;scheduleId&#x27;, &#x27;id&#x27;, dto.policyPaymentId)
        });
      }
      model.policyPayment &#x3D; this.transformPolicyPayment(
        model,
        price,
        projectSetting,
        dto.signedDate,
        receipt,
        policyPayment,
        housePrice,
        landPrice,
        dto.maintenanceFee);
    }

    if (!model.code) {
      const prefix &#x3D; &#x60;${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-&#x60;;
      model.code &#x3D; await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
    }
    model.name &#x3D; &#x60;${model.code}-${model.primaryTransaction.customer.personalInfo.name}&#x60;
    if(model.policyPayment) {
      model.paymentPercent &#x3D; this.calPaymentPercentage({
        installments: model.policyPayment.schedule.installments
      }, model);

    }

    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);
    let histories &#x3D; {
      propertyUnitId: primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: &quot;&quot;,
      contractStatus: &quot;Hợp đồng: &quot; + ContractTypeEnum.CREATED,
      actionName: ActionContractName.CREATE_CONTRACT,
      modifiedDate: new Date(),
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return {id: model.id};
  }

  async createPurchaseContract(user: any, dto: any, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;create service&#x27;));

    const depositDto: any &#x3D; await this.queryRepository.findOne({
      &#x27;deposit.id&#x27;: dto.id, 
      &#x27;type&#x27;: { $in: [ContractEnum.PURCHASE, ContractEnum.RENT] }
    });

    if(depositDto){
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.EXISTED, &#x27;Hợp đồng cọc&#x27;, &#x27;id&#x27;, dto.id)
      });
    }
    
    const model : any &#x3D; {...dto};
    this.commandId &#x3D; uuid.v4();
    model.modifiedBy &#x3D; user.id;
    model.createdBy &#x3D; user.id;
    model.createdDate &#x3D; new Date();
    let projectInfo : any &#x3D; {};
    let _fields : any &#x3D; {
      id: 1,
      ownership: 1,
    };
    const projectIds &#x3D; [... new Set(model.primaryTransaction.project.id)];
    projectInfo &#x3D; await this.propertyClient.sendDataPromise({ projectIds, _fields }, CmdPatternConst.LISTENER.GET_PROJECT_BY_IDS);
    if(projectInfo &amp;&amp; projectInfo.ownership &amp;&amp; projectInfo.ownership &#x3D;&#x3D;&#x3D; CommonConst.HINH_THUC.SO_HUU.SU_DUNG_VINH_VIEN){
      model.type &#x3D; ContractEnum.PURCHASE;
    }else{
      model.type &#x3D; ContractEnum.RENT;
    }

    model.deposit &#x3D; {
      id: dto.id,
      code: dto.code,
      name: dto.name
    };

    if (dto.policyPaymentId) {
      const policyPayment &#x3D; await this.policyQueryService.findOne({ id: dto.policyPaymentId, type: PolicyTypeEnum.PAYMENT });
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;scheduleId&#x27;, &#x27;id&#x27;, dto.policyPaymentId)
        });
      }
      // chỉ tính lại khi thay đổi chính sách
      if (dto.policyPaymentId !&#x3D;&#x3D; model.policyPayment.id) {
        const receiptList &#x3D; this.getTransferredReceiptList(model);
        model.policyPayment &#x3D; this.transformPolicyPayment(
          model,
          model.primaryTransaction.propertyUnit.price,
          {},
          dto.signedDate,
          receiptList,
          policyPayment,
          model.primaryTransaction.propertyUnit.housePrice,
          model.primaryTransaction.propertyUnit.landPrice);
      }
    }
    
    let prefix &#x3D; &#x60;${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_PURCHASE_CONTRACT}${model.primaryTransaction.project.code}-&#x60;;
    model.code &#x3D; await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
    model.name &#x3D; &#x60;${model.code}-${model.primaryTransaction.customer.personalInfo.name}&#x60;;
    model.status &#x3D; StatusContractEnum.INIT;
    delete(model.id);
    delete(model.interestCalculations);

    if(model.policyPayment) {
      model.paymentPercent &#x3D; this.calPaymentPercentage({
        installments: model.policyPayment.schedule.installments
      }, model);
    }

    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);
    return {id: model.id};
  }

  async requestApproveContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, &#x27;request approve ticket&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }

    if (oldDto.status !&#x3D;&#x3D; StatusContractEnum.INIT &amp;&amp; oldDto.status !&#x3D;&#x3D; StatusContractEnum.REJECTED) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;oldDto-Status&quot;)});
    }
    if (dto.status !&#x3D;&#x3D; StatusContractEnum.WAITING) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;
    oldDto.status &#x3D; dto.status;

    let result &#x3D; await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());

    // Lưu vào lịch sử sản phẩm
    let histories &#x3D; {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      contractStatus: &quot;Hợp đồng: &quot; + ContractTypeEnum.WAITTING_APPROVAL,
      actionName: ActionContractName.REQUEST_APPROVAL_CONTRACT,
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return result
  }

  async liquidationContract(userId: string, liquidation: any, actionName: string) {
    this.loggerService.log(this.context, &#x27;request approve ticket&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: liquidation.contract.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, liquidation.contract.id)});
    }

    if (oldDto.status !&#x3D;&#x3D; StatusContractEnum.APPROVED) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; userId;
    if (liquidation.type &#x3D;&#x3D;&#x3D; LiquidationTypeEnum.TRANSFER) {
      oldDto.status &#x3D; StatusContractEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND;
    } else {
      oldDto.status &#x3D; StatusContractEnum.LIQUIDATED;
      oldDto.liquidate &#x3D; new Date()
    }
    oldDto.liquidation &#x3D; liquidation;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async cloneContract(userId: string, liquidationDto: any, actionName: string, newTicket) {
    this.loggerService.log(this.context, &#x27;clone contract&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: liquidationDto.contract.id}).then(res &#x3D;&gt; res.toObject());
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, liquidationDto.contract.id)});
    }

    if (oldDto.status !&#x3D;&#x3D; StatusContractEnum.APPROVED) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }
    const model : any &#x3D; _.cloneDeep(oldDto);
    this.commandId &#x3D; uuid.v4();
    model.modifiedBy &#x3D; userId;
    model.createdDate &#x3D; new Date();
    model.modifiedDate &#x3D; new Date();
    model.primaryTransaction &#x3D; newTicket;
    model.liquidation &#x3D; liquidationDto;
    model.transferConfirmFromCustomer &#x3D; false;
    model.status &#x3D; LiquidationStatusEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND;
    if (model.policyPayment?.schedule?.installments) {
      model.policyPayment.schedule.installments &#x3D; model.policyPayment.schedule.installments.map(e &#x3D;&gt; ({
        ...e,
        totalTransfered: 0,
        receipts: []
      }));
    }

    model.oldContract &#x3D; {
      id: oldDto.id,
      code: oldDto.code,
      name: oldDto.name,
    }

    model.id &#x3D; uuid.v4();
    model.isTransferred &#x3D; true;

    // if (!model.code) {
      const prefix &#x3D; &#x60;${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_TRANSFER_CONTRACT}${liquidationDto.proposal.escrowTicket.project.code}-&#x60;;
      model.code &#x3D; await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
    // }
    
    model.name &#x3D; &#x60;${model.code}-${liquidationDto.proposal.customerTransfer.name}&#x60;
    await this.executeCommand(Action.CREATE, actionName, this.commandId, model);

    // update thông tin contract vào sản phẩm
    const propertyUnitId &#x3D; model.primaryTransaction.propertyUnit.id;
    this.propertyClient.sendDataPromise({
      id: propertyUnitId,
      contract: {
        id: model.id,
        code: model.code,
        type: model.type,
        isTransferred: model.isTransferred
      }}, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CONTRACT_IN_PROPERTY_UNIT);

    return {
      id: model.id,
      installments: oldDto.policyPayment?.schedule?.installments
    };
  }

  async approveContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, &#x27;approve ticket&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }

    if (oldDto.status !&#x3D;&#x3D; StatusContractEnum.WAITING) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }
    if (dto.status !&#x3D;&#x3D; StatusContractEnum.ACCOUNTANT_WAITING &amp;&amp; dto.status !&#x3D;&#x3D; StatusContractEnum.REJECTED) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }

    if (dto.status &#x3D;&#x3D;&#x3D; StatusContractEnum.ACCOUNTANT_WAITING) {
      //  create resident
        let projectInfo:any &#x3D;{};
        projectInfo &#x3D; await this.propertyClient.sendDataPromise(oldDto.primaryTransaction.project, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);
        let projectPassing :any&#x3D;{...oldDto.primaryTransaction.project};
        let newObject&#x3D;{};
        if(projectInfo){
          newObject &#x3D; {... oldDto.primaryTransaction,project:projectInfo,
                      customer:oldDto.primaryTransaction.customer,
                      propertyUnit:oldDto.primaryTransaction.propertyUnit,
                      contractId: oldDto.id
                    };
        }

          // send transaction to care service
        await this.careClient.sendDataPromise({
          primaryTransaction: newObject
        }, CmdPatternConst.CARE.GET_TRANSACTION);

        await this.socialClient.sendDataPromise({
          projectId: oldDto.primaryTransaction.project.id
        }, CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP);

      const customer &#x3D; await this.careClient.sendDataPromise({
        identityNumber: oldDto.primaryTransaction.customer.identities[0].value
      }, CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY);

      if (customer) {
        this.notificationClient.createNotificationCare(
          &quot;care_depositContract_Approved&quot;,
          null,
          customer.id,
          &quot;primary-contract&quot;,
          oldDto.id,
          {
            code: oldDto.primaryTransaction.propertyUnit.code,
            projectCode: oldDto.primaryTransaction.project.code,
            projectName: oldDto.primaryTransaction.project.name
          }
        );
      }

      // const {personalInfo, info} &#x3D; (oldDto[&#x27;primaryTransaction&#x27;].customer || {}) || {};
      // const { email, phone, name, identities } &#x3D; personalInfo;
      // const { address, birthday, gender } &#x3D; info;

      // if(email &amp;&amp; phone ) {
      //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)&#x3D;&gt; {

      //     if(!customer) {
      //       const payload &#x3D; { personalInfo: { name, email, phone, identities }, accessSystem: user.notiSystem ? [user.notiSystem] : null  };
      //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

      //     }else if(!customer.isActive) {
      //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
      //     }
      //   })
      // }
    }

    // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
    if (oldDto.policyPayment &amp;&amp; oldDto.policyPayment.schedule.installments[0].totalTransfered &gt;&#x3D; oldDto.policyPayment.schedule.installments[0].totalAmount) {
      oldDto.status &#x3D; StatusContractEnum.APPROVED;
    }else {
      oldDto.status &#x3D; dto.status;
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;
    oldDto.reason &#x3D; dto.reason;

    let result &#x3D; await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());

    // Lưu vào lịch sử sản phẩm
    let histories &#x3D; {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: oldDto.reason,
      contractStatus: oldDto.status &#x3D;&#x3D;&#x3D; StatusContractEnum.ACCOUNTANT_WAITING ? &quot;Hợp đồng: &quot; +  ContractTypeEnum.WAITING_COLLECT_MONEY : &quot;Hợp đồng: &quot; +  ContractTypeEnum.REJECTED,
      actionName: oldDto.status &#x3D;&#x3D;&#x3D; StatusContractEnum.ACCOUNTANT_WAITING ? ActionContractName.APPROVAL_CONTRACT: ActionContractName.REJECT_CONTRACT,
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return result;
  }

  async approveLiquidationContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, &#x27;approveLiquidationContract&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({ id: dto.id });
    if (!oldDto) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id) });
    }

    if (oldDto.status !&#x3D;&#x3D; StatusContractEnum.ACCOUNTANT_WAITING_CONFIRM_REFUND) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;) });
    }
    if (dto.status !&#x3D;&#x3D; StatusContractEnum.APPROVED &amp;&amp; dto.status !&#x3D;&#x3D; StatusContractEnum.LIQUIDATED) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;) });
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;
    oldDto.status &#x3D; dto.status;
    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
    const liquidation &#x3D; await this.liquidationQueryRepository.findOne({&quot;contract.id&quot;: dto.id})
    if (dto.status &#x3D;&#x3D;&#x3D; StatusContractEnum.LIQUIDATED) {
      const newContract: any &#x3D; await this.queryRepository.findOne({ &#x27;oldContract.id&#x27;: dto.id });
      // create history
      const history &#x3D; {
        from: {
          contract: {
            id: oldDto.id,
            code: oldDto.code,
            name: oldDto.name,
          },
          customer: oldDto.primaryTransaction.customer,
          date: liquidation.liquidationDate
        },
        to: {
          contract: {
            id: newContract.id,
            code: newContract.code,
            name: newContract.name
          },
          customer: newContract.primaryTransaction.customer,
          date: liquidation.liquidationDate
        },
        propertyUnit: {
          id: newContract.primaryTransaction.propertyUnit.id,
          code: newContract.primaryTransaction.propertyUnit.code
        }
      };
      await this.transferHistoryRepository.create(history);
      await this.liquidationQueryRepository.updateStatus({
        &quot;contract.id&quot;: dto.id
      }, StatusContractEnum.APPROVED);
    }
  }

  async approvePurchaseContract(user: any, dto: PrimaryContractStatusDto, actionName: string) {
    this.loggerService.log(this.context, &#x27;approve ticket&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }

    if (oldDto.status !&#x3D;&#x3D; StatusContractEnum.WAITING) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }
    if (dto.status !&#x3D;&#x3D; StatusContractEnum.ACCOUNTANT_WAITING &amp;&amp; dto.status !&#x3D;&#x3D; StatusContractEnum.REJECTED) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }

    // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
    const existedInstallmentToContractIndex &#x3D; oldDto.policyPayment ? (oldDto.policyPayment.schedule.installments as Array&lt;any&gt;).findIndex(item &#x3D;&gt; item.isToContract &#x3D;&#x3D;&#x3D; true) : -1;
    if (existedInstallmentToContractIndex &gt;&#x3D; 0 &amp;&amp; oldDto.policyPayment.schedule.installments[existedInstallmentToContractIndex].totalTransfered &gt;&#x3D; oldDto.policyPayment.schedule.installments[existedInstallmentToContractIndex].totalAmount) {
      oldDto.status &#x3D; StatusContractEnum.APPROVED;
    }else {
      oldDto.status &#x3D; dto.status;
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;
    oldDto.reason &#x3D; dto.reason;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async updateContractFiles(user: any, dto: UpdatePrimaryContractFileDto, actionName: string) {
    this.loggerService.log(this.context, &#x27;update contract file&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;
    oldDto.files &#x3D; dto.files;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async updateDeliveryDate(user: any, dto: UpdatePrimaryContractDeliveryDateDto, actionName: string) {
    this.loggerService.log(this.context, &#x27;update contract delivery date&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;
    oldDto.deliveryDate &#x3D; dto.deliveryDate;
    oldDto.filesDelivery &#x3D; dto.filesDelivery;
    if (oldDto.handoverStatus &amp;&amp; oldDto.handoverStatus !&#x3D; HandoverStatusEnum.handed) {
      oldDto.handoverStatus &#x3D; HandoverStatusEnum.handed;
      let syncConfig &#x3D; await this.syncErpClient.sendDataPromise(oldDto.primaryTransaction.project.id, CmdPatternConst.SYNC_ERP.GET_CAMPAIGN_ERP_BY_PROJECT_ID);
      if (syncConfig &amp;&amp; oldDto.syncErpData &amp;&amp; oldDto.syncErpData.contractid) {
        let property: any &#x3D; &#x27;&#x27;;
        if (oldDto.primaryTransaction &amp;&amp; oldDto.primaryTransaction.propertyUnit &amp;&amp; oldDto.primaryTransaction.propertyUnit.attributes &amp;&amp; oldDto.primaryTransaction.propertyUnit.attributes.length &gt; 21) {
          if (oldDto.primaryTransaction.propertyUnit.attributes[21].value) {
            property &#x3D; oldDto.primaryTransaction.propertyUnit.attributes[21].value;
          }
        }

        let data: any &#x3D; {
          formid: &#x27;contract01&#x27;,
          contractid: oldDto.syncErpData.contractid,
          property: property,
          statusdate: oldDto.deliveryDate ? moment(oldDto.deliveryDate).format(&#x27;YYYY-MM-DD hh:mm:ss A&#x27;) : &#x27;&#x27;,
          status: &#x27;BGI&#x27;,
          impstatus: &#x27;W&#x27;
        }
        let dataSendCRM: any &#x3D; {
          action: &#x27;contract01&#x27;,
          data: [data]
        }
        // send data sync erp
        await this.syncErpClient.sendDataPromise(dataSendCRM, CmdPatternConst.SYNC_ERP.SEND_REQUEST_TO_ERP);
      }


    }
    // Cập nhật đã bàn giao căn hộ
    let updatePropery &#x3D; {
      query: {id: oldDto.primaryTransaction.propertyUnit.id},
      model: {$set: {&quot;contract.handoverStatus&quot;: HandoverStatusEnum.handed}}
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);

    // Xác nhận bàn giao
    this.addHistoriesHandover(oldDto, user, dto.deliveryDate, HandoverScheduleStatusNameConst.HANDED_OVER, HandoverScheduleActionNameConst.ACCEPT_HANDOVER);

    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async addHistoriesHandover(contract, user, handoverStartTime, propertyStatusName, actionName) {
    let historiesHandover &#x3D; {
      propertyStatusName: propertyStatusName,
      actionName: actionName, // Hành động
      modifiedBy: user.id,
      modifiedByName: user.name, // Tên người thực hiện
      handoverStartTime: handoverStartTime
    }
    let updatePropery &#x3D; {
      query: {id: contract.primaryTransaction.propertyUnit.id},
      model: {$push: {historiesHandover: historiesHandover}}
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);
  }

  async updateContract(user: any, dto: UpdatePrimaryContractDto, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;update service&#x27;));
    let amount, price &#x3D; 0, housePrice &#x3D; 0, landPrice &#x3D; 0;
    let receipt &#x3D; [];
    let projectSetting &#x3D; {};
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});

    if (!oldDto
      || oldDto.status &#x3D;&#x3D;&#x3D; StatusContractEnum.APPROVED &amp;&amp; !user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_UPDATE_APPROVEMENT)) {
      throw new BadRequestException();
    }

    if (dto.primaryTransactionId) {
      const primaryTransaction &#x3D; await this.propertyClient.sendDataPromise({
        action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
        id: dto.primaryTransactionId
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID);
      if (!primaryTransaction) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;transactionId&#x27;, &#x27;id&#x27;, dto.primaryTransactionId)
        });
      }

      // if(primaryTransaction.contract &amp;&amp; primaryTransaction.contract.id !&#x3D;&#x3D; dto.id){
      //   throw new BadRequestException({
      //     errors: ErrorConst.Error(ErrorConst.EXISTED, &#x27;Hợp đồng&#x27;, &#x27;id&#x27;, dto.primaryTransactionId)
      //   });
      // }

      if(dto.customer2 &amp;&amp; dto.customer2.name){
        primaryTransaction.customer2 &#x3D; CommonUtils.getCustomerMapping(dto.customer2);
      }

      if (dto.calcContractPrice) {
        price &#x3D; primaryTransaction.propertyUnit.contractPrice;
        housePrice &#x3D; 0;
        landPrice &#x3D; 0;
      } else {
        price &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
        housePrice &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
        landPrice &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee &#x3D; {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      amount &#x3D; primaryTransaction.amount;
      receipt &#x3D; primaryTransaction.reciept;
      projectSetting &#x3D; primaryTransaction.project.setting;
      oldDto.primaryTransaction &#x3D; primaryTransaction;
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;transactionId&#x27;, &#x27;id&#x27;, &#x27;null&#x27;)
      });
    }

    if (dto.policyDiscountIds &amp;&amp; dto.policyDiscountIds.length) {
      const policyDiscounts : any[] &#x3D; await this.policyQueryService.findByIds(dto.policyDiscountIds);
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
        });
      }

      // Chỉ được áp dụng chiết khấu nhà hoặc đất nếu có giá nhà &amp; đất
      if (price &gt; 0 &amp;&amp; !housePrice &amp;&amp; !landPrice) {
        if (policyDiscounts.some(
            e &#x3D;&gt; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.HOUSE ||
            e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.LAND)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
          });
        }
      } else {
        if (
          policyDiscounts.some(e &#x3D;&gt; !e.discount.typeRealEstate || e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.DEFAULT)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
          });
        }
      }
      oldDto.policyDiscounts &#x3D; policyDiscounts;
    } else {
      oldDto.policyDiscounts &#x3D; [];
    }

    oldDto.calcCurrencyFirst &#x3D; dto.calcCurrencyFirst;
    oldDto.calcPriceVat &#x3D; dto.calcPriceVat;
    oldDto.calcContractPrice &#x3D; dto.calcContractPrice;
    oldDto.maintenanceFee &#x3D; dto.maintenanceFee;
    oldDto.changeInstallment &#x3D; dto.changeInstallment;
    if (dto.policyPaymentId) {
      const policyPayment &#x3D; await this.policyQueryService.findById(dto.policyPaymentId);
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;scheduleId&#x27;, &#x27;id&#x27;, dto.policyPaymentId)
        });
      }
      oldDto.policyPayment &#x3D; this.transformPolicyPayment(
        oldDto,
        price,
        projectSetting,
        dto.signedDate,
        receipt,
        policyPayment,
        housePrice,
        landPrice,
        dto.maintenanceFee);
    }
    
    oldDto.startDate &#x3D; dto.startDate;
    oldDto.expiredDate &#x3D; dto.expiredDate;
    oldDto.signedDate &#x3D; dto.signedDate;
    oldDto.transferType &#x3D; dto.transferType;
    oldDto.isDebtRemind &#x3D; dto.isDebtRemind;
    oldDto.isShowedInstallment &#x3D; dto.isShowedInstallment;
    oldDto.files &#x3D; dto.files;
    oldDto.companyInformation &#x3D; dto.companyInformation;
    oldDto.releaseStartDate &#x3D; dto.releaseStartDate;
    oldDto.releaseEndDate &#x3D; dto.releaseEndDate;



    if(oldDto.policyPayment ) {
      oldDto.paymentPercent &#x3D; this.calPaymentPercentage({
        installments: oldDto.policyPayment.schedule.installments
      }, oldDto);
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;

    let result &#x3D; await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
    // update hợp đồng mua bán
    this.updateMany({&#x27;deposit.id&#x27;: dto.id}, {
      releaseStartDate: dto.releaseStartDate,
      releaseEndDate: dto.releaseEndDate
    });

    // Lưu vào lịch sử sản phẩm.
    let histories &#x3D; {
      propertyUnitId: oldDto.primaryTransaction.propertyUnit.id,
      modifiedBy: user.id,
      modifiedByName: user.name,
      reason: dto.reason,
      contractStatus: oldDto.status &#x3D;&#x3D;&#x3D; StatusContractEnum.APPROVED ? &quot;Hợp đồng: &quot; +  ContractTypeEnum.APPROVED : &quot;Hợp đồng: &quot; +  ContractTypeEnum.CREATED,
      actionName: ActionContractName.EDIT_CONTRACT,
      modifiedDate: new Date(),
    }
    await this.propertyClient.sendDataPromise(histories, CmdPatternConst.LISTENER.UPDATE_HISTORIES_PROPERTY_UNIT);
    return result;
  }

  async updatePurchaseContract(user: any, dto: any, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;update service&#x27;));
    let amount, price &#x3D; 0, housePrice &#x3D; 0, landPrice &#x3D; 0;
    let receipt &#x3D; [];
    let projectSetting &#x3D; {};
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    
    if(!oldDto){
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;purchase&#x27;, &#x27;id&#x27;, dto.id)
      });
    }

    if(!oldDto){
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND)
      });
    }

    if (oldDto.status &#x3D;&#x3D;&#x3D; StatusContractEnum.APPROVED &amp;&amp; !user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_UPDATE_APPROVEMENT)) {
      throw new BadRequestException(ErrorConst.UNAUTHORIZED);
    }

    const depositDto: any &#x3D; await this.queryRepository.findOne({
      &#x27;deposit.id&#x27;: dto.depositId, 
      &#x27;type&#x27;: { $in: [ContractEnum.PURCHASE, ContractEnum.RENT] }
    });
    if(dto.deposit &amp;&amp; dto.depositId !&#x3D;&#x3D; dto.deposit.id &amp;&amp; depositDto &amp;&amp; depositDto.id !&#x3D;&#x3D; dto.depositId){
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.EXISTED, &#x27;Hợp đồng cọc&#x27;, &#x27;id&#x27;, dto.depositId)
      });
    }

    // check trường hợp có  primary transaction
    if (dto.primaryTransactionId) {
      const primaryTransaction &#x3D; await this.propertyClient.sendDataPromise({
        action: CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID,
        id: dto.primaryTransactionId
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_ID);
      if (!primaryTransaction) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;transactionId&#x27;, &#x27;id&#x27;, dto.primaryTransactionId)
        });
      }

      // if(primaryTransaction.contract &amp;&amp; primaryTransaction.contract.id !&#x3D;&#x3D; dto.id){
      //   throw new BadRequestException({
      //     errors: ErrorConst.Error(ErrorConst.EXISTED, &#x27;Hợp đồng&#x27;, &#x27;id&#x27;, dto.primaryTransactionId)
      //   });
      // }

      if(dto.customer2 &amp;&amp; dto.customer2.name){
        primaryTransaction.customer2 &#x3D; CommonUtils.getCustomerMapping(dto.customer2);
      }

      if (dto.calcContractPrice) {
        price &#x3D; primaryTransaction.propertyUnit.contractPrice;
        housePrice &#x3D; 0;
        landPrice &#x3D; 0;
      } else {
        price &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
        housePrice &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
        landPrice &#x3D; dto.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
      }
      dto.maintenanceFee &#x3D; {
        ...dto.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      amount &#x3D; primaryTransaction.amount;
      receipt &#x3D; primaryTransaction.reciept;
      projectSetting &#x3D; primaryTransaction.project.setting;
      oldDto.primaryTransaction &#x3D; primaryTransaction;
    } else {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;transactionId&#x27;, &#x27;id&#x27;, &#x27;null&#x27;)
      });
    }

    price &#x3D; oldDto.primaryTransaction.propertyUnit.price;


    // validate lại chiết khấu được áp dụng cho hợp đồng mua bán
    if (dto.policyDiscountIds &amp;&amp; dto.policyDiscountIds.length) {
      const policyDiscounts : any[] &#x3D; await this.policyQueryService.findByIds(dto.policyDiscountIds);
      if (!policyDiscounts || !policyDiscounts.length) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
        });
      }

      // Chỉ được áp dụng chiết khấu nhà hoặc đất nếu có giá nhà &amp; đất
      if (price &gt; 0 &amp;&amp; !housePrice &amp;&amp; !landPrice) {
        if (
          policyDiscounts.some(
            e &#x3D;&gt; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.HOUSE ||
            e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.LAND)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
          });
        }
      } else {
        if (
          policyDiscounts.some(e &#x3D;&gt; !e.discount.typeRealEstate || e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.DEFAULT)
        ) {
          throw new BadRequestException({
            errors: ErrorConst.Error(ErrorConst.DISCOUNT_POLICY_INVALID, &#x27;discountIds&#x27;, &#x27;id&#x27;, dto.policyDiscountId)
          });
        }
      }
      oldDto.policyDiscounts &#x3D; policyDiscounts;
    } else {
      oldDto.policyDiscounts &#x3D; [];
    }
    

    oldDto.calcCurrencyFirst &#x3D; dto.calcCurrencyFirst;
    oldDto.calcPriceVat &#x3D; dto.calcPriceVat;
    oldDto.calcContractPrice &#x3D; dto.calcContractPrice;
    oldDto.maintenanceFee &#x3D; dto.maintenanceFee // thay đổi đợt thu phí bảo trì cho hợp đồng mua bán
    if (dto.policyPaymentId) {
      const policyPayment &#x3D; await this.policyQueryService.findById(dto.policyPaymentId);
      if (!policyPayment) {
        throw new BadRequestException({
          errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;scheduleId&#x27;, &#x27;id&#x27;, dto.policyPaymentId)
        });
      }
      // chỉ tính lại khi thay đổi chính sách
      if (dto.policyPaymentId !&#x3D;&#x3D; oldDto.policyPayment.id) {
        const receiptList &#x3D; this.getTransferredReceiptList(oldDto);
        oldDto.policyPayment &#x3D; this.transformPolicyPayment(
          oldDto,
          price,
          {},
          dto.signedDate,
          receiptList,
          policyPayment,
          oldDto.primaryTransaction.propertyUnit.housePrice,
          oldDto.primaryTransaction.propertyUnit.landPrice);
      }
    } else {
      oldDto.policyPayment &#x3D; null;
    }
    
    oldDto.deposit &#x3D; {
      id: dto.depositId,
      code: dto.depositCode,
      name: dto.depositName
    };
    oldDto.name &#x3D; &#x60;${oldDto.code}-${dto.primaryTransaction.customer.personalInfo.name}&#x60;;

    oldDto.signedDate &#x3D; dto.signedDate;
    oldDto.isDebtRemind &#x3D; dto.isDebtRemind;
    oldDto.isShowedInstallment &#x3D; dto.isShowedInstallment;
    oldDto.files &#x3D; dto.files;
    oldDto.maintenanceFee &#x3D; dto.maintenanceFee;
    oldDto.releaseStartDate &#x3D; dto.releaseStartDate;
    oldDto.releaseEndDate &#x3D; dto.releaseEndDate;

    if(oldDto.policyPayment ) {
      oldDto.paymentPercent &#x3D; this.calPaymentPercentage({
        installments: oldDto.policyPayment.schedule.installments
      }, oldDto);
    }

    this.commandId &#x3D; uuid.v4();
    oldDto.modifiedBy &#x3D; user.id;
    
    await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());

    if (oldDto.deposit &amp;&amp; oldDto.deposit.id) {
      // update hợp đồng mua bán
      return await this.updateMany({&#x27;id&#x27;: oldDto.deposit.id}, {
        releaseStartDate: dto.releaseStartDate,
        releaseEndDate: dto.releaseEndDate
      });
    }
    return;
  }

  async updateInterestCalculation(user: any, dto: UpdateInterestCalculationDto, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;updated service&#x27;));
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }

    if(dto.interestCalculation.id){ // update
      let idx &#x3D; oldDto.interestCalculations.findIndex(i &#x3D;&gt; i.id &#x3D;&#x3D;&#x3D; dto.interestCalculation.id);
      if(idx &gt; -1){
        oldDto.interestCalculations[idx].interestReductionAmount &#x3D; dto.interestCalculation.interestReductionAmount;
        oldDto.interestCalculations[idx].remainingAmount &#x3D; oldDto.interestCalculations[idx].interestAmount - oldDto.interestCalculations[idx].interestAmountTransferred - oldDto.interestCalculations[idx].interestReductionAmount;
      }
    } else {
      if(!moment(dto.interestCalculation.startDate).toDate()){
        throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INTERNAL_DATE, &quot;interestCalculation&quot;, &quot;startDate&quot;, dto.interestCalculation.startDate.toString())});
      }
      if(!moment(dto.interestCalculation.endDate).toDate()){
        throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INTERNAL_DATE, &quot;interestCalculation&quot;, &quot;endDate&quot;, dto.interestCalculation.endDate.toString())});
      }
      dto.interestCalculation.id &#x3D; uuid.v4();
      dto.interestCalculation.createdDate &#x3D; new Date();
      const prefix &#x3D; CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_INTEREST;
      dto.interestCalculation.code &#x3D; await this.codeGenerateService.generateCode(&quot;&quot;, prefix);
      dto.interestCalculation.title &#x3D; dto.interestCalculation.code + &#x27;/&#x27; + oldDto.name;
      dto.interestCalculation.interestRate &#x3D; oldDto.primaryTransaction?.project?.setting?.interestRate || 0;
      dto.interestCalculation.interestReductionAmount &#x3D; 0;
      dto.interestCalculation.remainingAmount &#x3D; dto.interestCalculation.interestAmount - dto.interestCalculation.interestAmountTransferred - dto.interestCalculation.interestReductionAmount;
      dto.interestCalculation.status &#x3D; InterestCalculationStatusEnum.init;      
      oldDto.interestCalculations.push(dto.interestCalculation)
    }

    this.commandId &#x3D; uuid.v4();
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async approveInterestCalculation(user: any, dto: UpdateInterestCalculationDto, actionName: string) {
    this.loggerService.log(this.context, &#x27;approve interest calculation&#x27;);
    this.commandId &#x3D; uuid.v4();
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }

    if(!dto.interestCalculation.id){
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;interest&quot;, &quot;ID&quot;, dto.interestCalculation.id)});
    }

    let idx &#x3D; oldDto.interestCalculations.findIndex(i &#x3D;&gt; i.id &#x3D;&#x3D;&#x3D; dto.interestCalculation.id);
    if(idx &gt; -1){
      if(oldDto.interestCalculations[idx].status &#x3D;&#x3D; InterestCalculationStatusEnum.init){
        oldDto.interestCalculations[idx].status &#x3D; InterestCalculationStatusEnum.approved;
      }else{
        throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;interest calculation status&quot;)});
      }
    }

    this.commandId &#x3D; uuid.v4();
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async updatePropertyUnits(units: any[]) {
    const contracts &#x3D; await this.queryRepository.find({
      &quot;primaryTransaction.propertyUnit.id&quot;: {
        $in: units.map(e &#x3D;&gt; e.id)
      }
    });

    const update &#x3D; contracts.map(async(contract: any) &#x3D;&gt;  {
      const newUnit &#x3D; units.find(e &#x3D;&gt; e.id &#x3D;&#x3D;&#x3D; contract.primaryTransaction.propertyUnit.id);
      Object.assign(contract.primaryTransaction.propertyUnit, newUnit) ;
      if (contract?.policyPayment?.schedule?.installments &amp;&amp; contract?.policyPayment?.schedule?.installments?.length) {
        if (contract.maintenanceFee &amp;&amp; contract.maintenanceFee.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.PERCENT) {
          let price &#x3D; 0, housePrice &#x3D; 0, landPrice &#x3D; 0;
          if (contract.calcContractPrice) {
            price &#x3D; contract.primaryTransaction.propertyUnit.contractPrice;
            housePrice &#x3D; 0;
            landPrice &#x3D; 0;
          } else {
            price &#x3D; contract.calcPriceVat ? contract.primaryTransaction.propertyUnit.priceVat : contract.primaryTransaction.propertyUnit.price;
            housePrice &#x3D; contract.calcPriceVat ? contract.primaryTransaction.propertyUnit.housePriceVat : contract.primaryTransaction.propertyUnit.housePrice;
            landPrice &#x3D; contract.calcPriceVat ? contract.primaryTransaction.propertyUnit.landPriceVat : contract.primaryTransaction.propertyUnit.landPrice;
          }

          const policyPayment &#x3D; await this.policyQueryService.findOne({ id: contract.policyPayment?.id, type: PolicyTypeEnum.PAYMENT });
          if (policyPayment) {
            const newPaymentPolicy &#x3D;  this.transformPolicyPayment(
              contract,
              price,
              contract.primaryTransaction.project.setting,
              contract.signedDate,
              [],
              policyPayment,
              housePrice,
              landPrice,
              {
                ...contract.maintenanceFee.toJSON(),
                contractPriceForMaintenanceFee: newUnit.contractPriceForMaintenanceFee
              }
            );

            for (let index &#x3D; 0; index &lt; contract.policyPayment.schedule.installments.length; index++) {
              const element &#x3D; contract.policyPayment.schedule.installments[index];
              element.totalAmount &#x3D; newPaymentPolicy.schedule.installments[index].totalAmount;
            }
          }
        }
      }

      return this.queryRepository.update(contract);
    });
    await Promise.all(update);
  }

  async updateManyPrimaryContract(user: any, dto: UpdateManyPrimaryContract, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;update service&#x27;));
    if (dto.lstIdPrimaryContract &amp;&amp; dto.lstIdPrimaryContract.length &gt; 0) {
      await this.updateMany({id: {$in: dto.lstIdPrimaryContract}}, {
        releaseStartDate: dto.startDate,
        releaseEndDate: dto.endDate
      });
      // update hợp đồng mua bán
      return await this.updateMany({&#x27;deposit.id&#x27;: { $in: dto.lstIdPrimaryContract }}, {
        releaseStartDate: dto.startDate,
        releaseEndDate: dto.endDate
      });
    }
  }

  public transformPolicyPayment(contract, price, projectSetting, signedDate, receiptList, newPolicyPayment: any &#x3D; {}, housePrice &#x3D; 0, landPrice &#x3D; 0, maintenanceFee: any &#x3D; {}) {
    // không update lại đợt thanh toán nếu update và changeInstallment &#x3D; true
    if (contract.id &amp;&amp; contract.changeInstallment) {
      return contract.policyPayment;
    }

    // Kiểm tra nếu đã thanh toán đến đợt n thì CSTT mới phải có tối thiểu n đợt thanh toán
    if ((contract.policyPayment?.schedule?.installments || [])
        .reduce((acc, curr, index) &#x3D;&gt; acc &#x3D; curr.receipts &amp;&amp; curr.receipts.length ? index : acc, -1) &gt;
      (newPolicyPayment?.schedule?.installments?.length || 0)
      ) {
      throw new BadRequestException({
        errors: ErrorConst.Error(ErrorConst.PAYMENT_POLICY_INVALID, &#x27;policyPaymentId&#x27;, &#x27;id&#x27;, newPolicyPayment.id)
      });
    }


    // check chiết khấu
    if (contract.policyDiscounts &amp;&amp; contract.policyDiscounts.length) {
      // Trường hợp không có giá nhà &amp; đất &#x3D;&gt; áp dụng chính sách chiết khấu mặc định
      if (price &gt; 0 &amp;&amp; !housePrice  &amp;&amp; !landPrice) {
        let currencyValue &#x3D; contract.policyDiscounts
          .filter(e &#x3D;&gt; e.discount.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.CURRENCY &amp;&amp; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.DEFAULT)
          .reduce((acc, curr) &#x3D;&gt; acc + parseFloat(curr.discount.value), 0);
        let percentValue &#x3D; contract.policyDiscounts
          .filter(e &#x3D;&gt; e.discount.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.PERCENT &amp;&amp; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.DEFAULT)
          .reduce((acc, curr) &#x3D;&gt; acc + parseFloat(curr.discount.value), 0);

        if (contract.calcCurrencyFirst) {
          price -&#x3D; currencyValue;
          price -&#x3D; ((percentValue/100) * price);
        } else {
          price -&#x3D; ((percentValue/100) * price);
          price -&#x3D; currencyValue;
        }
      } else {
        let currencyValueHouse &#x3D; contract.policyDiscounts
          .filter(e &#x3D;&gt; e.discount.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.CURRENCY &amp;&amp; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.HOUSE)
          .reduce((acc, curr) &#x3D;&gt; acc + parseFloat(curr.discount.value), 0);
        let percentValueHouse &#x3D; contract.policyDiscounts
          .filter(e &#x3D;&gt; e.discount.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.PERCENT &amp;&amp; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.HOUSE)
          .reduce((acc, curr) &#x3D;&gt; acc + parseFloat(curr.discount.value), 0);

        let currencyValueLand &#x3D; contract.policyDiscounts
          .filter(e &#x3D;&gt; e.discount.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.CURRENCY &amp;&amp; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.LAND)
          .reduce((acc, curr) &#x3D;&gt; acc + parseFloat(curr.discount.value), 0);
        let percentValueLand &#x3D; contract.policyDiscounts
          .filter(e &#x3D;&gt; e.discount.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.PERCENT &amp;&amp; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.LAND)
          .reduce((acc, curr) &#x3D;&gt; acc + parseFloat(curr.discount.value), 0);

        if (contract.calcCurrencyFirst) {
          housePrice -&#x3D; currencyValueHouse;
          housePrice -&#x3D; ((percentValueHouse/100) * housePrice);

          landPrice -&#x3D; currencyValueLand;
          landPrice -&#x3D; ((percentValueLand/100) * landPrice);
        } else {
          housePrice -&#x3D; ((percentValueHouse/100) * housePrice);
          housePrice -&#x3D; currencyValueHouse;

          landPrice -&#x3D; ((percentValueLand/100) * landPrice);
          landPrice -&#x3D; currencyValueLand;
        }
      }

      // check trường hợp nhập nhầm chiết khấu quá cao khiến giá bị âm
      if (price &lt; 0) price &#x3D; 0;
      if (housePrice &lt; 0) housePrice &#x3D; 0;
      if (landPrice &lt; 0) landPrice &#x3D; 0;
    }
    let totalCurrencyBeforeToContract &#x3D; 0;
    signedDate &#x3D; signedDate ? moment(signedDate) : moment();
    let nextDate &#x3D; moment();
    let goContractIdx &#x3D; newPolicyPayment.schedule.installments.length;
    let totalAmount &#x3D; 0;

    const hasMaintenanceFromErp &#x3D; newPolicyPayment.schedule.installments.some(i &#x3D;&gt; i.name &#x3D;&#x3D;&#x3D; &#x27;99&#x27;);

    newPolicyPayment.schedule.installments.map((i, index) &#x3D;&gt; {
      if (i.isToContract) {
        goContractIdx &#x3D; index;
      }

      if ((housePrice &gt; 0 || landPrice &gt; 0) &amp;&amp; !i.type2) {
        i.type2 &#x3D; i.type;
        i.value2 &#x3D; i.value;
      }

      // Trường hợp tách thanh toán nhà &amp; đất
      if (i.type2) {
        // kiểm tra đã trừ đi tiền mặt đã đóng các đợt trước
        let flagSubTotalCurrencyBeforeToContract &#x3D; false;
        i.totalAmount &#x3D; 0;
        if (i.type &#x3D;&#x3D;&#x3D; ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount +&#x3D; i.value;
          totalCurrencyBeforeToContract +&#x3D; i.value;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount +&#x3D; i.isToContract ? ((i.value * housePrice) / 100) - totalCurrencyBeforeToContract : (i.value * housePrice) / 100;
          flagSubTotalCurrencyBeforeToContract &#x3D; i.isToContract;
        }
        if (i.type2 &#x3D;&#x3D;&#x3D; ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount +&#x3D; i.value2;
          totalCurrencyBeforeToContract +&#x3D; i.value2;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount +&#x3D; i.isToContract &amp;&amp; !flagSubTotalCurrencyBeforeToContract ? ((i.value2 * landPrice) / 100) - totalCurrencyBeforeToContract : (i.value2 * landPrice) / 100;
        }
      } else {
        if (i.type &#x3D;&#x3D;&#x3D; ScheduleInstallmentEnum.CURRENCY) {
          // tiền mặt, lưu tiền vào đợt và tính tổng để trừ đi khi ra HDMB
          i.totalAmount &#x3D; i.value;
          totalCurrencyBeforeToContract +&#x3D; i.value;
        } else {
          // Check xem có phải lần ra hợp đồng không, nếu đúng thì trừ đi tiền mặt đã đóng các đợt trước
          i.totalAmount &#x3D; i.isToContract ? ((i.value * price) / 100) - totalCurrencyBeforeToContract : (i.value * price) / 100;
        }
      }

      if ((index &#x3D;&#x3D;&#x3D; newPolicyPayment.schedule.installments.length - 1 &amp;&amp; !hasMaintenanceFromErp) || (hasMaintenanceFromErp &amp;&amp; index &#x3D;&#x3D;&#x3D; newPolicyPayment.schedule.installments.length - 2)) {
        i.totalAmount &#x3D; (landPrice + housePrice &gt; 0 ? landPrice + housePrice : price) - totalAmount
      } else {
        i.totalAmount &#x3D; Math.round(i.totalAmount);
        totalAmount +&#x3D; i.totalAmount;
      }

      if (maintenanceFee.stagePayment &amp;&amp; maintenanceFee.stagePayment &#x3D;&#x3D; i.name &amp;&amp; maintenanceFee.type &amp;&amp; maintenanceFee.value) {
        i.name &#x3D; &#x60;${i.name} (Bao gồm Phí bảo trì)&#x60;;
        if (maintenanceFee.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.CURRENCY) {
          i.totalAmount +&#x3D; maintenanceFee.value;
        } else if (maintenanceFee.type &#x3D;&#x3D;&#x3D; DiscountTypeEnum.PERCENT) {
          const maintenanceValue: any &#x3D; maintenanceFee.contractPriceForMaintenanceFee * maintenanceFee.value / 100;
          i.totalAmount +&#x3D; Math.round(maintenanceValue);
        }
      }

      i.totalTransfered &#x3D; 0;
      i.receipts &#x3D; [];
      let needTransferred &#x3D; i.totalAmount - i.totalTransfered;

      // khi thay đổi chính sách thanh toán, đem các phiếu thu đã thanh toán sang chính sách mới
      if(receiptList.length &gt; 0) {
        receiptList.map((r, idx) &#x3D;&gt; {
          if(needTransferred &gt; 0 &amp;&amp; r.amount&gt; 0) {
            if (r.amount &lt;&#x3D; needTransferred) {
              i.receipts.push(Object.assign({}, {...r}));
              i.totalTransfered +&#x3D; r.amount;
              needTransferred -&#x3D; r.amount;
              receiptList[idx].amount &#x3D; 0;
            } else {
              receiptList[idx].amount &#x3D; r.amount - needTransferred;
              i.receipts.push(Object.assign({}, {...r}, {amount: needTransferred}));
              i.totalTransfered +&#x3D; needTransferred;
              needTransferred &#x3D; 0;
            }
          }
        });
      }

      if(contract.type &#x3D;&#x3D;&#x3D; ContractEnum.DEPOSIT) {
        if (index &#x3D;&#x3D;&#x3D; 0) {
          // tính ngày hạn thanh toán của Đợt 1
          if(i.expiredDateType &#x3D;&#x3D;&#x3D; expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            i.paymentDueDate &#x3D; moment(i.exactDays).toDate()
            nextDate &#x3D; moment(i.exactDays)
          } else {
            const projectBonusDate &#x3D; projectSetting.dateToIssue || 0;
            i.paymentDueDate &#x3D; projectBonusDate !&#x3D;&#x3D; 0 ? signedDate.add(projectBonusDate, &#x27;days&#x27;).toDate() : signedDate.toDate();
            nextDate &#x3D; signedDate.clone();
          }

        } else {
          // tính ngày hạn thanh toán của các đợt khác
          if(i.expiredDateType !&#x3D;&#x3D; expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            nextDate &#x3D; nextDate.add(i.expiredDays, &#x27;days&#x27;);
            i.paymentDueDate &#x3D; nextDate.toDate();
          } else if(i.expiredDateType &#x3D;&#x3D;&#x3D; expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            nextDate &#x3D; moment(i.exactDays)
            i.paymentDueDate &#x3D; nextDate.toDate();
          }

        }
      } else {
        // HĐ không phải HĐ cọc
        if(index &gt; goContractIdx) {
          if (index &#x3D;&#x3D;&#x3D; (goContractIdx + 1)) {
            // tính ngày hạn thanh toán của Đợt tiếp sau khi ra HĐMB
            if(i.expiredDateType !&#x3D;&#x3D; expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate &#x3D; nextDate.add(i.expiredDays, &#x27;days&#x27;);
              i.paymentDueDate &#x3D; nextDate.toDate();
            } else if(i.expiredDateType &#x3D;&#x3D;&#x3D; expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate &#x3D; moment(i.exactDays)
              i.paymentDueDate &#x3D; nextDate.toDate();
            }
          } else {
            // tính ngày hạn thanh toán của các đợt khác
              if(i.expiredDateType !&#x3D;&#x3D; expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
            nextDate &#x3D; nextDate.add(i.expiredDays, &#x27;days&#x27;);
            i.paymentDueDate &#x3D; nextDate.toDate();
            } else if(i.expiredDateType &#x3D;&#x3D;&#x3D; expiredDateType.EXPIRED_DATE_TYPE_EXACT) {
              nextDate &#x3D; moment(i.exactDays)
              i.paymentDueDate &#x3D; nextDate.toDate();
            }
          }
        } else {
          // lấy hạn thanh toán của chính sách cũ
          i.paymentDueDate &#x3D; contract.policyPayment.schedule.installments[index].paymentDueDate;
        }
      }

      return i;
    });

    // Nếu HĐ đã duyệt, chuyển hết phiếu thu cũ sang các đợt tương ứng
    (contract.policyPayment?.schedule?.installments || []).forEach((element, index) &#x3D;&gt; {
      if (element.receipts &amp;&amp; element.receipts.length) {
        newPolicyPayment.schedule.installments[index].receipts &#x3D; element.receipts;
        newPolicyPayment.schedule.installments[index].totalTransfered &#x3D; element.totalTransfered;
      }
    });

    return newPolicyPayment;
  }

  private getTransferredReceiptList(contract) {
    // lấy các phiếu thu đã duyệt
    let transferredReceipt &#x3D; [];
    contract.policyPayment.schedule.installments.map((i, index) &#x3D;&gt; {
      i.receipts &#x3D; i.receipts || [];
      i.receipts.map(r &#x3D;&gt; {
        if(r.status &#x3D;&#x3D;&#x3D; &#x27;TRANSFERED&#x27;) {
          const idx &#x3D; transferredReceipt.findIndex(re &#x3D;&gt; re.id &#x3D;&#x3D;&#x3D; r.id);
          if(idx !&#x3D;&#x3D; -1) {
            transferredReceipt[idx].amount +&#x3D; r.amount;
          } else {
            transferredReceipt.push(Object.assign({}, {...r}));
          }
        }
      });
    });
    return transferredReceipt
  }

  async deleteContract(user: any, id: string, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;delete service&#x27;));
    const oldDto: any &#x3D; await this.queryRepository.findOne({id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, id)});
    }
    if (oldDto.status !&#x3D;&#x3D; StatusContractEnum.INIT) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;contractStatus&quot;)});
    }
    let model : any &#x3D; {id};
    
    if(oldDto.primaryTransaction){
      model.primaryTransactionId &#x3D; oldDto.primaryTransaction.id;
    }

    this.commandId &#x3D; uuid.v4();
    model.modifiedBy &#x3D; user.id;
    return await this.executeCommand(Action.DELETE, actionName, this.commandId, model);
  }

  async deleteInterestCalculation(user: any, dto: UpdateInterestCalculationDto, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;delete service&#x27;));
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: dto.id});
    if (!oldDto) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, dto.id)});
    }
    
    if(!dto.interestCalculation.id){
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;interest&quot;, &quot;ID&quot;, dto.interestCalculation.id)});
    }

    let idx &#x3D; oldDto.interestCalculations.findIndex(i &#x3D;&gt; i.id &#x3D;&#x3D;&#x3D; dto.interestCalculation.id);
    if(idx &gt; -1){
      if(oldDto.interestCalculations[idx].status &#x3D;&#x3D; InterestCalculationStatusEnum.init){
        oldDto.interestCalculations.splice(idx, 1);
      }else{
        throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID_INPUT, &quot;interest calculation status&quot;)});
      }
    }

    this.commandId &#x3D; uuid.v4();
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, oldDto.toObject());
  }

  async syncContractTransaction(transaction: any) {
    const contract: any &#x3D; await this.queryRepository.findOne({ id: transaction.contractId });
    if (!contract || !contract.policyPayment || !contract.policyPayment.schedule) {
      return null;
    }
    let updatePropertyContract &#x3D; false;
    let installments &#x3D; contract.policyPayment.schedule.installments || [];
    let interestCalculations &#x3D; contract.interestCalculations || [];
    if(installments.length &gt; 0) {
      const existedInstallmentIndex &#x3D; (contract.policyPayment.schedule.installments as Array&lt;any&gt;).findIndex(item &#x3D;&gt; item.name &#x3D;&#x3D;&#x3D; transaction.paymentBatch);
      const existedInstallmentToContractIndex &#x3D; (contract.policyPayment.schedule.installments as Array&lt;any&gt;).findIndex(item &#x3D;&gt; item.isToContract &#x3D;&#x3D;&#x3D; true);
      // transform lại data
      transaction.amount &#x3D; transaction.money;
      transaction.receiptNum &#x3D; transaction.code;
      transaction.receiptDate &#x3D; transaction.collectMoneyDate;
      const simpleTransaction: any &#x3D; (({ id, amount, code, status, type, receiptNum, receiptDate }) &#x3D;&gt; ({ id, amount, code, status, type, receiptNum, receiptDate }))(transaction);
      if (existedInstallmentIndex !&#x3D;&#x3D; -1) {
        // Chọn Đợt
        // lưu phiếu thu vào Đợt
        // check tồn tại phiếu thu cùng id chưa
        const existedTransactionIndex &#x3D; installments[existedInstallmentIndex].receipts.findIndex(item &#x3D;&gt; item.id &#x3D;&#x3D;&#x3D; transaction.id);
        if (existedTransactionIndex &#x3D;&#x3D;&#x3D; -1) {
          // chưa tồn tại, thêm mới
          installments[existedInstallmentIndex].receipts.push(Object.assign({}, simpleTransaction));
        } else {

          // reject phiếu đã thu
          if (simpleTransaction.status &#x3D;&#x3D;&#x3D; TransactionStatusEnum.processing &amp;&amp;
            installments[existedInstallmentIndex].receipts[existedTransactionIndex].status &#x3D;&#x3D;&#x3D; TransactionStatusEnum.transfered ){
            installments[existedInstallmentIndex].totalTransfered -&#x3D; simpleTransaction.amount;
          }
          // đã tồn tại, update trạng thái
          installments[existedInstallmentIndex].receipts[existedTransactionIndex].status &#x3D; simpleTransaction.status;
        }
        // điều chỉnh số tiền đã thu, chỉ khi đã duyệt
        if (simpleTransaction.status &#x3D;&#x3D;&#x3D; TransactionStatusEnum.transfered) {
          installments[existedInstallmentIndex].totalTransfered &#x3D; installments[existedInstallmentIndex].totalTransfered || 0;
          installments[existedInstallmentIndex].totalTransfered +&#x3D; transaction.amount;
          if (contract.type &#x3D;&#x3D;&#x3D; ContractEnum.DEPOSIT) { // HĐ cọc
            // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
            if (existedInstallmentIndex &#x3D;&#x3D;&#x3D; 0 &amp;&amp; installments[0].totalTransfered &gt;&#x3D; installments[0].totalAmount) {
              contract.status &#x3D; contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract &#x3D; true;
            }
          } else { // HDMB/Thuê
            // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
            if (existedInstallmentIndex &#x3D;&#x3D;&#x3D; existedInstallmentToContractIndex &amp;&amp; installments[existedInstallmentToContractIndex].totalTransfered &gt;&#x3D; installments[existedInstallmentToContractIndex].totalAmount) {
              contract.status &#x3D; contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract &#x3D; true;
            }
          }
          // kiểm tra có thanh toán lãi hay không.
          if(transaction.isInterest){
            const ids &#x3D; transaction.interestCalculations.map(i &#x3D;&gt; i.id);
            contract.interestCalculations &#x3D; interestCalculations.map(i &#x3D;&gt; {
              if(ids.includes(i.id) &amp;&amp; i.installmentName &#x3D;&#x3D;&#x3D; transaction.paymentBatch ){
                i.status &#x3D; InterestCalculationStatusEnum.transfered;
                i.receipts &#x3D; simpleTransaction
              }
              return i;
            });
          }
          if(transaction.amount !&#x3D;&#x3D; 0) {
            // gửi thông báo thanh toán thành công CarePlus
            const customer &#x3D; await this.careClient.sendDataPromise({
              identityNumber: contract.primaryTransaction.customer.identities[0].value
            }, CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY);

            if(customer &amp;&amp; customer.id) {
              this.notificationClient.createNotificationCare(
                &quot;care_PrimaryContract_InstallmentPaymentSuccess&quot;,
                null,
                customer.id,
                &quot;primary-contract&quot;,
                contract.id,
                {
                  installment: existedInstallmentIndex + 1,
                  productCode: contract.primaryTransaction.propertyUnit.code,
                  projectCode: contract.primaryTransaction.project.code,
                  amount: transaction.amount.toLocaleString().split(&#x27;,&#x27;).join(&#x27;.&#x27;),
                  receiptCode: simpleTransaction.code,
                })
            }
          }
        }
      } else {
        // Đợt &quot;Khác&quot;
        let money &#x3D; transaction.money;
        installments &#x3D; installments.map((i, idx) &#x3D;&gt; {
          i.totalTransfered &#x3D; i.totalTransfered || 0;
          // Nếu đợt chưa thanh toán đủ tiền
          if (money &gt; 0 &amp;&amp; (i.totalAmount &gt; i.totalTransfered)) {
            // số tiền cần thanh toán
            const needTransfer &#x3D; i.totalAmount - i.totalTransfered;
            // số tiền có thể thanh toán
            const canTransfer &#x3D; money &gt;&#x3D; needTransfer ? needTransfer : money;
            // điều chỉnh đúng số tiền và lưu phiếu thu vào Đợt
            simpleTransaction.amount &#x3D; canTransfer;
            // check tồn tại phiếu thu cùng id chưa
            const existedTransactionIndex &#x3D; i.receipts.findIndex(item &#x3D;&gt; item.id &#x3D;&#x3D;&#x3D; transaction.id);
            if (existedTransactionIndex &#x3D;&#x3D;&#x3D; -1) {
              // chưa tồn tại, thêm mới
              i.receipts.push(Object.assign({}, simpleTransaction));
            } else {

              // reject phiếu đã thu
              if (simpleTransaction.status &#x3D;&#x3D;&#x3D; TransactionStatusEnum.processing &amp;&amp;
                  i.receipts[existedTransactionIndex].status &#x3D;&#x3D;&#x3D; TransactionStatusEnum.transfered ){
                    i.totalTransfered -&#x3D; canTransfer;
              }
              // đã tồn tại, update trạng thái
              i.receipts[existedTransactionIndex].status &#x3D; simpleTransaction.status;
            }
            // điều chỉnh số tiền đã thu, chỉ khi đã duyệt
            if (simpleTransaction.status &#x3D;&#x3D;&#x3D; TransactionStatusEnum.transfered) {
              i.totalTransfered +&#x3D; canTransfer;
            }
            money -&#x3D; canTransfer;
          }

          if(contract.type &#x3D;&#x3D;&#x3D; ContractEnum.DEPOSIT){ // HĐ cọc
            // check đủ tiền đợt 1 chưa để đổi trạng thái thành đã duyệt
            if (idx &#x3D;&#x3D;&#x3D; 0 &amp;&amp; i.totalTransfered &gt;&#x3D; i.totalAmount) {
              contract.status &#x3D; contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract &#x3D; true;
            }
          }else{ // HDMB/Thuê
            // check đủ tiền đợt ra HDMB chưa để đổi trạng thái thành đã duyệt
            if (idx &#x3D;&#x3D;&#x3D; existedInstallmentToContractIndex &amp;&amp; i.totalTransfered &gt;&#x3D; i.totalAmount) {
              contract.status &#x3D; contract.isTransferred ? contract.status : StatusContractEnum.APPROVED;
              updatePropertyContract &#x3D; true;
            }
          }
          return i;
        });
        if(simpleTransaction.status &#x3D;&#x3D;&#x3D; TransactionStatusEnum.transfered &amp;&amp; transaction.amount !&#x3D;&#x3D; 0) {
          // gửi thông báo thanh toán thành công CarePlus
          const customer &#x3D; await this.careClient.sendDataPromise({
            identityNumber: contract.primaryTransaction.customer.identities[0].value
          }, CmdPatternConst.CARE.GET_CUSTOMER_BY_IDENTITY);

          if (customer &amp;&amp; customer.id) {
            this.notificationClient.createNotificationCare(
              &quot;care_PrimaryContract_MultiPaymentSuccess&quot;,
              null,
              customer.id,
              &quot;primary-contract&quot;,
              contract.id,
              {
                productCode: contract.primaryTransaction.propertyUnit.code,
                projectCode: contract.primaryTransaction.project.code,
                amount: transaction.amount.toLocaleString().split(&#x27;,&#x27;).join(&#x27;.&#x27;),
                receiptCode: simpleTransaction.code,
              })
          }
        }
      }
      // reverse lại đúng thứ tự và save
      contract.policyPayment.schedule.installments &#x3D; installments;
      if (contract.deposit) {
        // nếu là HĐMB, update Lịch sử thanh toán cho HĐC
        const depositContract &#x3D; await this.queryRepository.findOne({ id: contract.deposit.id });
        // chỉ update khi cùng Chính sách thanh toán
        if (depositContract &amp;&amp; depositContract.policyPayment &amp;&amp; depositContract.policyPayment.id &#x3D;&#x3D;&#x3D; contract.policyPayment.id) {
          depositContract.policyPayment &#x3D; contract.policyPayment;
        }
        if (contract.status &#x3D;&#x3D;&#x3D; StatusContractEnum.APPROVED) {
          depositContract.purchase &#x3D; {
            id: contract.id,
            code: contract.code,
            name: contract.name,
          };
          depositContract.isDebtRemind &#x3D; false;
        }
        this.executeCommand(Action.UPDATE, null, this.commandId, depositContract);
      }

      contract.paymentPercent &#x3D; this.calPaymentPercentage({
        installments: contract.policyPayment.schedule.installments
      }, contract);

    }

    await this.executeCommand(Action.UPDATE, null, this.commandId, contract);
    if(updatePropertyContract) {
      // update thông tin contract vào sản phẩm
      const propertyUnitId &#x3D; contract.primaryTransaction.propertyUnit.id;
      this.propertyClient.sendDataPromise({
        id: propertyUnitId,
        contract: {
          id: contract.id,
          code: contract.code,
          type: contract.type,
          isTransferred: contract.isTransferred
        }
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CONTRACT_IN_PROPERTY_UNIT)
    }
    return contract;
  }

   calPaymentPercentage(params: { installments: any[] }, contract?) {
    const { installments &#x3D; [] } &#x3D; params;

    // cal total amount
    const price &#x3D; installments.reduce(function (prev, cur) {
      return prev + cur.totalAmount;
    }, 0);

    let totalPercentage &#x3D; 0;
    let currentTotalTransfered &#x3D; 0;

    let resetedTotalPercent &#x3D; false;
    for (let index &#x3D; 0; index &lt; installments.length; index++) {
      const inst &#x3D; installments[index];
      currentTotalTransfered &#x3D; currentTotalTransfered + Number(inst.totalTransfered);

      if (inst.totalTransfered &lt; inst.totalAmount || inst.type &#x3D;&#x3D; &#x27;currency&#x27; || inst.totalAmount &#x3D;&#x3D;&#x3D; 0) {
        if (inst.type &#x3D;&#x3D;&#x3D; &#x27;percent&#x27; &amp;&amp; index &gt; 0) {
          const oldInstallments &#x3D; installments.slice(0, index + 1);

          let remainAmount &#x3D; oldInstallments.reduce(function (prev, cur) {
            return prev + cur.totalTransfered - + cur.totalAmount;
          }, 0);
          remainAmount &#x3D; remainAmount &gt; 0 ? remainAmount : 0;
          resetedTotalPercent &#x3D; true;
          if ((inst.totalTransfered + remainAmount) &lt; inst.totalAmount) {
            totalPercentage &#x3D; price !&#x3D;&#x3D; 0 ? totalPercentage + Math.round((Number(inst.totalTransfered + remainAmount) * 100) / price) : 0;
          } else {
            totalPercentage &#x3D; totalPercentage + Number(inst.value);
          }
        } else {
          totalPercentage &#x3D; price !&#x3D;&#x3D; 0 ? totalPercentage + Math.round((Number(inst.totalTransfered) * 100) / price) : 0;
        }
      } else {
        if (resetedTotalPercent) {
          totalPercentage &#x3D; totalPercentage + Number(inst.value);
        } else {
          resetedTotalPercent &#x3D; true;
          totalPercentage &#x3D; totalPercentage + Number(inst.value);
        }
      }
    }

    if (installments.length &#x3D;&#x3D;&#x3D; 1) {
      totalPercentage &#x3D; price !&#x3D;&#x3D; 0 ? totalPercentage + Math.round((currentTotalTransfered * 100) / price) : 0;
    }

    // Đồng bộ % thanh toán hợp đồng &#x3D;&gt; sản phẩm
    this.syncTotalPaymentToProperty(totalPercentage, contract);

    return totalPercentage;
  }

  // Đồng bộ % thanh toán hợp đồng  &#x3D;&gt; sản phẩm
  async syncTotalPaymentToProperty(totalPercentage, contract) {

    let updatePropery &#x3D; {
      query: {id: contract.primaryTransaction.propertyUnit.id},
      model: {$set: {totalPercentageContract: totalPercentage}}
    }
    this.propertyClient.sendDataPromise(updatePropery, CmdPatternConst.PROPERTY.UPDATE_ONE_PROPERTY_UNIT_BY_QUERY);
  }

  async sendDeliveryNotify(user, dto: SendPrimaryContractDeliveryNotifyDto, actionName) {
    this.loggerService.log(this.context, &#x27;send Delivery Notify&#x27;);

    // lấy thiết lập bàn giao
    const handOverSetting &#x3D; await this.handoverQueryService.findOneByQuery({id: dto.id, status: true});

    if(!handOverSetting) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;Thiết lập&#x27;) });
    }

    const project &#x3D; await this.propertyClient.sendDataPromise({ id: handOverSetting.project.id }, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);

    if(!project) {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;Dự án&#x27;) });
    }

    let contracts &#x3D; dto.contracts;
    const ids &#x3D; contracts.map(contract &#x3D;&gt; contract.id);
    const notifyData &#x3D; [];
    const mailerData &#x3D; [];

    // Người dùng chỉ có thể chọn các căn có trạng thái &quot;Đã lên lịch&quot;
    const listContractSchedule &#x3D; await this.queryRepository.find({id: {$in: ids}, handoverStatus: HandoverStatusEnum.scheduled});
    const listContractScheduleIds  &#x3D; listContractSchedule.map(x &#x3D;&gt; x.id);
    contracts &#x3D; contracts.filter(x &#x3D;&gt; listContractScheduleIds.includes(x.id));

    for (let contract of contracts) {
      const time &#x3D; contract.deliveryDate ? moment(contract.deliveryDate).format(&#x27;HH:mm&#x27;) : &#x27;&#x27;;
      const date &#x3D; contract.deliveryDate ? moment(contract.deliveryDate).format(&#x27;DD/MM/YYYY&#x27;) : &#x27;&#x27;;
      const { code, identityNumber, customerName } &#x3D; contract;

      const session  &#x3D; handOverSetting.sessions.find(ses &#x3D;&gt; (ses.name &#x3D;&#x3D;&#x3D; contract.session &amp;&amp; ses.status));
      const smsTemplate &#x3D; handOverSetting ? handOverSetting.smsTemplate : &#x27;&#x27;;
      const smsBrandName &#x3D; handOverSetting ? handOverSetting.smsBrandName : &#x27;&#x27;;
      const emailTitle &#x3D; handOverSetting ? handOverSetting.emailTitle : &#x27;&#x27;;
      const emailFrom &#x3D; handOverSetting ? handOverSetting.emailFrom : &#x27;&#x27;;
      const emailCC &#x3D; handOverSetting ? handOverSetting.emailCC : &#x27;&#x27;;
      let emailBCC &#x3D; handOverSetting ? handOverSetting.emailBCC : &#x27;&#x27;;
      const emailTemplate &#x3D; handOverSetting &amp;&amp; handOverSetting.emailTemplate ? handOverSetting.emailTemplate.replace(/(?:\r\n|\r|\n)/g, &#x27;&lt;br&gt;&#x27;) : &#x27;&#x27;;
      const hotline &#x3D; handOverSetting.hotline ? handOverSetting.hotline : &#x27;&#x27;;
      const investorName &#x3D; project.careInvestor ? project.careInvestor.name : &#x27;&#x27;;
      const investorAddress &#x3D; project.careInvestor ? project.careInvestor.address : &#x27;&#x27;;
      const investorWebsite &#x3D; project.careInvestor ? project.careInvestor.website : &#x27;&#x27;;

      // gửi noti cho app
      this.notificationClient.createNotificationCare(
        &quot;care_deliveryReminderSend&quot;,
        null,
        identityNumber,
        &quot;primary-contract&quot;,
        contract.id,
        { code, time, date, hotline, identityNumber }
      );

      notifyData.push({
        phone: contract.phone,
        contentSMS: smsTemplate,
        brandName: smsBrandName,
        code, time, date, hotline,customerName
      });

      // Thông tin nhân viên bàn giao
      let handoverSchedule &#x3D; await this.handoverScheduleQueryService.findOneByQuery({&quot;handoverApartment.id&quot;: contract.id});
      if (handoverSchedule) {
        emailBCC &#x3D; &#x60;${emailBCC};${handoverSchedule.supportEmployee.email}&#x60;;
      }

      mailerData.push({
        email: contract.email,
        emailTitle,
        emailFrom,
        emailCC,
        emailBCC,
        contentMail: emailTemplate,
        code, time, date, hotline, investorName, investorAddress, investorWebsite, customerName
      });
    }

    // gửi SMS
    console.log(&#x27;SEND_SMS_DELIVERY&#x27;, notifyData)
    this.notificationClient.sendDeliverySMS({notifyData}, user);

    //gửi email
    console.log(&#x27;SEND_MAIL_DELIVERY&#x27;, mailerData)
    this.mailerClient.sendDataPromise({mailerData}, CmdPatternConst.MAILER.SEND_MAIL_DELIVERY, user);


    // update trạng thái đã gửi thông báo
    return await this.updateMany({id: {$in: ids}}, {
      isSendDelivery: true,
      modifiedDate: new Date(),
      modifiedBy: user.id
    })
  }

  async updateMany(query, updateQuery) {
    return await this.queryRepository.updateMany(query, updateQuery);
  }

  async updateDepositConfirm(data) {
    this.loggerService.log(this.context, clc.green(&#x27;updateDepositConfirm&#x27;));
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: data.id});
    if (!oldDto) {
      return;
    }
    oldDto.depositConfirmFromCustomer &#x3D; data.status;

    this.commandId &#x3D; uuid.v4();
    return await this.executeCommand(Action.UPDATE, null, this.commandId, oldDto);
  }

  async updateTransferConfirm(data) {
    this.loggerService.log(this.context, clc.green(&#x27;updateTransferConfirm&#x27;));
    const oldDto: any &#x3D; await this.queryRepository.findOne({id: data.id});
    if (!oldDto) {
      return;
    }
    oldDto.transferConfirmFromCustomer &#x3D; data.status;

    this.commandId &#x3D; uuid.v4();
    return await this.executeCommand(Action.UPDATE, null, this.commandId, oldDto);
  }

  async updateShowReceipt(dto: UpdateShowReceiptDto) {
    this.loggerService.log(this.context, clc.green(&#x27;updateShowReceipt&#x27;));
    let oldData &#x3D; await this.queryRepository.findOne({
      id: dto.id,
      &quot;policyPayment.schedule.installments.receipts.id&quot;: dto.receiptId
    });

    let installments: any[] &#x3D; oldData?.policyPayment?.schedule?.installments || [];
    if(installments.length) {
      for(let i of installments) {
          let receipts:any[] &#x3D; i.receipts || [];
          const index &#x3D; receipts.findIndex((r)&#x3D;&gt; r.id &#x3D;&#x3D;&#x3D; dto.receiptId);
          if(index &gt; -1) {
             receipts[index].isShowedReceipt &#x3D; dto.isShowedReceipt;
            i.receipts &#x3D; receipts;
          }
      }
      oldData.policyPayment.schedule.installments  &#x3D; installments;
    }

    this.commandId &#x3D; uuid.v4();
    return this.executeCommand(Action.UPDATE, null,  this.commandId, oldData);
  }

  async importFiles(user, dto, files, actionName: string) {
    this.loggerService.log(this.context, clc.green(&#x27;create service&#x27;));
    let contracts &#x3D; await CommonUtils.convertToJson(files);
    let unitsReject: any[] &#x3D; [];
    let unitsImport: any[] &#x3D; [];

    const history &#x3D; [];
    for(const contract of contracts) {

      let unit &#x3D; contract;
      let amount, price &#x3D; 0, landPrice &#x3D; 0, housePrice &#x3D; 0;
      let receipt &#x3D; {};
      let projectSetting &#x3D; {};
      const model: any &#x3D; Object.assign({},
        contract
      , {
        isDebtRemind: contract[&#x27;isDebtRemind&#x27;] &#x3D;&#x3D;&#x3D; &#x27;x&#x27;,
        calcCurrencyFirst: contract[&#x27;calcCurrencyFirst&#x27;] &#x3D;&#x3D;&#x3D; &#x27;x&#x27;,
        type: ContractEnum.DEPOSIT
      });

      if(contract[&#x27;startDate&#x27;]) {
        if (!moment(contract.startDate, &#x27;DD/MM/YYYY&#x27;,true).isValid()) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Sai định dạng &quot;DD/MM/YYYY&quot;: Ngày bắt đầu&#x60;
          });
        } else {
          model.startDate &#x3D; moment(contract[&#x27;startDate&#x27;], &#x27;DD/MM/YYYY&#x27;).toDate();
        }
      }
      if(contract[&#x27;expiredDate&#x27;]) {
        if (!moment(contract.expiredDate, &#x27;DD/MM/YYYY&#x27;,true).isValid()) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Sai định dạng &quot;DD/MM/YYYY&quot;: Ngày kết thúc&#x60;
          });
        } else {
          model.expiredDate &#x3D; moment(contract[&#x27;expiredDate&#x27;], &#x27;DD/MM/YYYY&#x27;).toDate();
        }
      }
      if(contract[&#x27;maintenanceFee&#x27;] &amp;&amp; contract[&#x27;maintenanceFee&#x27;][&#x27;type&#x27;]) {
        model.maintenanceFee.type &#x3D; contract[&#x27;maintenanceFee&#x27;][&#x27;type&#x27;] &#x3D;&#x3D;&#x3D; &#x27;VND&#x27; ? &#x27;currency&#x27; : &#x27;percent&#x27;;
      }
      if(contract[&#x27;maintenanceFee&#x27;] &amp;&amp; contract[&#x27;maintenanceFee&#x27;][&#x27;value&#x27;]) {
        model.maintenanceFee.value &#x3D; parseInt(contract[&#x27;maintenanceFee&#x27;][&#x27;value&#x27;]);
      }

      this.commandId &#x3D; uuid.v4();

      model.modifiedBy &#x3D; user.id;
      model.createdBy &#x3D; user.id;
      model.createdDate &#x3D; new Date();

      model.calcPriceVat &#x3D; false;
      model.calcContractPrice &#x3D; false;

      if (contract[&#x27;calcPrice&#x27;] &#x3D;&#x3D;&#x3D; &#x27;Giá có VAT&#x27;) {
        model.calcPriceVat &#x3D; true
      } else if (contract[&#x27;calcPrice&#x27;] &#x3D;&#x3D;&#x3D; &#x27;Giá không có VAT&#x27;) {

      } else if (contract[&#x27;calcPrice&#x27;] &#x3D;&#x3D;&#x3D; &#x27;Tổng giá trị CH sau CK gồm VAT, chưa gồm PBT&#x27;) {
        model.calcContractPrice &#x3D; true;
      }

      if (!contract.signedDate) {
        unit &#x3D; null;
        history.push({
          line: &#x60;${contract.stt}&#x60;,
          error: &#x60;Thiếu trường bắt buộc: Ngày ký kết&#x60;
        });
      } else {
        if (!moment(contract.signedDate, &#x27;DD/MM/YYYY&#x27;,true).isValid()) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Sai định dạng &quot;DD/MM/YYYY&quot;: Ngày ký kết&#x60;
          });
        } else {
          model.signedDate &#x3D; moment(contract[&#x27;signedDate&#x27;], &#x27;DD/MM/YYYY&#x27;).toISOString();
        }
      }

      if (contract.transferType) {
        model.transferType &#x3D; this.getTransferType(contract.transferType);
        if (!model.transferType) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Sai kiểu dữ liệu: Hình thức thanh toán&#x60;
          });
        }
      }

      let primaryTransaction;
      if (!contract.primaryTransactionCode) {
        unit &#x3D; null;
        history.push({
          line: &#x60;${contract.stt}&#x60;,
          error: &#x60;Thiếu trường bắt buộc: Mã phiếu YCDCO&#x60;
        });
      } else {
        primaryTransaction &#x3D; await this.propertyClient.sendDataPromise({
          code: contract.primaryTransactionCode
        }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_CODE);
        if (!primaryTransaction) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Không tìm thấy phiếu YCDCO&#x60;
          });
        } else if (primaryTransaction.status !&#x3D;&#x3D; &#x27;SUCCESS&#x27;) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Phiếu YCDCO trạng thái không hợp lệ&#x60;
          });
        } else if (primaryTransaction.contract) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Phiếu YCDCO đã có hợp đồng&#x60;
          });
        }
        if (!unit) {
          unitsReject.push(contract);
          continue;
        }
        if (contract.hasCustomer2 &amp;&amp; contract.customer2.name) {
          unit &#x3D; await this.checkErrorCustomer(history, contract, contract.customer2, unit);
          let address &#x3D; {
            country: contract.customer2[&#x27;country&#x27;],
            province: contract.customer2[&#x27;province&#x27;],
            district: contract.customer2[&#x27;district&#x27;],
            ward: contract.customer2[&#x27;ward&#x27;],
            address: contract.customer2[&#x27;address&#x27;],
            fullAddress: &#x27;&#x27;
          };
          let rootAddress &#x3D; {
            country: contract.customer2[&#x27;rootCountry&#x27;],
            province: contract.customer2[&#x27;rootProvince&#x27;],
            district: contract.customer2[&#x27;rootDistrict&#x27;],
            ward: contract.customer2[&#x27;rootWard&#x27;],
            address: contract.customer2[&#x27;rootAddress&#x27;],
            fullAddress: &#x27;&#x27;
          };

          let customer2: any &#x3D; {
            name: contract.customer2[&#x27;name&#x27;],
            gender: contract.customer2[&#x27;gender&#x27;] &#x3D;&#x3D;&#x3D; &#x27;Nam&#x27; ? &#x27;male&#x27; : &#x27;female&#x27;,
            birthday: contract.customer2[&#x27;onlyYear&#x27;] &#x3D;&#x3D;&#x3D; &#x27;x&#x27; ? contract.customer2[&#x27;dob&#x27;] : moment(contract.customer2[&#x27;dob&#x27;], &#x27;DD/MM/YYYY&#x27;),
            birthdayYear: contract.customer2[&#x27;dob&#x27;],
            onlyYear: contract.customer2[&#x27;onlyYear&#x27;] &#x3D;&#x3D;&#x3D; &#x27;x&#x27;,
            phone: contract.customer2[&#x27;phone&#x27;],
            email: contract.customer2[&#x27;email&#x27;],
            identityNumber: contract.customer2[&#x27;identityNumber&#x27;],
            identityIssueDate: contract.customer2[&#x27;identityDate&#x27;],
            identityIssueLocation: contract.customer2[&#x27;identityLocation&#x27;],
            taxCode: contract.customer2[&#x27;taxCode&#x27;] ? contract.customer2[&#x27;taxCode&#x27;] : &#x27;&#x27; ,
            bankInfo: {
              code: contract[&#x27;bankCode&#x27;],
              value: contract[&#x27;bankValue&#x27;],
            },
            address: address,
            rootAddress: rootAddress,
            code: &#x27;&#x27;,
            company: &#x27;&#x27;,
            position: &#x27;&#x27;,
            type: &#x27;&#x27;
          };
          primaryTransaction.customer2 &#x3D; CommonUtils.getCustomerMapping(customer2);
        }
        model.primaryTransaction &#x3D; primaryTransaction;

        if (model.calcContractPrice) {
          price &#x3D; primaryTransaction.propertyUnit.contractPrice;
          housePrice &#x3D; 0;
          landPrice &#x3D; 0;
        } else {
          price &#x3D; model.calcPriceVat ? primaryTransaction.propertyUnit.priceVat : primaryTransaction.propertyUnit.price;
          housePrice &#x3D; model.calcPriceVat ? primaryTransaction.propertyUnit.housePriceVat : primaryTransaction.propertyUnit.housePrice;
          landPrice &#x3D; model.calcPriceVat ? primaryTransaction.propertyUnit.landPriceVat : primaryTransaction.propertyUnit.landPrice;
        }
        model.maintenanceFee &#x3D; {
          ...model.maintenanceFee,
          contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
        }

        receipt &#x3D; primaryTransaction.reciept;
        amount &#x3D; primaryTransaction.amount;
        projectSetting &#x3D; primaryTransaction.project.setting
      }

      if (contract.policyDiscountCodes) {
        const codes &#x3D; contract.policyDiscountCodes.split(&#x27;|&#x27;);
        const policyDiscounts : any[] &#x3D; await this.policyQueryService.findByCodes(codes);
        if (!policyDiscounts || !policyDiscounts.length) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Không tìm thấy chính sách chiết khấu&#x60;
          });
        } else {
          model.policyDiscounts &#x3D; policyDiscounts;

          // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà &amp; đất
          if (price &gt; 0 &amp;&amp; !housePrice &amp;&amp; !landPrice) {
            if (
              policyDiscounts.some(
                e &#x3D;&gt; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.HOUSE ||
                  e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.LAND)
            ) {
              unit &#x3D; null;
              history.push({
                line: &#x60;${contract.stt}&#x60;,
                error: &#x60;Chính sách chiết khẩu không phù hợp&#x60;
              });
            }
          } else {
            if (
              policyDiscounts.some(e &#x3D;&gt; !e.discount.typeRealEstate || e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.DEFAULT)
            ) {
              unit &#x3D; null;
              history.push({
                line: &#x60;${contract.stt}&#x60;,
                error: &#x60;Chính sách chiết khẩu không phù hợp&#x60;
              });
            }
          }
        }
      } else {
        model.policyDiscounts &#x3D; [];
      }

      if (contract.policyPaymentCode) {
        const policyPayment &#x3D; await this.policyQueryService.findOne({
          code: contract.policyPaymentCode, type: PolicyTypeEnum.PAYMENT
        });
        if (!policyPayment) {
          unit &#x3D; null;
          history.push({
            line: &#x60;${contract.stt}&#x60;,
            error: &#x60;Không tìm thấy chính sách thanh toán&#x60;
          });
        } else {
          model.policyPayment &#x3D; this.transformPolicyPayment(
            model,
            price,
            projectSetting,
            model.signedDate,
            receipt,
            policyPayment,
            housePrice,
            landPrice,
            model.maintenanceFee);
        }
      } else {
        unit &#x3D; null;
        history.push({
          line: &#x60;${contract.stt}&#x60;,
          error: &#x60;Thiếu trường bắt buộc: Mã chính sách thanh toán&#x60;
        });
      }

      if(!unit) {
        unitsReject.push(contract);
        continue;
      }

      if (!model.code) {
        const prefix &#x3D; &#x60;${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-&#x60;;
        model.code &#x3D; await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
      }
      model.name &#x3D; &#x60;${model.code}-${model.primaryTransaction.customer.personalInfo.name}&#x60;

      if(model.policyPayment &amp;&amp; !model.paymentPercent) {

        let totalPercentage &#x3D; 0;
        let currentTotalTransfered &#x3D; 0;

        model.policyPayment.schedule.installments.forEach((inst, index)&#x3D;&gt; {
          currentTotalTransfered &#x3D; currentTotalTransfered + inst.totalTransfered;
          if(index &gt; 0 ) { // bắt đầu tính từ đợt 1;
            if(inst.totalTransfered &lt; inst.totalAmount || inst.type &#x3D;&#x3D;&#x3D; &#x27;currency&#x27;) {
              totalPercentage &#x3D; totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
            }else {
              totalPercentage &#x3D; totalPercentage + inst.value;
            }
          }
        });

        if(model.policyPayment.schedule.installments.length &#x3D;&#x3D;&#x3D; 1) {
          totalPercentage &#x3D; totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
        }

        model.paymentPercent &#x3D; totalPercentage;
      }

      if (contract.companyInformation &amp;&amp; contract.companyInformation.haveValue) {
        model.companyInformation &#x3D; Object.assign({}, contract.companyInformation, {
          haveValue: true,
          dateOfIssue: contract.companyInformation.dateOfIssue ? moment(contract.companyInformation.dateOfIssue, &#x27;DD/MM/YYYY&#x27;).toDate() : &#x27;&#x27;
        })
      }

      if (contract.receipt &amp;&amp; contract.receipt.length) {
        contract.receipt &#x3D; contract.receipt.filter(r &#x3D;&gt; !!r.amount);
        const receipts &#x3D; [];
        contract.receipt.forEach(function (receipt, idx) {
          const installmentName &#x3D; model.policyPayment.schedule.installments[idx].name;
          const amounts &#x3D; receipt.amount.split(&#x27;|&#x27;) || [];
          const dates &#x3D; receipt.date.split(&#x27;|&#x27;) || [];
          const receiptNums &#x3D; receipt.receiptNum.split(&#x27;|&#x27;) || [];
          if (amounts.length) {
            amounts.forEach(function (amount, aIdx) {
              receipts.push({
                amount: amount,
                date: dates[aIdx],
                receiptNum: receiptNums[aIdx],
                installmentName
              })
            });
          }
        });
        model.receipts &#x3D; receipts;
        model.user &#x3D; user;
      }

      unitsImport.push(contract);

      model.status &#x3D; StatusContractEnum.ACCOUNTANT_WAITING;

      //  create resident
      let projectInfo:any &#x3D;{};
      projectInfo &#x3D; await this.propertyClient.sendDataPromise(model.primaryTransaction.project, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);
      let newObject&#x3D;{};
      if(projectInfo){
        newObject &#x3D; {... model.primaryTransaction,project:projectInfo,
          customer:model.primaryTransaction.customer,
          propertyUnit:model.primaryTransaction.propertyUnit,
          contractId: this.commandId
        };
      }

      // send transaction to care service
      await this.careClient.sendDataPromise({
        primaryTransaction: newObject
      }, CmdPatternConst.CARE.GET_TRANSACTION);

      await this.socialClient.sendDataPromise({
        projectId: model.primaryTransaction.project.id
      }, CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP);

      const {personalInfo, info} &#x3D; (model[&#x27;primaryTransaction&#x27;].customer || {}) || {};
      const { email, phone, name, identities } &#x3D; personalInfo;

      // if(email &amp;&amp; phone ) {
      //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)&#x3D;&gt; {

      //     if(!customer) {
      //       const payload &#x3D; { personalInfo: { name, email, phone, identities }, accessSystem: user.notiSystem ? [user.notiSystem] : null  };
      //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

      //     }else if(!customer.isActive) {

      //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
      //     }

      //   })
      // }

      model.id &#x3D; this.commandId;
      await this.queryRepository.create(model);
      await this.updateContractInTracsaction(model);

      if (model.receipts &amp;&amp; model.receipts.length) {
        const transactionData &#x3D; {
          action: &#x27;property-ticketCreated&#x27;,
          receipts: model.receipts,
          contractId: model.id,
          user: model.user
        }
        await this.transactionClient.sendDataPromise(transactionData, CmdPatternConst.LISTENER.PRIMARY_CONTRACT_CREATE_TRANSFERRED_TICKET_LISTENER);
      }

      console.log(&#x27;model&#x27;, model)
    }

    // Tạo history.
    const file &#x3D; await this.uploadClient.sendData(files[0].originalname, files[0].buffer);
    const historyModel &#x3D; {
      fileName: files[0].originalname,
      createdBy: user.id,
      fail: unitsReject.length,
      success: unitsImport.length,
      description: history,
      type: &quot;IMPORT_CONTRACT_DEPOSIT&quot;,
      eventName: &#x27;importHDC&#x27;,
      file
    };
    await this.historyRepository.create(historyModel);
  }

  updateContractInTracsaction(event) {
    try {
      let data;
      data &#x3D; {
        primaryTransactionId: event.primaryTransaction.id,
        id: event.id,
        code: event.code,
        type: event.type,
        isTransferred: event.isTransferred
      };

      if(event.oldPrimaryTransaction) data.oldPrimaryTransactionId &#x3D; event.oldPrimaryTransaction;

      // khi HĐC đã sang HĐMB thì không update sang primary transaction nữa
      if(!event.purchase) {
        this.propertyClient.sendData(data, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CONTRACT_IN_TRANSACTION_BY_ID);
      }
    } catch (error) {
      console.log(&#x27;[Error] Update primary transaction failed, detail: &#x27;, error);
    }
  }

  getTransferType(type) {
    switch (type) {
      case &#x27;Tiền mặt&#x27;:
        return &#x27;CASH&#x27;;
      case &#x27;Chuyển khoản&#x27;:
        return &#x27;TRANSFER&#x27;;
      case &#x27;Khác&#x27;:
        return &#x27;OTHER&#x27;;
      default:
        return &#x27;&#x27;;
    }
  }

  checkErrorCustomer(history, ticket, customer, unit) {
    let customer2Name &#x3D; &#x27;(đồng sở hữu)&#x27;;

    // Check đồng sở hữu
    if (ticket.hasCustomer2 &#x3D;&#x3D;&#x3D; &#x27;x&#x27; &amp;&amp; !ticket.customer2) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Thiếu thông tin đồng sở hữu&#x60;
      });
      return unit;
    }

    // Check error
    if (!customer.name) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Thiếu trường bắt buộc: Tên khách hàng&#x60; + customer2Name
      });
    }
    // if (!customer.dob) {
    //   unit &#x3D; null;
    //   history.push({
    //     line: &#x60;${ticket.stt}&#x60;,
    //     error: &#x60;Thiếu trường bắt buộc: Ngày sinh&#x60; + customer2Name
    //   });
    // }
    if (!customer.gender) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Thiếu trường bắt buộc: Giới tính&#x60; + customer2Name
      });
    }
    // if (!customer.phone) {
    //   unit &#x3D; null;
    //   history.push({
    //     line: &#x60;${ticket.stt}&#x60;,
    //     error: &#x60;Thiếu trường bắt buộc: Số điện thoại&#x60; + customer2Name
    //   });
    // }
    if (customer.phone &amp;&amp; !customer.phone.toString().match(CommonConst.REGEX_VN_PHONE)) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Số điện thoại không hợp lệ&#x60; + customer2Name
      });
    }
    // if (!customer.email) {
    //   unit &#x3D; null;
    //   history.push({
    //     line: &#x60;${ticket.stt}&#x60;,
    //     error: &#x60;Thiếu trường bắt buộc: Địa chỉ Email&#x60; + customer2Name
    //   });
    // }
    if (customer.email &amp;&amp; !customer.email.toString().match(CommonConst.REGEX_EMAIL)) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Email: Sai định dạng email&#x60; + customer2Name
      });
    }
    if (!customer.identityNumber) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Thiếu trường bắt buộc: Số CMND/Hộ chiếu&#x60; + customer2Name
      });
    }
    if (!customer.identityDate) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Thiếu trường bắt buộc: Ngày cấp&#x60; + customer2Name
      });
    }
    if (!customer.identityLocation) {
      unit &#x3D; null;
      history.push({
        line: &#x60;${ticket.stt}&#x60;,
        error: &#x60;Thiếu trường bắt buộc: Nơi cấp&#x60; + customer2Name
      });
    }
    return unit;
  }

  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any
  ) {
    let commandObject &#x3D; null;
    switch (action) {
      case Action.CREATE:
        commandObject &#x3D; new CreatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.UPDATE:
        commandObject &#x3D; new UpdatePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      case Action.DELETE:
        commandObject &#x3D; new DeletePrimaryContractCommand(
          actionName,
          commandId,
          item
        );
        break;
      default:
        break;
    }

    return await this.commandBus
      .execute(commandObject)
      .then((response) &#x3D;&gt; {
        return response;
      })
      .catch((error) &#x3D;&gt; {
        return error;
      });
  }

  checkErrorCustomer2SyncErp(history, customer2, unit, isCustomer2 &#x3D; false) {
    let customer2Name &#x3D; isCustomer2 ? &#x27;(đồng sở hữu)&#x27; : &#x27;&#x27;;

    // Check error
    if (!customer2.name) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Tên khách hàng&#x60; + customer2Name
      });
    }
    if (!customer2.birthday &amp;&amp; !isCustomer2) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Ngày sinh&#x60; + customer2Name
      });
    }
    if (!customer2.gender &amp;&amp; !isCustomer2) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Giới tính&#x60; + customer2Name
      });
    }
    if (!customer2.phone &amp;&amp; !isCustomer2) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Số điện thoại&#x60; + customer2Name
      });
    }
    if (customer2.phone &amp;&amp; !customer2.phone.toString().match(CommonConst.REGEX_VN_PHONE)) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Số điện thoại không hợp lệ&#x60; + customer2Name
      });
    }
    // if (!customer2.email) {
    //   unit &#x3D; null;
    //   history.push({
    //     error: &#x60;Thiếu trường bắt buộc: Địa chỉ Email&#x60; + customer2Name
    //   });
    // }
    if (customer2.email &amp;&amp; !customer2.email.toString().match(CommonConst.REGEX_EMAIL)) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Email: Sai định dạng email&#x60; + customer2Name
      });
    }
    if (!customer2.identityNumber) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Số CMND/Hộ chiếu&#x60; + customer2Name
      });
    }
    if (!customer2.identityIssueDate) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Ngày cấp&#x60; + customer2Name
      });
    }
    if (!customer2.identityIssueLocation) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Nơi cấp&#x60; + customer2Name
      });
    }
    return unit;
  }

  async createdContractSyncErp(contract) {
    this.loggerService.log(this.context, clc.green(&#x27;created contract sync ERP&#x27;));
    let historyModel: any &#x3D; {};
    const history &#x3D; [];
    let unit &#x3D; contract;
    let amount, price &#x3D; 0, landPrice &#x3D; 0, housePrice &#x3D; 0;
    let receipt &#x3D; [];
    let projectSetting &#x3D; {};
    let updatePrimaryTransactionQuery;

    let oldContract;
    // check trùng
    if (contract.contractid) {
      oldContract &#x3D; await this.queryRepository.findOne({&#x27;syncErpData.contractid&#x27;: contract.contractid});
      if (oldContract) {
        for (const installment of oldContract.policyPayment.schedule.installments) {
          if (installment.receipts &amp;&amp; installment.receipts.length) {
            receipt &#x3D; [...receipt, ...installment.receipts];
          }
        }
      }
    }

    let paymentConfig, discountConfig;
    if (!contract.project) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: mã dự án ERP&#x60;
      });

      // Return báo lỗi
      historyModel &#x3D; {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }

    let configErp &#x3D; await this.syncErpClient.sendDataPromise(contract.project, CmdPatternConst.LISTENER.SEARCH_CONFIG_ERP_BY_CAMPAIGN);
    if (!configErp) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Không tìm thấy cấu hình ERP&#x60;
        });
    } else {
      paymentConfig &#x3D; configErp.paymentMethod &amp;&amp; configErp.paymentMethod.length &gt; 0 ? configErp.paymentMethod.find(e &#x3D;&gt; e.paymentMethodCRM &#x3D;&#x3D;&#x3D; contract.pymtterm) : null;
      if (!paymentConfig) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Không tìm thấy chính sách thanh toán&#x60;
        });
      }
      discountConfig &#x3D; configErp.discountTable &amp;&amp; configErp.discountTable.length &gt; 0 ? configErp.discountTable.find(e &#x3D;&gt; e.discountTableCRM &#x3D;&#x3D;&#x3D; contract.policyDiscountCodes) : null;
      if (contract.policyDiscountCodes &amp;&amp; !discountConfig) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Không tìm thấy chính sách chiết khấu&#x60;
        });
      }
    }

    // Return báo lỗi
    if (!unit) {
      historyModel &#x3D; {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }

    const model: any &#x3D; Object.assign({},
      contract
    , {
      isDebtRemind: false,
      calcCurrencyFirst: false,
      type: ContractEnum.DEPOSIT
    });

    if (!contract.startdate) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Ngày ký kết&#x60;
      });
    } else {
      model.startDate &#x3D; new Date(momentTz(contract.startdate,(&#x27;DD/MM/YYYY HH:mm:ss&#x27;)).tz(&#x27;Asia/Ho_Chi_Minh&#x27;));
      model.signedDate &#x3D; new Date(momentTz(contract.startdate,(&#x27;DD/MM/YYYY HH:mm:ss&#x27;)).tz(&#x27;Asia/Ho_Chi_Minh&#x27;));
    }
    if(contract.enddate) {
      model.expiredDate &#x3D; new Date(momentTz(contract.enddate,(&#x27;DD/MM/YYYY HH:mm:ss&#x27;)).tz(&#x27;Asia/Ho_Chi_Minh&#x27;));
    }

    this.commandId &#x3D; uuid.v4();

    // model.modifiedBy &#x3D; user.id;
    // model.createdBy &#x3D; user.id;
    if(!oldContract) {
      model.createdDate &#x3D; new Date();
    }

    model.calcPriceVat &#x3D; false;
    model.calcContractPrice &#x3D; true;
    model.transferType &#x3D; null;

    let primaryTransaction;
    // Tìm YCDCO theo systemno
    if (!contract.anal_pct5) {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Mã phiếu YCDCO&#x60;
      });
    } else {
      primaryTransaction &#x3D; await this.propertyClient.sendDataPromise({
        &#x27;syncErpData.systemno&#x27;: contract.anal_pct5
      }, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.GET_PROPERTY_PRIMARY_TRANSACTION_BY_QUERY);
      if (!primaryTransaction) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Không tìm thấy phiếu YCDCO&#x60;
        });
      } else if (primaryTransaction.status !&#x3D;&#x3D; &#x27;SUCCESS&#x27;) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Phiếu YCDCO trạng thái không hợp lệ&#x60;
        });
      } else if (primaryTransaction.contract &amp;&amp; (!oldContract || primaryTransaction.contract.id !&#x3D;&#x3D; oldContract.id)) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Phiếu YCDCO đã có hợp đồng&#x60;
        });
      }

      // Return báo lỗi
      if (!unit) {
        historyModel &#x3D; {
          isSuccess: false,
          description: history,
        };
        return historyModel;
      }

      // update primary transaction
      updatePrimaryTransactionQuery &#x3D; {
        id: primaryTransaction.id
      };

      // Khách hàng
      let customerAddress &#x3D; contract.address || &#x27;&#x27;;
      let customerRootAddress &#x3D; contract.contactaddress || &#x27;&#x27;;
      let address &#x3D; {
        country: &#x27;Việt Nam&#x27;,
        province: customerAddress.split(&quot;,&quot;)[3],
        district: customerAddress.split(&quot;,&quot;)[2],
        ward: customerAddress.split(&quot;,&quot;)[1],
        address: customerAddress.split(&quot;,&quot;)[0],
        fullAddress: customerAddress
      };
      let rootAddress &#x3D; {
        country: &#x27;Việt Nam&#x27;,
        province: customerRootAddress.split(&quot;,&quot;)[3],
        district: customerRootAddress.split(&quot;,&quot;)[2],
        ward: customerRootAddress.split(&quot;,&quot;)[1],
        address: customerRootAddress.split(&quot;,&quot;)[0],
        fullAddress: customerRootAddress
      };
      let birthArr &#x3D; contract.birthdate.split(&quot;/&quot;);
      birthArr &#x3D; birthArr.filter(e &#x3D;&gt; e !&#x3D; &#x27;0&#x27;);
      let customer: any &#x3D; {
        name: contract.clientname,
        gender: (contract.gender) &#x3D;&#x3D;&#x3D; &#x27;M&#x27; ? &#x27;male&#x27; : &#x27;female&#x27;,
        birthday: moment(contract.birthdate, &#x27;DD/MM/YYYY&#x27;),
        birthdayYear: birthArr[birthArr.length - 1],
        onlyYear: birthArr.length &lt; 3,
        phone: contract.telephone,
        email: contract.email,
        identityNumber: contract.idcard,
        identityIssueDate: moment(contract.issueddate, &#x27;DD/MM/YYYY&#x27;),
        identityIssueLocation: contract.issuedplace,
        address: address,
        rootAddress: rootAddress,
        company: &#x27;&#x27;,
        position: &#x27;&#x27;,
        type: &#x27;&#x27;,
        taxCode: contract.taxcode
      };
      unit &#x3D; await this.checkErrorCustomer2SyncErp(history, customer, unit);
      primaryTransaction.customer &#x3D; CommonUtils.getCustomerMapping(customer);
      updatePrimaryTransactionQuery.customer &#x3D; primaryTransaction.customer;

      const members &#x3D; (contract.membername || &#x27;&#x27;).split(&#x27; | &#x27;);
      const hasCustomer2 &#x3D; members.length &gt; 1;

      // Đồng sở hữu
      if (hasCustomer2) {
        let customer2Address &#x3D; contract.maddress.split(&#x27; | &#x27;).pop() || &#x27;&#x27;;
        let customer2RootAddress &#x3D; contract.mcontactaddress.split(&#x27; | &#x27;).pop() || &#x27;&#x27;;
        let address &#x3D; {
          country: &#x27;Việt Nam&#x27;,
          province: customer2Address.split(&quot;,&quot;)[3],
          district: customer2Address.split(&quot;,&quot;)[2],
          ward: customer2Address.split(&quot;,&quot;)[1],
          address: customer2Address.split(&quot;,&quot;)[0],
          fullAddress: customer2Address
        };
        let rootAddress &#x3D; {
          country: &#x27;Việt Nam&#x27;,
          province: customer2RootAddress.split(&quot;,&quot;)[3],
          district: customer2RootAddress.split(&quot;,&quot;)[2],
          ward: customer2RootAddress.split(&quot;,&quot;)[1],
          address: customer2RootAddress.split(&quot;,&quot;)[0],
          fullAddress: customer2RootAddress
        };
        let birthArr &#x3D; [contract.mbirthday.split(&#x27; | &#x27;).pop(), contract.mbirthmonth.split(&#x27; | &#x27;).pop(), contract.mbirthyear.split(&#x27; | &#x27;).pop()]
        birthArr &#x3D; birthArr.filter(e &#x3D;&gt; e !&#x3D;&#x3D; &#x27;0&#x27;);
        const onlyYear &#x3D; birthArr.length &lt; 3;
        let customer2: any &#x3D; {
          name: contract.membername.split(&#x27; | &#x27;).pop(),
          gender: (contract.mgender.split(&#x27; | &#x27;).pop()) &#x3D;&#x3D;&#x3D; &#x27;M&#x27; ? &#x27;male&#x27; : &#x27;female&#x27;,
          birthday: onlyYear ? birthArr[birthArr.length - 1] : moment(&#x60;${contract.mbirthday.split(&#x27; | &#x27;).pop()}/${contract.mbirthmonth.split(&#x27; | &#x27;).pop()}/${contract.mbirthyear.split(&#x27; | &#x27;).pop()}&#x60;, &#x27;DD/MM/YYYY&#x27;),
          birthdayYear: birthArr[birthArr.length - 1],
          onlyYear: onlyYear,
          phone: contract.mtelephone.split(&#x27; | &#x27;).pop(),
          email: contract.memail.split(&#x27; | &#x27;).pop(),
          identityNumber: contract.midcard.split(&#x27; | &#x27;).pop(),
          identityIssueDate: moment(contract.missueddate.split(&#x27; | &#x27;).pop(), &#x27;DD/MM/YYYY&#x27;),
          identityIssueLocation: contract.missuedplace.split(&#x27; | &#x27;).pop(),
          address: address,
          rootAddress: rootAddress,
          company: &#x27;&#x27;,
          position: &#x27;&#x27;,
          type: &#x27;&#x27;
        };
        unit &#x3D; await this.checkErrorCustomer2SyncErp(history, customer2, unit, true);
        primaryTransaction.customer2 &#x3D; CommonUtils.getCustomerMapping(customer2);
        updatePrimaryTransactionQuery.customer2 &#x3D; primaryTransaction.customer2;
      }

      // Return báo lỗi
      if (!unit) {
        historyModel &#x3D; {
          isSuccess: false,
          description: history,
        };
        return historyModel;
      }

      model.primaryTransaction &#x3D; primaryTransaction;

      if (model.calcContractPrice) {
        price &#x3D; parseInt(contract.contractvalue);
        housePrice &#x3D; 0;
        landPrice &#x3D; 0;
      }
      model.maintenanceFee &#x3D; {
        ...model.maintenanceFee,
        contractPriceForMaintenanceFee: primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0
      }

      receipt &#x3D; [...receipt, ...primaryTransaction.reciept];
      projectSetting &#x3D; primaryTransaction.project.setting
    }

    // Chính sách chiết khấu
    if (discountConfig &amp;&amp; discountConfig.discountTableDXG) {
      const policyDiscounts : any[] &#x3D; await this.policyQueryService.findByCodes([discountConfig.discountTableDXG]);
      if (!policyDiscounts || !policyDiscounts.length) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Không tìm thấy chính sách chiết khấu&#x60;
        });
      } else {
        model.policyDiscounts &#x3D; policyDiscounts;

        // Chỉ được áp dụng chiết khấu mặc định nếu không có giá nhà &amp; đất
        if (price &gt; 0 &amp;&amp; !housePrice &amp;&amp; !landPrice) {
          if (
            policyDiscounts.some(
              e &#x3D;&gt; e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.HOUSE ||
                e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.LAND)
          ) {
            unit &#x3D; null;
            history.push({
              error: &#x60;Chính sách chiết khẩu không phù hợp&#x60;
            });
          }
        } else {
          if (
            policyDiscounts.some(e &#x3D;&gt; !e.discount.typeRealEstate || e.discount.typeRealEstate &#x3D;&#x3D;&#x3D; DiscountTypeRealEstateEnum.DEFAULT)
          ) {
            unit &#x3D; null;
            history.push({
              error: &#x60;Chính sách chiết khẩu không phù hợp&#x60;
            });
          }
        }
      }
    } else {
      model.policyDiscounts &#x3D; [];
    }

    // Chính sách thanh toán
    if (paymentConfig &amp;&amp; paymentConfig.paymentMethodDXG) {
      const policyPayment &#x3D; await this.policyQueryService.findOne({
        code: paymentConfig.paymentMethodDXG, type: PolicyTypeEnum.PAYMENT
      });
      if (!policyPayment) {
        unit &#x3D; null;
        history.push({
          error: &#x60;Không tìm thấy chính sách thanh toán&#x60;
        });
      } else {
        model.policyPayment &#x3D; this.transformPolicyPayment(
          model,
          price,
          projectSetting,
          model.signedDate,
          receipt,
          policyPayment,
          housePrice,
          landPrice,
          model.maintenanceFee);
      }
    } else {
      unit &#x3D; null;
      history.push({
        error: &#x60;Thiếu trường bắt buộc: Mã chính sách thanh toán&#x60;
      });
    }

    // Return báo lỗi
    if(!unit) {
      historyModel &#x3D; {
        isSuccess: false,
        description: history,
      };
      return historyModel;
    }
    if(!oldContract) {
      if (!model.code) {
        const prefix &#x3D; &#x60;${CommonConst.AGGREGATES.PRIMARY_CONTRACT.CODE_PREFIX_DEPOSIT_CONTRACT}${primaryTransaction.project.code}-&#x60;;
        model.code &#x3D; await this.codeGenerateService.generateCode(CommonConst.AGGREGATES.PRIMARY_CONTRACT.NAME, prefix);
      }
      model.name &#x3D; &#x60;${model.code}-${model.primaryTransaction.customer.personalInfo.name}&#x60;
    } else {
      model.name &#x3D; oldContract.name;
      model.code &#x3D; oldContract.code;
    }
    if(model.policyPayment &amp;&amp; !model.paymentPercent) {

      let totalPercentage &#x3D; 0;
      let currentTotalTransfered &#x3D; 0;

      model.policyPayment.schedule.installments.forEach((inst, index)&#x3D;&gt; {
        currentTotalTransfered &#x3D; currentTotalTransfered + inst.totalTransfered;
        if(index &gt; 0 ) { // bắt đầu tính từ đợt 1;
          if(inst.totalTransfered &lt; inst.totalAmount || inst.type &#x3D;&#x3D;&#x3D; &#x27;currency&#x27;) {
            totalPercentage &#x3D; totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
          }else {
            totalPercentage &#x3D; totalPercentage + inst.value;
          }
        }
      });

      if(model.policyPayment.schedule.installments.length &#x3D;&#x3D;&#x3D; 1) {
        totalPercentage &#x3D; totalPercentage + Math.round((Number(currentTotalTransfered) * 100) / price);
      }

      model.paymentPercent &#x3D; totalPercentage;
    }

    if (contract.companyInformation &amp;&amp; contract.companyInformation.haveValue) {
      model.companyInformation &#x3D; Object.assign({}, contract.companyInformation, {
        haveValue: true,
        dateOfIssue: contract.companyInformation.dateOfIssue ? moment(contract.companyInformation.dateOfIssue, &#x27;DD/MM/YYYY&#x27;).toDate() : &#x27;&#x27;
      })
    }

    if(!oldContract) {
      model.status &#x3D; StatusContractEnum.ACCOUNTANT_WAITING;
    } else {
      delete model.status;
    }

    //  create resident
    let projectInfo:any &#x3D;{};
    projectInfo &#x3D; await this.propertyClient.sendDataPromise(model.primaryTransaction.project, CmdPatternConst.LISTENER.GET_PROJECT_BY_ID);
    let newObject&#x3D;{};
    if(projectInfo){
      newObject &#x3D; {... model.primaryTransaction,project:projectInfo,
        customer:model.primaryTransaction.customer,
        propertyUnit:model.primaryTransaction.propertyUnit,
        contractId: this.commandId
      };
    }

    // send transaction to care service
    await this.careClient.sendDataPromise({
      primaryTransaction: newObject
    }, CmdPatternConst.CARE.GET_TRANSACTION);

    await this.socialClient.sendDataPromise({
      projectId: model.primaryTransaction.project.id
    }, CmdPatternConst.SOCIAL.CREATE_MARKET_PLACE_GROUP);

    const {personalInfo, info} &#x3D; (model[&#x27;primaryTransaction&#x27;].customer || {}) || {};
    const { email, phone, name, identities } &#x3D; personalInfo;

    // if(email &amp;&amp; phone ) {
    //   this.careClient.sendDataPromise({ email, phone }, CmdPatternConst.CARE.CHECK_EXISTENT_CUSTOMER).then((customer)&#x3D;&gt; {

    //     if(!customer) {
    //       const payload &#x3D; { personalInfo: { name, email, phone, identities }, accessSystem: &quot;DX_AGENT&quot;};
    //       this.careClient.sendDataPromise( payload, CmdPatternConst.CARE.CREATE_USER_CARE_AUTO);

    //     }else if(!customer.isActive) {

    //       this.careClient.sendDataPromise({ id: customer.id, isActive: true}, CmdPatternConst.CARE.ACTIVE_CUSTOMER);
    //     }

    //   })
    // }
    model.syncErpData &#x3D; contract;
    // update price
    model.primaryTransaction.propertyUnit.contractPrice &#x3D; price;
    updatePrimaryTransactionQuery.contractPrice &#x3D; price;
    updatePrimaryTransactionQuery.propetyUnitId &#x3D; primaryTransaction.propertyUnit.id;

    if (updatePrimaryTransactionQuery) {
      await this.propertyClient.sendDataPromise(updatePrimaryTransactionQuery, CmdPatternConst.PROPERTY_PRIMARY_TRANSACTION.UPDATE_CUSTOMER_FROM_CONTRACT);
    }
    if(!oldContract) {
      model.id &#x3D; this.commandId;
      await this.queryRepository.create(model);
      await this.updateContractInTracsaction(model);
    } else {
      model.id &#x3D; oldContract.id;
      await this.queryRepository.update(model);
    }

    // Return báo hoàn thành
    historyModel &#x3D; {
      isSuccess: true,
      description: history,
    };
    return historyModel;
  }

}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'PrimaryContractDomainService.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
