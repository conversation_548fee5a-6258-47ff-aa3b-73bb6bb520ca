<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>DeleteLiquidationCommandHandler</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts</code>
        </p>



            <p class="comment">
                <h3>Implements</h3>
            </p>
            <p class="comment">
                        <code>ICommandHandler</code>
            </p>


            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#aggregateName">aggregateName</a>
                            </li>
                            <li>
                                <a href="#client">client</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#eventName">eventName</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#result">result</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#execute">execute</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(repository: <a href="../injectables/LiquidationEventRepository.html">LiquidationEventRepository</a>, publisher: EventPublisher, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts:31</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>repository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LiquidationEventRepository.html" target="_self" >LiquidationEventRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>publisher</td>
                                                  
                                                        <td>
                                                                    <code>EventPublisher</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="aggregateName"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            aggregateName</b>
                            <a href="#aggregateName"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>CommonConst.LIQUIDATION_AGGREGATE_NAME</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts:20</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="client"></a>
                        <span class="name">
                            <b>
                            client</b>
                            <a href="#client"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>ClientProxy</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @Client({transport: undefined})<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts:31</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>DeleteLiquidationCommandHandler.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts:19</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="eventName"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            eventName</b>
                            <a href="#eventName"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>CommonConst.AGGREGATES.LIQUIDATION.DELETED</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts:22</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="result"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            result</b>
                            <a href="#result"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../interfaces/IResult.html" target="_self" >IResult</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    msg: &quot;Delete Command Handler Excecuted.&quot;,
    err: null,
    data: null,
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts:24</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="execute"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            execute
                        </b>
                        <a href="#execute"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>execute(command: <a href="../classes/DeleteLiquidationCommand.html">DeleteLiquidationCommand</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="39"
                            class="link-to-prism">src/modules/liquidation.domain/commands/handlers/delete-liquidation.cmd.handler.ts:39</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>command</td>
                                    <td>
                                                <code><a href="../classes/DeleteLiquidationCommand.html" target="_self" >DeleteLiquidationCommand</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { EventPublisher, ICommandHandler, CommandHandler } from &quot;@nestjs/cqrs&quot;;
import { Client, ClientProxy, Transport } from &quot;@nestjs/microservices&quot;;
import { IResult } from &quot;../../../shared/interfaces/result.interface&quot;;
import { InfoMessagePattern } from &quot;../../../shared/interfaces/messaging-pattern.interface&quot;;
import { isNullOrUndefined } from &quot;util&quot;;
import { BaseEventStream } from &quot;../../../shared/eventStream/models/base-event-stream.model&quot;;
import { CommonConst } from &quot;../../../shared/constant/index&quot;;
import { MsxLoggerService } from &quot;../../../logger/logger.service&quot;;
import { DeleteLiquidationCommand } from &quot;../impl/delete-liquidation.cmd&quot;;
import { LiquidationEventRepository } from &quot;../../repository/liquidation.event.repository&quot;;
import { LiquidationDomainAggregateModel } from &quot;../../models/liquidation.domain-aggregate.model&quot;;
import { catchError } from &#x27;rxjs/operators&#x27;;

const clc &#x3D; require(&quot;cli-color&quot;);

@CommandHandler(DeleteLiquidationCommand)
export class DeleteLiquidationCommandHandler
  implements ICommandHandler&lt;DeleteLiquidationCommand&gt; {
  private readonly context &#x3D; DeleteLiquidationCommandHandler.name;
  private readonly aggregateName: string &#x3D;
    CommonConst.LIQUIDATION_AGGREGATE_NAME;
  private readonly eventName: string &#x3D;
    CommonConst.AGGREGATES.LIQUIDATION.DELETED;
  private readonly result: IResult &#x3D; {
    msg: &quot;Delete Command Handler Excecuted.&quot;,
    err: null,
    data: null,
  };

  @Client({ transport: Transport.REDIS })
  client: ClientProxy;
  constructor(
    private readonly repository: LiquidationEventRepository,
    private readonly publisher: EventPublisher,
    private readonly loggerService: MsxLoggerService
  ) {}

  // execute Cteate Liquidation Command
  async execute(command: DeleteLiquidationCommand) {
    this.loggerService.log(
      this.context,
      clc.yellowBright(&#x60;Async Delete ${this.aggregateName} cmd ...&#x60;)
    );
    const { messagePattern, id, commandModel } &#x3D; command;
    const es &#x3D; await this.repository.findEventStreamById(commandModel.id);

    if (isNullOrUndefined(es)) {
      this.result.msg &#x3D; &quot;No data to delele&quot;;
      this.result.err &#x3D; new Error(&quot;Null or Undefined&quot;);

      const pattern &#x3D; { cmd: &quot;msx-adsg.notification.info&quot; };
      const messagingPattern &#x3D; new InfoMessagePattern(
        messagePattern,
        this.result
      );
      this.client.send&lt;any&gt;(pattern, messagingPattern)
        .pipe(catchError(async (err) &#x3D;&gt; {
          console.log(err)
        }))
        .subscribe();
    }

    /**
     * this code block only works for payload relationship with other entities
     */
    if (es &amp;&amp; es.payload) {
      const payload: any &#x3D; es.payload;
      // if existed object and have no new one
      // keep existing object
      if (payload) {
        commandModel.id &#x3D; es.streamId;
        // commandModel.name &#x3D; (commandModel.name || payload.name);
        commandModel.description &#x3D;
          commandModel.description || payload.description;
        commandModel.active &#x3D; commandModel.active || payload.active;
        commandModel.softDelete &#x3D; commandModel.softDelete || payload.softDelete;
        commandModel.modifiedBy &#x3D; commandModel.modifiedBy || payload.modifiedBy;
      }
    }

    const eventStream &#x3D; new BaseEventStream();
    eventStream.id &#x3D; id;
    eventStream.aggregateId &#x3D; id;

    if (isNullOrUndefined(commandModel.id)) commandModel.id &#x3D; id;
    eventStream.streamId &#x3D; commandModel.id;

    // if (isNullOrUndefined(es.streamId)) eventStream.streamId &#x3D; commandModel.id;
    // else eventStream.streamId &#x3D; es.streamId;

    eventStream.aggregate &#x3D; this.aggregateName;
    eventStream.eventName &#x3D; this.eventName;

    commandModel.eventName &#x3D; this.eventName;
    commandModel.actionName &#x3D; messagePattern;

    // aggregate model
    const aggregateModel &#x3D; new LiquidationDomainAggregateModel(eventStream);
    const cmdPublisher &#x3D; this.publisher.mergeObjectContext(aggregateModel);
    // const msg &#x3D; &#x60;${this.aggregateName}.${messagePattern}&#x60;;
    const msg &#x3D; &#x60;${this.aggregateName}&#x60;;
    cmdPublisher.addItem(msg, commandModel);
    cmdPublisher.commit();

    this.result.msg &#x3D; &quot;Delete Command Handler Excecuted.&quot;;
  }
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'DeleteLiquidationCommandHandler.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
