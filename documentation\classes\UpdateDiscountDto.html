<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>UpdateDiscountDto</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/discount.domain/dto/discount.dto.ts</code>
        </p>


            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code><a href="../classes/DiscountDto.html" target="_self" >DiscountDto</a></code>
            </p>



            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#id">id</a>
                            </li>
                            <li>
                                <a href="#name">name</a>
                            </li>
                            <li>
                                <a href="#type">type</a>
                            </li>
                            <li>
                                <a href="#typeRealEstate">typeRealEstate</a>
                            </li>
                            <li>
                                <a href="#value">value</a>
                            </li>
                            <li>
                                <a href="#description">description</a>
                            </li>
                            <li>
                                <a href="#id">id</a>
                            </li>
                            <li>
                                <a href="#active">active</a>
                            </li>
                            <li>
                                <a href="#modifiedBy">modifiedBy</a>
                            </li>
                            <li>
                                <a href="#softDelete">softDelete</a>
                            </li>
                            <li>
                                <a href="#active">active</a>
                            </li>
                            <li>
                                <a href="#modifiedBy">modifiedBy</a>
                            </li>
                            <li>
                                <a href="#softDelete">softDelete</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="id"></a>
                        <span class="name">
                            <b>
                            id</b>
                            <a href="#id"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <b>Decorators : </b>
                            <br />
                            <code>
                                @IsString()<br />@IsNotEmpty()<br />
                            </code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="30" class="link-to-prism">src/modules/discount.domain/dto/discount.dto.ts:30</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="name"></a>
                        <span class="name">
                            <b>
                            name</b>
                            <a href="#name"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/modules/discount.domain/dto/discount.dto.ts:32</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="type"></a>
                        <span class="name">
                            <b>
                            type</b>
                            <a href="#type"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/modules/discount.domain/dto/discount.dto.ts:33</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="typeRealEstate"></a>
                        <span class="name">
                            <b>
                            typeRealEstate</b>
                            <a href="#typeRealEstate"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/modules/discount.domain/dto/discount.dto.ts:34</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="value"></a>
                        <span class="name">
                            <b>
                            value</b>
                            <a href="#value"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/modules/discount.domain/dto/discount.dto.ts:35</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="description"></a>
                        <span class="name">
                            <b>
                            description</b>
                            <a href="#description"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/DiscountDto.html" target="_self" >DiscountDto</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/DiscountDto.html#source" target="_self" >DiscountDto:8</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="id"></a>
                        <span class="name">
                            <b>
                            id</b>
                            <a href="#id"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/DiscountDto.html" target="_self" >DiscountDto</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/DiscountDto.html#source" target="_self" >DiscountDto:7</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="active"></a>
                        <span class="name">
                            <b>
                            active</b>
                            <a href="#active"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/DiscountDto.html" target="_self" >DiscountDto</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/DiscountDto.html#source" target="_self" >DiscountDto:4</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="modifiedBy"></a>
                        <span class="name">
                            <b>
                            modifiedBy</b>
                            <a href="#modifiedBy"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/DiscountDto.html" target="_self" >DiscountDto</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/DiscountDto.html#source" target="_self" >DiscountDto:6</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="softDelete"></a>
                        <span class="name">
                            <b>
                            softDelete</b>
                            <a href="#softDelete"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/DiscountDto.html" target="_self" >DiscountDto</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/DiscountDto.html#source" target="_self" >DiscountDto:5</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="active"></a>
                        <span class="name">
                            <b>
                            active</b>
                            <a href="#active"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/ClassBased.html" target="_self" >ClassBased</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/ClassBased.html#source" target="_self" >ClassBased:4</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="modifiedBy"></a>
                        <span class="name">
                            <b>
                            modifiedBy</b>
                            <a href="#modifiedBy"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/ClassBased.html" target="_self" >ClassBased</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/ClassBased.html#source" target="_self" >ClassBased:6</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="softDelete"></a>
                        <span class="name">
                            <b>
                            softDelete</b>
                            <a href="#softDelete"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../classes/ClassBased.html" target="_self" >ClassBased</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../classes/ClassBased.html#source" target="_self" >ClassBased:5</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { IsNotEmpty, IsNumber, IsString, IsEnum } from &quot;class-validator&quot;;
import { ClassBased } from &quot;../../shared/classes/class-based&quot;;
import { IDiscount } from &quot;../../shared/services/discount/interfaces/interface&quot;;
import { DiscountTypeEnum, DiscountTypeRealEstateEnum } from &quot;../../shared/enum/primary-contract.enum&quot;;

export class DiscountDto extends ClassBased implements IDiscount {
  id: string;
  description: string;
}
export class CreateDiscountDto extends DiscountDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEnum(DiscountTypeEnum)
  @IsNotEmpty()
  type: string;

  @IsEnum(DiscountTypeRealEstateEnum)
  @IsNotEmpty()
  typeRealEstate: string;

  @IsNumber()
  @IsNotEmpty()
  value: number;
}
export class UpdateDiscountDto extends DiscountDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  name: string;
  type: string;
  typeRealEstate: string;
  value: number;
}
export class ActiveDiscountDto extends DiscountDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  active: boolean;
}

</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'UpdateDiscountDto.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
