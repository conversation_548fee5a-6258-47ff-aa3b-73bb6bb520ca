<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>ProposalDomainController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/proposal.domain/controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/proposal</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approveProposal">approveProposal</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createProposal">createProposal</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteProposal">deleteProposal</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#requestApproveProposal">requestApproveProposal</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateFiles">updateFiles</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateProposal">updateProposal</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approveProposal"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approveProposal
                        </b>
                        <a href="#approveProposal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approveProposal(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/ProposalStatusDto.html">ProposalStatusDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post(&#x27;approveProposal&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="92"
                            class="link-to-prism">src/modules/proposal.domain/controller.ts:92</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/ProposalStatusDto.html" target="_self" >ProposalStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createProposal"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createProposal
                        </b>
                        <a href="#createProposal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createProposal(user, dto: <a href="../classes/CreateProposalDto.html">CreateProposalDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="36"
                            class="link-to-prism">src/modules/proposal.domain/controller.ts:36</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/CreateProposalDto.html" target="_self" >CreateProposalDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteProposal"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteProposal
                        </b>
                        <a href="#deleteProposal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteProposal(user, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Delete(&#x27;:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="107"
                            class="link-to-prism">src/modules/proposal.domain/controller.ts:107</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requestApproveProposal"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            requestApproveProposal
                        </b>
                        <a href="#requestApproveProposal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>requestApproveProposal(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/ProposalStatusDto.html">ProposalStatusDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post(&#x27;requestApproveProposal&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="76"
                            class="link-to-prism">src/modules/proposal.domain/controller.ts:76</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/ProposalStatusDto.html" target="_self" >ProposalStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateFiles"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateFiles
                        </b>
                        <a href="#updateFiles"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateFiles(user, dto: <a href="../classes/UpdateProposalFileDto.html">UpdateProposalFileDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;files&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="123"
                            class="link-to-prism">src/modules/proposal.domain/controller.ts:123</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateProposalFileDto.html" target="_self" >UpdateProposalFileDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateProposal"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateProposal
                        </b>
                        <a href="#updateProposal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateProposal(user, dto: <a href="../classes/UpdateProposalDto.html">UpdateProposalDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="56"
                            class="link-to-prism">src/modules/proposal.domain/controller.ts:56</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateProposalDto.html" target="_self" >UpdateProposalDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Controller,
  Post,
  Put,
  Body,
  Headers,
  UseGuards,
  UseInterceptors,
  Param,
  Delete,
} from &quot;@nestjs/common&quot;;
import { LoggingInterceptor } from &quot;../../common/interceptors/logging.interceptor&quot;;
import { ProposalDomainService } from &quot;./service&quot;;
import { Action } from &quot;../shared/enum/action.enum&quot;;
import { RolesGuard } from &quot;../../common/guards/roles.guard&quot;;
import { CreateProposalDto, ProposalStatusDto, UpdateProposalDto, UpdateProposalFileDto } from &quot;./dto/proposal.dto&quot;;
import { AuthGuard } from &quot;@nestjs/passport&quot;;
import { ACGuard, UseRoles } from &quot;nest-access-control&quot;;
import { Usr } from &quot;../shared/services/user/decorator/user.decorator&quot;;
import { PermissionEnum } from &quot;../shared/enum/permission.enum&quot;;
import { ValidationPipe } from &quot;../../common/pipes/validation.pipe&quot;;
@Controller(&quot;v1/proposal&quot;)
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard(&quot;jwt&quot;))
export class ProposalDomainController {
  private actionName: string &#x3D; Action.NOTIFY;
  constructor(private readonly proposalService: ProposalDomainService) {}

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_CREATE, // &#x3D;&gt; feature name
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Post()
  async createProposal(
    @Usr() user,
    @Body(new ValidationPipe()) dto: CreateProposalDto,
    @Headers(&quot;act&quot;) actionName?: string
  ) {
    this.actionName &#x3D; actionName || this.actionName;
    return await this.proposalService.createProposal(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_UPDATE, // &#x3D;&gt; feature name
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Put()
  async updateProposal(
    @Usr() user,
    @Body(new ValidationPipe()) dto: UpdateProposalDto,
    @Headers(&quot;act&quot;) actionName?: string
  ) {
    this.actionName &#x3D; actionName || this.actionName;
    return await this.proposalService.updateProposal(
      user,
      dto,
      this.actionName
    );
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_REQUEST_APPROVED, 
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Post(&#x27;requestApproveProposal&#x27;)
  async requestApproveProposal(
    @Usr() user: any,
    @Body(new ValidationPipe()) dto: ProposalStatusDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.proposalService.requestApproveProposal(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_APPROVED, 
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })

  @Post(&#x27;approveProposal&#x27;)
  async approveProposal(
    @Usr() user: any,
    @Body(new ValidationPipe()) dto: ProposalStatusDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.proposalService.approveProposal(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_DELETE, // &#x3D;&gt; feature name
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Delete(&quot;:id&quot;)
  async deleteProposal(
    @Usr() user,
    @Param(&quot;id&quot;) id: string,
    @Headers(&quot;act&quot;) actionName?: string
  ) {
    this.actionName &#x3D; actionName || this.actionName;
    return await this.proposalService.deleteProposal(user, id, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PROPOSAL_UPDATE, // &#x3D;&gt; feature name
    action: &quot;read&quot;,
    possession: &quot;own&quot;,
  })
  @Put(&#x27;files&#x27;)
  async updateFiles(@Usr() user, @Body(new ValidationPipe()) dto: UpdateProposalFileDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.proposalService.updateProposalFiles(user, dto, this.actionName);
  }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'ProposalDomainController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
