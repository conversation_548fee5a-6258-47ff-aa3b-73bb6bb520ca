<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>PrimaryContractQueryController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/primary-contract.queryside/controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/primary-contract</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#calculateInterestById">calculateInterestById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#confirmPayment">confirmPayment</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#downloadContract">downloadContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#downloadContractById">downloadContractById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#downloadInterestCalculation">downloadInterestCalculation</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#downloadTicketByQuery">downloadTicketByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#exportDebtReportContract">exportDebtReportContract</a>
                            </li>
                            <li>
                                <a href="#findById">findById</a>
                            </li>
                            <li>
                                <a href="#findByUnitId">findByUnitId</a>
                            </li>
                            <li>
                                <a href="#findContractByDiscountPolicy">findContractByDiscountPolicy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findContractByDiscountPolicyByIds">findContractByDiscountPolicyByIds</a>
                            </li>
                            <li>
                                <a href="#findContractReminder">findContractReminder</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findDepositForPurchaseContract">findDepositForPurchaseContract</a>
                            </li>
                            <li>
                                <a href="#getByCareCustomer">getByCareCustomer</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getContract">getContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getDebtReportContract">getDebtReportContract</a>
                            </li>
                            <li>
                                <a href="#getHandoverByProject">getHandoverByProject</a>
                            </li>
                            <li>
                                <a href="#getHandoverByStatus">getHandoverByStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#historyPayment">historyPayment</a>
                            </li>
                            <li>
                                <a href="#reportHandoverByProject">reportHandoverByProject</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="calculateInterestById"></a>
                    <span class="name">
                        <b>
                            calculateInterestById
                        </b>
                        <a href="#calculateInterestById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>calculateInterestById(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;calculateInterest/:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="309"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:309</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="confirmPayment"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            confirmPayment
                        </b>
                        <a href="#confirmPayment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>confirmPayment(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, res)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;download/:id/confirmPayment&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="98"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:98</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>res</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            downloadContract
                        </b>
                        <a href="#downloadContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>downloadContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, res)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;download&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="49"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:49</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>res</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadContractById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            downloadContractById
                        </b>
                        <a href="#downloadContractById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>downloadContractById(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, res)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;download/:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="67"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:67</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>res</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadInterestCalculation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            downloadInterestCalculation
                        </b>
                        <a href="#downloadInterestCalculation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>downloadInterestCalculation(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, res)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;downloadInterestCalculation/:contractId&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="209"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:209</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>res</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadTicketByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            downloadTicketByQuery
                        </b>
                        <a href="#downloadTicketByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>downloadTicketByQuery(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, res)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/contract/download-query&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="151"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:151</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>res</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="exportDebtReportContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            exportDebtReportContract
                        </b>
                        <a href="#exportDebtReportContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>exportDebtReportContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/getDebtReportContract/export&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="185"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:185</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findById"></a>
                    <span class="name">
                        <b>
                            findById
                        </b>
                        <a href="#findById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findById(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="252"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:252</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByUnitId"></a>
                    <span class="name">
                        <b>
                            findByUnitId
                        </b>
                        <a href="#findByUnitId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findByUnitId(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, propertyUnitId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;byPropertyUnitId/:propertyUnitId&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="266"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:266</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>propertyUnitId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findContractByDiscountPolicy"></a>
                    <span class="name">
                        <b>
                            findContractByDiscountPolicy
                        </b>
                        <a href="#findContractByDiscountPolicy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findContractByDiscountPolicy(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/getByDiscountPolicy/:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="295"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:295</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findContractByDiscountPolicyByIds"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findContractByDiscountPolicyByIds
                        </b>
                        <a href="#findContractByDiscountPolicyByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findContractByDiscountPolicyByIds(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, ids: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/getByDiscountPolicy/ids&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="280"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:280</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>ids</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findContractReminder"></a>
                    <span class="name">
                        <b>
                            findContractReminder
                        </b>
                        <a href="#findContractReminder"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findContractReminder(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/getContractReminder&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="157"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:157</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findDepositForPurchaseContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findDepositForPurchaseContract
                        </b>
                        <a href="#findDepositForPurchaseContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findDepositForPurchaseContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;/getDepositContract&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="162"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:162</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getByCareCustomer"></a>
                    <span class="name">
                        <b>
                            getByCareCustomer
                        </b>
                        <a href="#getByCareCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getByCareCustomer(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/getByCareCustomer/:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="323"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:323</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getContract
                        </b>
                        <a href="#getContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="41"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:41</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getDebtReportContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getDebtReportContract
                        </b>
                        <a href="#getDebtReportContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getDebtReportContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/getDebtReportContract&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="174"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:174</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getHandoverByProject"></a>
                    <span class="name">
                        <b>
                            getHandoverByProject
                        </b>
                        <a href="#getHandoverByProject"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getHandoverByProject(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, handoverId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/handover/:handoverId&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="334"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:334</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>handoverId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getHandoverByStatus"></a>
                    <span class="name">
                        <b>
                            getHandoverByStatus
                        </b>
                        <a href="#getHandoverByStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getHandoverByStatus(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, handoverId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/handover-by-status/:handoverId&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="356"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:356</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>handoverId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="historyPayment"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            historyPayment
                        </b>
                        <a href="#historyPayment"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>historyPayment(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Get(&#x27;download/:id/historyPayment&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="123"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:123</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="reportHandoverByProject"></a>
                    <span class="name">
                        <b>
                            reportHandoverByProject
                        </b>
                        <a href="#reportHandoverByProject"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>reportHandoverByProject(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, handoverId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Get(&#x27;/handover/report/:handoverId&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="345"
                            class="link-to-prism">src/modules/primary-contract.queryside/controller.ts:345</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>handoverId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IPrimaryContractDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Controller, Get, NotFoundException, Param, Query, Res, UseInterceptors } from &quot;@nestjs/common&quot;;
import { PrimaryContractQueryService } from &quot;./service&quot;;
import { isNullOrUndefined } from &quot;util&quot;;
import { Usr } from &quot;../shared/services/user/decorator/user.decorator&quot;;
import { Body } from &quot;@nestjs/common&quot;;
import { PermissionEnum } from &quot;../shared/enum/permission.enum&quot;;
import { ACGuard, UseRoles } from &quot;nest-access-control&quot;;
import { RolesGuard } from &quot;../../common/guards/roles.guard&quot;;
import { UseGuards } from &quot;@nestjs/common&quot;;
import { LoggingInterceptor } from &quot;../../common/interceptors/logging.interceptor&quot;;
import { AuthGuard } from &quot;@nestjs/passport&quot;;
import { FileGenerationService } from &quot;./file-generation.service&quot;;
import { ConfigService } from &quot;../config/config.service&quot;;
import { existsSync, unlinkSync } from &quot;fs&quot;;
import { join } from &quot;path&quot;;
import { IPrimaryContractDocument } from &quot;./interfaces/document.interface&quot;;
import { BadRequestException } from &quot;@nestjs/common&quot;;
import { ErrorConst } from &quot;../shared/constant/error.const&quot;;
const momentTz &#x3D; require(&#x27;moment-timezone&#x27;);
import { CommonUtils } from &#x27;../shared/classes/class-utils&#x27;;
import { ScheduleQueryRepository } from &quot;../schedule.queryside/repository/schedule.query.repository&quot;;

const _ &#x3D; require(&quot;lodash&quot;);
@Controller(&quot;v1/primary-contract&quot;)
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard(&#x27;jwt&#x27;))
export class PrimaryContractQueryController {
  constructor(
    private readonly primaryContractService: PrimaryContractQueryService,
    private readonly fileGenerationService: FileGenerationService,
    private readonly scheduleQueryRepository: ScheduleQueryRepository,
    private readonly configService: ConfigService
  ) {}
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET, 
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get()
  async getContract(
    @Usr() user: any, @Query() query: any) {
    query.createdBy &#x3D; user.id;
    let isSeeAll: boolean &#x3D; user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_GET_ALL)
    return await this.primaryContractService.getContract(user, query, isSeeAll);
  }

  @Get(&#x27;download&#x27;)
  async downloadContract(
    @Usr() user: any, @Query() query: any, @Res() res) {
    query.createdBy &#x3D; user.id;
    let isSeeAll: boolean &#x3D; user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_GET_ALL)
    const data &#x3D; await this.primaryContractService.getContract(user, query, isSeeAll);
    const fileName &#x3D; &#x27;Danh_sach_contract&#x27; + new Date().getTime();
    await this.fileGenerationService.exportContract(user, fileName, data);
    const filePath &#x3D; join(this.configService.getUploadFolderPath(), &#x60;${fileName}.xlsx&#x60;);
    const isFileExisted &#x3D; existsSync(filePath);
    if (isFileExisted &#x3D;&#x3D;&#x3D; false) {
        throw new NotFoundException(&#x27;File not found&#x27;);
    }
    res.sendFile(&#x60;${fileName}.xlsx&#x60;, { root: this.configService.getUploadFolderPath() });
    setTimeout(() &#x3D;&gt; {
      unlinkSync(filePath);
    }, 5000);
  }
  @Get(&#x27;download/:id&#x27;)
  async downloadContractById(
    @Usr() user: any, @Param(&#x27;id&#x27;) id: string, @Query() query: any, @Res() res) {
    const data &#x3D; await this.primaryContractService.getContractById(user, id);
    const project &#x3D; await this.primaryContractService.getProjectById(data.primaryTransaction.project.id);
    let url &#x3D; null;
    const scheduleId &#x3D; data?.policyPayment?.schedule?.id;
    const schedule : any &#x3D; scheduleId ? await this.scheduleQueryRepository.findOne({ id: scheduleId }) : null;
    if (schedule &amp;&amp; schedule.templates &amp;&amp; schedule.templates.length) {
      url &#x3D; schedule.templates[0].absoluteUrl;
    } else if (_.get(project , &#x27;setting.templateFileContract&#x27;, null)) {
      const file &#x3D; project.setting.templateFileContract.find(item &#x3D;&gt; (item.type &#x3D;&#x3D;&#x3D; query.type));
      url &#x3D; file ? file.file.absoluteUrl : null;
    }
    if (!url) {
      throw new NotFoundException(&#x27;File template not found&#x27;);
    }

    const fileName &#x3D; &#x27;contract&#x27;+ id + new Date().getTime();

    await this.fileGenerationService.downloadContractById(user, fileName, data, url, fileName);
    const filePath &#x3D; join(this.configService.getUploadFolderPath(), &#x60;${fileName}.docx&#x60;);
    const isFileExisted &#x3D; existsSync(filePath);
    if (isFileExisted &#x3D;&#x3D;&#x3D; false) {
        throw new NotFoundException(&#x27;File not found&#x27;);
    }
    res.sendFile(&#x60;${fileName}.docx&#x60;, { root: this.configService.getUploadFolderPath() });
    setTimeout(() &#x3D;&gt; {
      unlinkSync(filePath);
    }, 5000);
  }
  @Get(&#x27;download/:id/confirmPayment&#x27;)
  async confirmPayment(
    @Usr() user: any, @Param(&#x27;id&#x27;) id: string, @Query() query: any, @Res() res) {
    const data &#x3D; await this.primaryContractService.getContractById(user, id);
    const project &#x3D; await this.primaryContractService.getProjectById(data.primaryTransaction.project.id);
    let url &#x3D; _.get(project , &#x27;setting.templateFileHasStatus&#x27;, [])
      .find(e &#x3D;&gt; e.stage &amp;&amp; e.stage.includes(parseInt(query.stage)) &amp;&amp; e.fileId &#x3D;&#x3D;&#x3D; query.fileId)?.file?.absoluteUrl;
    if (!url) {
      throw new NotFoundException(&#x27;File template not found&#x27;);
    }

    const fileName &#x3D; &#x27;contract&#x27; + id + new Date().getTime();

    await this.fileGenerationService.downloadContractById(user, fileName, data, url, fileName);
    const filePath &#x3D; join(this.configService.getUploadFolderPath(), &#x60;${fileName}.docx&#x60;);
    const isFileExisted &#x3D; existsSync(filePath);
    if (isFileExisted &#x3D;&#x3D;&#x3D; false) {
        throw new NotFoundException(&#x27;File not found&#x27;);
    }
    res.sendFile(&#x60;${fileName}.docx&#x60;, { root: this.configService.getUploadFolderPath() });
    setTimeout(() &#x3D;&gt; {
      unlinkSync(filePath);
    }, 5000);
  }

  @Get(&#x27;download/:id/historyPayment&#x27;)
  async historyPayment(
    @Usr() user: any, @Param(&#x27;id&#x27;) id: string, @Query() query: any) {
      const data &#x3D; await this.primaryContractService.getContractById(user, id);
      let url &#x3D; null;
      if (data?.policyPayment &amp;&amp; data?.policyPayment?.schedule &amp;&amp; data?.policyPayment?.schedule.id) {
        const scheduleId &#x3D; data?.policyPayment?.schedule?.id;
        const schedule : any &#x3D; scheduleId ? await this.scheduleQueryRepository.findOne({ id: scheduleId }) : null;
        if (schedule &amp;&amp; schedule.templateFiles &amp;&amp; schedule.templateFiles.length) {
          url &#x3D; schedule.templateFiles[0].absoluteUrl;
        }
      } else if(data?.policyPayment &amp;&amp; data?.policyPayment?.schedule &amp;&amp; !data?.policyPayment?.schedule.id) {
        const schedule &#x3D; data?.policyPayment?.schedule
        if (schedule &amp;&amp; schedule.templateFiles &amp;&amp; schedule.templateFiles.length) {
          url &#x3D; schedule.templateFiles[0].absoluteUrl;
        }
      }
      if (!url) {
        throw new NotFoundException(&#x27;File template not found&#x27;);
      }
  
      const fileName &#x3D; &#x27;Tien_Do_Thanh_Toan&#x27;;
      const fileNameUnique &#x3D; &#x27;Tien_Do_Thanh_Toan_&#x27; + id + &quot;_&quot; + new Date().getTime();
  
      const result &#x3D; await this.fileGenerationService.downloadContractById(user, fileNameUnique, data, url, fileName, true, true);
      return result;
      
  }
  @Get(&#x27;/contract/download-query&#x27;)
  async downloadTicketByQuery(@Usr() user: any, @Query() query: any, @Res() res) {
    query.createdBy &#x3D; user.id;
    let isSeeAll: boolean &#x3D; user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_GET_ALL)
    return await this.primaryContractService.printTicketByQuery(user, query, res, isSeeAll);
  }
  @Get(&#x27;/getContractReminder&#x27;)
  findContractReminder(@Usr() user: any) {
    return this.primaryContractService.getContractReminder();
  }

  @Get(&#x27;/getDepositContract&#x27;)
  async findDepositForPurchaseContract(@Usr() user: any, @Query() query: any) {
    let isSeeAll: boolean &#x3D; user.roles.includes(PermissionEnum.PRIMARY_CONTRACT_GET_ALL)
    return await this.primaryContractService.getDepositForPurchaseContract(user, query, isSeeAll);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET_DEPT_REPORT,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/getDebtReportContract&#x27;)
  async getDebtReportContract(@Usr() user: any, @Query() query: any) {
    return await this.primaryContractService.getDebtReportContract(user, query);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_EXPORT_DEPT_REPORT,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/getDebtReportContract/export&#x27;)
  async exportDebtReportContract(@Usr() user: any, @Query() query: any) {
    // check xem user login có được cấu hình template thống kê công nợ không
    const fileTemplate &#x3D; await this.primaryContractService.getFileByPosIdUserLogin(user);
    if (fileTemplate) {
      // Lấy data từ db
      let data &#x3D; await  this.primaryContractService.getDebtReportContract(user, query);
      data &#x3D; data.map((item, idx) &#x3D;&gt; {
        item.stt &#x3D; idx + 1;
        item.debt &#x3D; (item.debt).toString().length &lt; 5 ? item.debt : CommonUtils.parseVND(item.debt) ;
        item.countPaymentDueDate &#x3D; item.paymentDueDate &amp;&amp; this.primaryContractService.countDayBetweenCurrentDate(item.paymentDueDate) &gt; 0 
          ? this.primaryContractService.countDayBetweenCurrentDate(item.paymentDueDate).toString() : &#x27;&#x27;;
        item.paymentDueDate &#x3D; item.paymentDueDate ? momentTz(item.paymentDueDate).tz(&#x27;Asia/Ho_Chi_Minh&#x27;).format(&#x27;DD/MM/YYYY&#x27;) : &#x27;&#x27;;
        item.expiredDate &#x3D; item.expiredDate ? momentTz(item.expiredDate).tz(&#x27;Asia/Ho_Chi_Minh&#x27;).format(&#x27;DD/MM/YYYY&#x27;) : &#x27;&#x27;;
        return item;
      });
      const fileName &#x3D; &#x60;Cong_no.xlsx&#x60;;
      // đẩy data vào file
      return await this.fileGenerationService.generateDebtReportTemplate(data, fileTemplate, fileName, true);
    } else {
      throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.NOT_FOUND, &#x27;template&#x27;) });
    }
  }

  @Get(&#x27;downloadInterestCalculation/:contractId&#x27;)
  async downloadInterestCalculation(
    @Usr() user: any, @Param(&#x27;contractId&#x27;) id: string, @Query() query: any, @Res() res) {
    const data &#x3D; await this.primaryContractService.getContractById(user, id);
    let projectInfo: any &#x3D; {};
    if(data &amp;&amp; data.primaryTransaction &amp;&amp; data.primaryTransaction.project &amp;&amp; data.primaryTransaction.project.id){
      projectInfo &#x3D; await this.primaryContractService.getProjectById(data.primaryTransaction.project.id);
    }
    let url &#x3D; this.configService.getTemplateFileDownload(&#x27;Template_Interest_Calculation.docx&#x27;);
    if(!url){
      throw new NotFoundException(&#x27;File template not found&#x27;);
    }
    let interestCalculation: any &#x3D; {};
    if(query.interestId){
      interestCalculation &#x3D; data.interestCalculations.find(interset &#x3D;&gt; interset.id &#x3D;&#x3D;&#x3D; query.interestId);
    }
    interestCalculation.customerName &#x3D; data?.primaryTransaction?.customer?.personalInfo?.name;
    interestCalculation.address &#x3D; data?.primaryTransaction?.customer?.info?.address?.fullAddress;
    interestCalculation.productCode &#x3D; data?.primaryTransaction?.propertyUnit?.code;
    interestCalculation.projectName &#x3D; data?.primaryTransaction?.project?.name;
    interestCalculation.contractName &#x3D; data?.name;
    interestCalculation.signedDate &#x3D; data?.signedDate;
    interestCalculation.companyName &#x3D; projectInfo?.investor;
    interestCalculation.banks &#x3D; projectInfo?.banks;
    const fileName &#x3D; &#x27;interestCalculation&#x27;+ query.interestId + new Date().getTime() + &#x27;.docx&#x27;;

    await this.fileGenerationService.generateAndSaveInterestCalculationFile(user.name, interestCalculation, url, fileName);
    const filePath &#x3D; join(this.configService.getUploadFolderPath(), fileName);
    const isFileExisted &#x3D; existsSync(filePath);
    if (isFileExisted &#x3D;&#x3D;&#x3D; false) {
        throw new NotFoundException(&#x27;File not found&#x27;);
    }
    res.sendFile(fileName, { root: this.configService.getUploadFolderPath() });
    setTimeout(() &#x3D;&gt; {
      unlinkSync(filePath);
    }, 5000);
  }
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET_ID, 
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/:id&#x27;)
  findById(@Usr() user: any, @Param(&#x27;id&#x27;) id: string): Promise&lt;IPrimaryContractDocument&gt; {
    if (!id){
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, &#x27;null&#x27;)});
    }
    return this.primaryContractService.getContractById(user, id);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET_ID, 
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;byPropertyUnitId/:propertyUnitId&#x27;)
  findByUnitId(@Usr() user: any, @Param(&#x27;propertyUnitId&#x27;) propertyUnitId: string): Promise&lt;IPrimaryContractDocument&gt; {
    if (!propertyUnitId){
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, &#x27;null&#x27;)});
    }
    return this.primaryContractService.getContractByPropertyUnitId(user, propertyUnitId);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/getByDiscountPolicy/ids&#x27;)
  async findContractByDiscountPolicyByIds(@Usr() user: any, @Query(&#x27;ids&#x27;) ids: string): Promise&lt;IPrimaryContractDocument[]&gt; {
    if (!ids) {
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;discount-policy&quot;, &quot;ID&quot;, &#x27;null&#x27;)});
    }
    const arrId &#x3D; ids.split(&#x27;,&#x27;);
    return await Promise.all(arrId.map(id &#x3D;&gt; this.primaryContractService.getContractByDiscountPolicy(user, id)))
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/getByDiscountPolicy/:id&#x27;)
  findContractByDiscountPolicy(@Usr() user: any, @Param(&#x27;id&#x27;) id: string): Promise&lt;IPrimaryContractDocument&gt; {
    if (!id){
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;discount-policy&quot;, &quot;ID&quot;, &#x27;null&#x27;)});
    }
    return this.primaryContractService.getContractByDiscountPolicy(user, id);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET_ID,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;calculateInterest/:id&#x27;)
  calculateInterestById(@Usr() user: any, @Param(&#x27;id&#x27;) id: string, @Query() query: any): Promise&lt;any&gt; {
    if (!id){
      throw new BadRequestException({errors: ErrorConst.Error(ErrorConst.INVALID, &quot;contract&quot;, &quot;ID&quot;, &#x27;null&#x27;)});
    }
    return this.primaryContractService.calculateInterestById(user, id, query);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/getByCareCustomer/:id&#x27;)
  getByCareCustomer(@Usr() user: any, @Param(&#x27;id&#x27;) id: string, @Query() query: any): Promise&lt;IPrimaryContractDocument&gt; {
    return this.primaryContractService.getByCustomer({id: id}, query);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/handover/:handoverId&#x27;)
  getHandoverByProject(@Usr() user: any, @Param(&#x27;handoverId&#x27;) handoverId: string, @Query() query: any): Promise&lt;IPrimaryContractDocument&gt; {
    return this.primaryContractService.getHandoverByProject(handoverId, query);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/handover/report/:handoverId&#x27;)
  reportHandoverByProject(@Usr() user: any, @Param(&#x27;handoverId&#x27;) handoverId: string, @Query() query: any): Promise&lt;IPrimaryContractDocument&gt; {
    return this.primaryContractService.reportHandoverByProject(handoverId, query);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_GET,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Get(&#x27;/handover-by-status/:handoverId&#x27;)
  getHandoverByStatus(@Usr() user: any, @Param(&#x27;handoverId&#x27;) handoverId: string, @Query() query: any): Promise&lt;IPrimaryContractDocument&gt; {
    return this.primaryContractService.getHandoverByStatus(handoverId, query);
  }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'PrimaryContractQueryController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
