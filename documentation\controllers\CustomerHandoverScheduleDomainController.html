<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>CustomerHandoverScheduleDomainController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/handover-schedule.domain/customer.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/customer/handover-schedule</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#changeHandoverScheduleTime">changeHandoverScheduleTime</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#confirmHandoverSchedule">confirmHandoverSchedule</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteHandoverSchedule">deleteHandoverSchedule</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="changeHandoverScheduleTime"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            changeHandoverScheduleTime
                        </b>
                        <a href="#changeHandoverScheduleTime"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>changeHandoverScheduleTime(user, dto: <a href="../classes/UpdateHandoverScheduleDto.html">UpdateHandoverScheduleDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;change-handover-schedule-time&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="29"
                            class="link-to-prism">src/modules/handover-schedule.domain/customer.controller.ts:29</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateHandoverScheduleDto.html" target="_self" >UpdateHandoverScheduleDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="confirmHandoverSchedule"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            confirmHandoverSchedule
                        </b>
                        <a href="#confirmHandoverSchedule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>confirmHandoverSchedule(user, dto: <a href="../classes/UpdateHandoverScheduleDto.html">UpdateHandoverScheduleDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;confirm-handover-schedule-time&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="39"
                            class="link-to-prism">src/modules/handover-schedule.domain/customer.controller.ts:39</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateHandoverScheduleDto.html" target="_self" >UpdateHandoverScheduleDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteHandoverSchedule"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteHandoverSchedule
                        </b>
                        <a href="#deleteHandoverSchedule"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteHandoverSchedule(user, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Delete(&#x27;:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="49"
                            class="link-to-prism">src/modules/handover-schedule.domain/customer.controller.ts:49</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Controller,
  Post,
  Put,
  Body,
  Headers,
  UseGuards,
  UseInterceptors,
  Delete,
  Param
} from &quot;@nestjs/common&quot;;
import { LoggingInterceptor } from &quot;../../common/interceptors/logging.interceptor&quot;;
import { HandoverScheduleDomainService } from &quot;./service&quot;;
import { Action } from &quot;../shared/enum/action.enum&quot;;
import { UpdateHandoverScheduleDto } from &quot;./dto/handover-schedule.dto&quot;;
import { AuthGuard } from &quot;@nestjs/passport&quot;;
import { Usr } from &quot;../shared/services/user/decorator/user.decorator&quot;;
import { ValidationPipe } from &quot;../../common/pipes/validation.pipe&quot;;
import { HandoverScheduleStatusEnum } from &quot;../shared/enum/status.enum&quot;;

@Controller(&quot;v1/customer/handover-schedule&quot;)
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard(&quot;jwt&quot;))
export class CustomerHandoverScheduleDomainController {
  private actionName: string &#x3D; Action.NOTIFY;
  constructor(private readonly handoverService: HandoverScheduleDomainService) {}

  @Put(&#x27;change-handover-schedule-time&#x27;)
  async changeHandoverScheduleTime(
    @Usr() user,
    @Body(new ValidationPipe()) dto: UpdateHandoverScheduleDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {

    this.actionName &#x3D; (actionName || Action.CREATE);
    return await this.handoverService.customerChangeHandoverSchedule(user, dto, this.actionName);
  }

  @Put(&#x27;confirm-handover-schedule-time&#x27;)
  async confirmHandoverSchedule(
    @Usr() user,
    @Body(new ValidationPipe()) dto: UpdateHandoverScheduleDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {

    this.actionName &#x3D; (actionName || Action.CREATE); 
    return await this.handoverService.customerConfirmHandoverSchedule(user, dto, this.actionName);
  }

  @Delete(&#x27;:id&#x27;)
  async deleteHandoverSchedule(@Usr() user, @Param(&#x27;id&#x27;) id: string, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.handoverService.deleteHandoverSchedule(user, id, this.actionName);
  }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'CustomerHandoverScheduleDomainController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
