<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>ListenerController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/listener/listener.controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>listener</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkDeliveryScheduleEndDate">checkDeliveryScheduleEndDate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createContractSyncErp">createContractSyncErp</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createPaymentPolicySyncErp">createPaymentPolicySyncErp</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getPrimaryContractsByPropertyUnitCodeProjectId">getPrimaryContractsByPropertyUnitCodeProjectId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getPrimaryContractsByPropertyUnitCodesProjectIds">getPrimaryContractsByPropertyUnitCodesProjectIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#handedContractByProjectId">handedContractByProjectId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#handoverScheduleNotifyCustomer">handoverScheduleNotifyCustomer</a>
                            </li>
                            <li>
                                <a href="#listenerEmployee">listenerEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerEmployeeHandOverBusy">listenerEmployeeHandOverBusy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerGetEmployeeIdentitiesByProjectId">listenerGetEmployeeIdentitiesByProjectId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerGetHandoverSetting">listenerGetHandoverSetting</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerGetPrimaryContractByCustomer">listenerGetPrimaryContractByCustomer</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerPrimaryContract">listenerPrimaryContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerPrimaryContractByQuery">listenerPrimaryContractByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerPrimaryContractTimout">listenerPrimaryContractTimout</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerPrimaryContractUpdateDepositConfirm">listenerPrimaryContractUpdateDepositConfirm</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerPrimaryContractUpdatePathDtt">listenerPrimaryContractUpdatePathDtt</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerPrimaryContractUpdateReceipt">listenerPrimaryContractUpdateReceipt</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listenerPrimaryContractUpdateTransferConfirm">listenerPrimaryContractUpdateTransferConfirm</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#requestApproveContract">requestApproveContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#sendNoticeDeliveryBeforeDeadline">sendNoticeDeliveryBeforeDeadline</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#syncPrimaryTransaction">syncPrimaryTransaction</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#syncProject">syncProject</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updatePropertyUnits">updatePropertyUnits</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateTradeHistorySyncErp">updateTradeHistorySyncErp</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkDeliveryScheduleEndDate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkDeliveryScheduleEndDate
                        </b>
                        <a href="#checkDeliveryScheduleEndDate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkDeliveryScheduleEndDate(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="323"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:323</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createContractSyncErp"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createContractSyncErp
                        </b>
                        <a href="#createContractSyncErp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createContractSyncErp(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="85"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:85</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createPaymentPolicySyncErp"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createPaymentPolicySyncErp
                        </b>
                        <a href="#createPaymentPolicySyncErp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createPaymentPolicySyncErp(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="342"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:342</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getPrimaryContractsByPropertyUnitCodeProjectId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getPrimaryContractsByPropertyUnitCodeProjectId
                        </b>
                        <a href="#getPrimaryContractsByPropertyUnitCodeProjectId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getPrimaryContractsByPropertyUnitCodeProjectId(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="303"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:303</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getPrimaryContractsByPropertyUnitCodesProjectIds"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getPrimaryContractsByPropertyUnitCodesProjectIds
                        </b>
                        <a href="#getPrimaryContractsByPropertyUnitCodesProjectIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getPrimaryContractsByPropertyUnitCodesProjectIds(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="310"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:310</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="handedContractByProjectId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            handedContractByProjectId
                        </b>
                        <a href="#handedContractByProjectId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>handedContractByProjectId(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="363"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:363</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="handoverScheduleNotifyCustomer"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            handoverScheduleNotifyCustomer
                        </b>
                        <a href="#handoverScheduleNotifyCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>handoverScheduleNotifyCustomer(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="317"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:317</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerEmployee"></a>
                    <span class="name">
                        <b>
                            listenerEmployee
                        </b>
                        <a href="#listenerEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>listenerEmployee(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="40"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:40</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerEmployeeHandOverBusy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerEmployeeHandOverBusy
                        </b>
                        <a href="#listenerEmployeeHandOverBusy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerEmployeeHandOverBusy(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="236"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:236</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerGetEmployeeIdentitiesByProjectId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerGetEmployeeIdentitiesByProjectId
                        </b>
                        <a href="#listenerGetEmployeeIdentitiesByProjectId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerGetEmployeeIdentitiesByProjectId(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="270"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:270</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerGetHandoverSetting"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerGetHandoverSetting
                        </b>
                        <a href="#listenerGetHandoverSetting"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerGetHandoverSetting(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="77"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:77</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerGetPrimaryContractByCustomer"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerGetPrimaryContractByCustomer
                        </b>
                        <a href="#listenerGetPrimaryContractByCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerGetPrimaryContractByCustomer(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="263"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:263</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerPrimaryContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerPrimaryContract
                        </b>
                        <a href="#listenerPrimaryContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerPrimaryContract(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="63"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:63</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerPrimaryContractByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerPrimaryContractByQuery
                        </b>
                        <a href="#listenerPrimaryContractByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerPrimaryContractByQuery(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="92"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:92</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerPrimaryContractTimout"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerPrimaryContractTimout
                        </b>
                        <a href="#listenerPrimaryContractTimout"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerPrimaryContractTimout(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="243"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:243</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerPrimaryContractUpdateDepositConfirm"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerPrimaryContractUpdateDepositConfirm
                        </b>
                        <a href="#listenerPrimaryContractUpdateDepositConfirm"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerPrimaryContractUpdateDepositConfirm(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="249"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:249</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerPrimaryContractUpdatePathDtt"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerPrimaryContractUpdatePathDtt
                        </b>
                        <a href="#listenerPrimaryContractUpdatePathDtt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerPrimaryContractUpdatePathDtt(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="99"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:99</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerPrimaryContractUpdateReceipt"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerPrimaryContractUpdateReceipt
                        </b>
                        <a href="#listenerPrimaryContractUpdateReceipt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerPrimaryContractUpdateReceipt(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="150"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:150</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerPrimaryContractUpdateTransferConfirm"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listenerPrimaryContractUpdateTransferConfirm
                        </b>
                        <a href="#listenerPrimaryContractUpdateTransferConfirm"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listenerPrimaryContractUpdateTransferConfirm(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="256"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:256</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requestApproveContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            requestApproveContract
                        </b>
                        <a href="#requestApproveContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>requestApproveContract(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="276"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:276</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="sendNoticeDeliveryBeforeDeadline"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            sendNoticeDeliveryBeforeDeadline
                        </b>
                        <a href="#sendNoticeDeliveryBeforeDeadline"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>sendNoticeDeliveryBeforeDeadline(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="329"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:329</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="syncPrimaryTransaction"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            syncPrimaryTransaction
                        </b>
                        <a href="#syncPrimaryTransaction"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>syncPrimaryTransaction(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="296"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:296</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="syncProject"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            syncProject
                        </b>
                        <a href="#syncProject"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>syncProject(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="289"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:289</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatePropertyUnits"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updatePropertyUnits
                        </b>
                        <a href="#updatePropertyUnits"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updatePropertyUnits(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="282"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:282</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateTradeHistorySyncErp"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateTradeHistorySyncErp
                        </b>
                        <a href="#updateTradeHistorySyncErp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateTradeHistorySyncErp(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="335"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:335</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Controller } from &#x27;@nestjs/common&#x27;;
import { MessagePattern } from &#x27;@nestjs/microservices&#x27;;
import { CmdPatternConst, CommonConst } from &#x27;../shared/constant&#x27;;
import { EmployeeService } from &#x27;../employee.query/service&#x27;;
import { PrimaryContractQueryService } from &#x27;../primary-contract.queryside/service&#x27;;
import { PrimaryContractQueryRepository } from &#x27;../primary-contract.queryside/repository/primary-contract-query.repository&#x27;;
import { PrimaryContractDomainService } from &#x27;../primary-contract.domain/service&#x27;;
import { MsxLoggerService } from &#x27;../logger/logger.service&#x27;;
import {LiquidationDomainService} from &quot;../liquidation.domain/service&quot;;
import { HandoverScheduleQueryService } from &#x27;../handover-schedule.queryside/service&#x27;;
import {StatushistoryService} from &quot;../statusHistory/application/service&quot;;
import {Action} from &quot;../shared/enum/action.enum&quot;;
import {PolicyQueryService} from &quot;../policy.queryside/service&quot;;
import {ScheduleQueryService} from &quot;../schedule.queryside/service&quot;;
import {ErpStatusEnum, TransactionStatusEnum} from &quot;../shared/enum/status.enum&quot;;
import { HandoverScheduleDomainService } from &#x27;../handover-schedule.domain/service&#x27;;
import { HandoverQueryRepository } from &#x27;../handover.queryside/repository/handover.query.repository&#x27;;
const moment &#x3D; require(&#x27;moment&#x27;);

@Controller(&#x27;listener&#x27;)
export class ListenerController {
    private readonly context &#x3D; ListenerController.name;

    constructor(
        private readonly employeeService: EmployeeService,
        private readonly primaryContractDomainService: PrimaryContractDomainService,
        private readonly primaryContractQueryService: PrimaryContractQueryService,
        private readonly liquidationDomainService: LiquidationDomainService,
        private readonly primaryContractQueryRepository: PrimaryContractQueryRepository,
        private readonly loggerService: MsxLoggerService, 
        private readonly handoverScheduleQueryService: HandoverScheduleQueryService,
        private readonly statushistoryService: StatushistoryService,
        private readonly policyQueryService: PolicyQueryService,
        private readonly scheduleQueryService: ScheduleQueryService,
        private readonly handoverScheduleDomainService: HandoverScheduleDomainService,
        private readonly handoverQueryRepository: HandoverQueryRepository
    ) { }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.EMPLOYEE })
    listenerEmployee(pattern: any) {
        const data &#x3D; pattern.data;
        const action &#x3D; data.action;
        const model &#x3D; data.model;
        switch (action) {
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.CREATED:
                this.employeeService.create(model);
                break;
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.UPDATED:
                this.employeeService.update(model);
                break;
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.DELETED:
                this.employeeService.delete(model);
                break;
            case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.LIST_UPDATED:
                this.employeeService.updateList(model);
                break;
            default:
                break;
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT })
    async listenerPrimaryContract(pattern: any) {
        const data &#x3D; pattern.data;
        if (data.id) {
            return this.primaryContractQueryService.getContractById(null, data.id);
        } else if (data.oldContractId) {
            return this.primaryContractQueryRepository.findOne({ &quot;oldContract.id&quot;: data.oldContractId })
        } else if (data.transaction) {
            await this.primaryContractDomainService.syncContractTransaction(data.transaction);
        } else {
            return null;
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_ONE_HANDOVER_SETTING })
    async listenerGetHandoverSetting(pattern: any) {
        this.loggerService.log(this.context, &#x27;GET_HANDOVER_SETTING&#x27;);
        const data &#x3D; pattern.data;
        let handover &#x3D; await this.handoverQueryRepository.findOne(data)
        return handover;
    }

    @MessagePattern({ cmd: CmdPatternConst.PRIMARY_CONTRACT.CREATE_CONTRACT_SYNC_ERP })
    async createContractSyncErp(pattern: any) {
        this.loggerService.log(this.context, &#x27;createContractSyncErp&#x27;);
        const data &#x3D; pattern.data;
        return await this.primaryContractDomainService.createdContractSyncErp(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_ONE_BY_QUERY })
    async listenerPrimaryContractByQuery(pattern: any) {
        this.loggerService.log(this.context, &#x27;listenerPrimaryContractTimout&#x27;);
        const data &#x3D; pattern.data;
        return this.primaryContractQueryRepository.findOne(data)
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_PATHDTT })
    async listenerPrimaryContractUpdatePathDtt(pattern: any) {
        this.loggerService.log(this.context, &#x27;listenerPrimaryContractUpdatePathDtt&#x27;);
        const data &#x3D; pattern.data;

        const contract &#x3D; await this.primaryContractQueryRepository.findOne({&quot;syncErpData.contractid&quot;:  data.contractid});

        if (!contract) {
            return;
        }

        const installments &#x3D; contract.policyPayment.schedule.installments.map(i &#x3D;&gt; {
            const erpInstallments &#x3D; data.pathDtt.filter(p &#x3D;&gt; p.value0 &#x3D;&#x3D;&#x3D; i.name);
            if (erpInstallments.length &#x3D;&#x3D;&#x3D; 0) {
                return i;
            }
            const totalAmount &#x3D; erpInstallments.reduce(function (prev, cur) {
                return prev + parseInt(cur.amount);
            }, 0);
            const erpInstallment &#x3D; erpInstallments.pop();
            i.duedate &#x3D; erpInstallment.transdate ? moment(erpInstallment.transdate, &#x27;DD/MM/YYYY HH:mm:ss A&#x27;).toDate() : &#x27;&#x27;;
            i.totalAmount &#x3D; totalAmount;
            return i;
        });

        const $set: any &#x3D; {
            changeInstallment: true,
            &#x27;policyPayment.schedule.installments&#x27;: installments,
        };

        const maintenance &#x3D; data.pathDtt.find(p &#x3D;&gt; p.value0 &#x3D;&#x3D;&#x3D; &#x27;99&#x27;);
        if (maintenance) {
            $set.maintenanceFee &#x3D; {
                type: &#x27;currency&#x27;,
                value: parseInt(maintenance.amount),
                stagePayment: &#x27;99&#x27;,
            }
        }

        const q &#x3D; {
            query: {
                &quot;syncErpData.contractid&quot;:  data.contractid,
            },
            model: {
                $set,
                $push: {&quot;pathDtt&quot;: data.pathDtt}
            }
        };
        return await this.primaryContractQueryRepository.updateOne(q.query, q.model);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_RECEIPT_AMOUNT })
    async listenerPrimaryContractUpdateReceipt(pattern: any) {
        this.loggerService.log(this.context, &#x27;listenerPrimaryContractUpdateReceipt&#x27;);
        const data &#x3D; pattern.data;
        const id &#x3D; data.id;
        const transaction &#x3D; data.transaction;
        const contract: any &#x3D; await this.primaryContractQueryRepository.findOne({id});
        const instalment &#x3D; transaction.syncErpData.value0;
        let receipt &#x3D; null;
        let existReceiptId &#x3D; null;
        contract.policyPayment.schedule.installments &#x3D; contract.policyPayment.schedule.installments.map(i &#x3D;&gt; {
            let amountDiff &#x3D; 0;
            i.receipts &#x3D; i.receipts.map(r &#x3D;&gt; {
                if (r.id &#x3D;&#x3D;&#x3D; transaction.id) {
                    existReceiptId &#x3D; r.id;
                    amountDiff &#x3D; (instalment !&#x3D;&#x3D; i.name) ? 0 - r.amount : transaction.money - r.amount;
                    r.amount &#x3D; transaction.money;
                    // thay đổi đợt thanh toán
                    if (instalment !&#x3D;&#x3D; i.name) {
                        receipt &#x3D; r;
                    }
                }
                return r;
            })
            i.totalTransfered &#x3D; i.totalTransfered + amountDiff;
            return i;
        })

        if (receipt) {
            contract.policyPayment.schedule.installments &#x3D; contract.policyPayment.schedule.installments.map(i &#x3D;&gt; {
                // nhét phiếu vào đợt mới, remove phiếu khỏi đợt cũ
                if (instalment &#x3D;&#x3D;&#x3D; i.name) {
                    i.receipts.push(receipt);
                    i.totalTransfered &#x3D; i.totalTransfered + receipt.amount;
                } else {
                    const existReceipt &#x3D; i.receipts.find(r &#x3D;&gt; r.id &#x3D;&#x3D;&#x3D; transaction.id);
                    if (existReceipt) {
                        i.totalTransfered &#x3D; i.totalTransfered - receipt.amount;
                    }
                    i.receipts &#x3D; i.receipts.filter(r &#x3D;&gt; r.id !&#x3D;&#x3D; transaction.id);
                }
                return i;
            })
        }

        if (!existReceiptId) {
            // transform lại data
            transaction.amount &#x3D; transaction.money;
            transaction.receiptDate &#x3D; transaction.collectMoneyDate;
            transaction.status &#x3D; TransactionStatusEnum.transfered;
            const simpleTransaction: any &#x3D; (({ id, amount, code, status, type, receiptNum, receiptDate }) &#x3D;&gt; ({ id, amount, code, status, type, receiptNum, receiptDate }))(transaction);

            contract.policyPayment.schedule.installments &#x3D; contract.policyPayment.schedule.installments.map(i &#x3D;&gt; {
                // bổ sung phiếu bị miss
                if (instalment &#x3D;&#x3D;&#x3D; i.name) {
                    i.receipts.push(simpleTransaction);
                    i.totalTransfered &#x3D; i.totalTransfered + simpleTransaction.amount;
                }
                return i;
            })
        }

        if (!existReceiptId) {
            // transform lại data
            transaction.amount &#x3D; transaction.money;
            transaction.receiptDate &#x3D; transaction.collectMoneyDate;
            transaction.status &#x3D; TransactionStatusEnum.transfered;
            const simpleTransaction: any &#x3D; (({ id, amount, code, status, type, receiptNum, receiptDate }) &#x3D;&gt; ({ id, amount, code, status, type, receiptNum, receiptDate }))(transaction);

            contract.policyPayment.schedule.installments &#x3D; contract.policyPayment.schedule.installments.map(i &#x3D;&gt; {
                // bổ sung phiếu bị miss
                if (instalment &#x3D;&#x3D;&#x3D; i.name) {
                    i.receipts.push(simpleTransaction);
                    i.totalTransfered &#x3D; i.totalTransfered + simpleTransaction.amount;
                }
                return i;
            })
        }

        contract.paymentPercent &#x3D; this.primaryContractDomainService.calPaymentPercentage({
            installments: contract.policyPayment.schedule.installments
        });

        return await this.primaryContractQueryRepository.updateOne({id: contract.id}, contract);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.LIST_EMPLOYEE_HAND_OVER_BUSY })
    async listenerEmployeeHandOverBusy(pattern: any) {
        this.loggerService.log(this.context, &#x27;listenerPrimaryContractTimout&#x27;);
        const data &#x3D; pattern.data;
        return await this.handoverScheduleDomainService.getEmployeeHandoverBusy(data.handoverConfigId, data.startTime, data.endTime);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_TIMEOUT })
    async listenerPrimaryContractTimout(pattern: any) {
        this.loggerService.log(this.context, &#x27;listenerPrimaryContractTimout&#x27;);
        this.primaryContractQueryService.getContractReminder();
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM })
    async listenerPrimaryContractUpdateDepositConfirm(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM&#x27;);
        this.primaryContractDomainService.updateDepositConfirm(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM })
    async listenerPrimaryContractUpdateTransferConfirm(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM&#x27;);
        this.primaryContractDomainService.updateTransferConfirm(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_PRIMARY_CONTRACT_BY_CUSTOMER })
    async listenerGetPrimaryContractByCustomer(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;GET_PRIMARY_CONTRACT_BY_CUSTOMER&#x27;);
        return await this.primaryContractQueryService.getByCustomer(data.user, data.query, data.getDetail);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_CUSTOMER_IDENTITIES_BY_PROJECT_ID })
    async listenerGetEmployeeIdentitiesByProjectId(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;GET_CUSTOMER_IDENTITIES_BY_PROJECT_ID&#x27;);
        return await this.primaryContractQueryService.getAllCustomerByProjectId(data);
    }
    @MessagePattern({ cmd: CmdPatternConst.LISTENER.REQUEST_APPROVE })
    async requestApproveContract(pattern: any) {
        const data &#x3D; pattern.data;
        return await this.primaryContractDomainService.approveLiquidationContract(data.user, data.dto, &#x27;requestApproveContract&#x27;);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.UPDATE_PROPERTY_UNITS })
    async updatePropertyUnits(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;UPDATE_PROPERTY_UNITS&#x27;);
        return await this.primaryContractDomainService.updatePropertyUnits(data.units);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.SYNC_PROJECT })
    async syncProject(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;SYNC_PROJECT&#x27;);
        return await this.primaryContractQueryService.syncProject(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.SYNC_PRIMARY_TRANSACTION })
    async syncPrimaryTransaction(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;SYNC_PRIMARY_TRANSACTION&#x27;);
        return await this.primaryContractQueryService.syncPrimaryTransaction(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_PRIMARY_CONTRACT_BY_PROPERTY_UNIT_CODE_AND_PROJECT_ID })
    async getPrimaryContractsByPropertyUnitCodeProjectId(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;SYNC_GET_PRIMARY_CONTRACTS&#x27;);
        return this.primaryContractQueryService.getPrimaryContractsByPropertyUnitCodeAndProjectId(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_PRIMARY_CONTRACTS_BY_PROPERTY_UNIT_CODES_AND_PROJECT_IDS })
    async getPrimaryContractsByPropertyUnitCodesProjectIds(pattern: any) {
        const data &#x3D; pattern.data;
        this.loggerService.log(this.context, &#x27;SYNC_GET_PRIMARY_CONTRACTS&#x27;);
        return this.primaryContractQueryService.getPrimaryContractsByPropertyUnitCodesAndProjectIds(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.HANDOVER_SCHEDULE_NOTIFY_CUSTOMER })
    async handoverScheduleNotifyCustomer(pattern: any) {
        this.loggerService.log(this.context, &#x27;SYNC_HANDOVER_SCHEDULE_NOTIFY&#x27;);
        return this.handoverScheduleQueryService.handoverScheduleNotifyCustomer();
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.CHECK_DELIVERY_SCHEDULE_END_DATE })
    async checkDeliveryScheduleEndDate(pattern: any) {
        this.loggerService.log(this.context, &#x27;CHECK_DELIVERY_SCHEDULE_END_DATE&#x27;);
        return this.handoverScheduleQueryService.checkDeliveryScheduleEndDate();
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.SEND_NOTICE_DELIVERY_BEFORE_DEADLINE })
    async sendNoticeDeliveryBeforeDeadline(pattern: any) {
        this.loggerService.log(this.context, &#x27;SEND_NOTICE_DELIVERY_BEFORE_DEADLINE&#x27;);
        return this.handoverScheduleQueryService.sendNoticeDeliveryBeforeDeadline();
    }

    @MessagePattern({cmd: CmdPatternConst.LISTENER.UPDATE_TRADEHISTORY_SYNC_ERP})
    async updateTradeHistorySyncErp(pattern: any) {
        this.loggerService.log(this.context, &#x27;UPDATE_TRADEHISTORY_SYNC_ERP&#x27;);
        const data &#x3D; pattern.data;
        return await this.statushistoryService.updateTradeHistory(data);
    }

    @MessagePattern({cmd: CmdPatternConst.LISTENER.CREATE_PAYMENT_POLICY_SYNC_ERP})
    async createPaymentPolicySyncErp(pattern: any) {
        this.loggerService.log(this.context, &#x27;CREATE_PAYMENT_POLICY_SYNC_ERP&#x27;);
        const data &#x3D; pattern.data;
        const syncErpData &#x3D; data.syncErpData;
        const project &#x3D; data.project;
        const userId &#x3D; data.userId;

        // check tồn tại
        const oldPolicy: any &#x3D; await this.policyQueryService.findOne({pymtterm: syncErpData[0].pymtterm});
        const scheduleId &#x3D; oldPolicy ? oldPolicy.schedule.id : null;
        const policyId &#x3D; oldPolicy ? oldPolicy.id : null;

        const schedule &#x3D; await this.scheduleQueryService.createScheduleSyncErp(syncErpData, userId, scheduleId);
        await this.policyQueryService.createPaymentPolicySyncErp(syncErpData, project, schedule, userId, policyId);

        return {
            isSuccess: true
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_HANDED_CONTRACT_BY_PROJECT_ID })
    async handedContractByProjectId(pattern: any) {
        const projectId &#x3D; pattern.data;
        return await this.primaryContractQueryService.handedContractByProjectId(projectId);
    }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'ListenerController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
