<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>PaymentConst</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/constant/payment.const.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#BANK_LIST">BANK_LIST</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#BANK_TYPE_GLOBAL">BANK_TYPE_GLOBAL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#BANK_TYPE_LOCAL">BANK_TYPE_LOCAL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#BANK_TYPE_QRCODE">BANK_TYPE_QRCODE</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="BANK_LIST"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            BANK_LIST</b>
                            <a href="#BANK_LIST"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
        {
            INDEX: &quot;1&quot;,
            CODE: &quot;ABBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần An Bình (ABBANK)&quot;,
            LOGO: &quot;/msx-payment/images/bank/abbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;2&quot;,
            CODE: &quot;ACB&quot;,
            NAME: &quot;Ngân hàng ACB&quot;,
            LOGO: &quot;/msx-payment/images/bank/acb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;3&quot;,
            CODE: &quot;AGRIBANK&quot;,
            NAME: &quot;Ngân hàng Nông nghiệp (Agribank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/agribank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;4&quot;,
            CODE: &quot;BACABANK&quot;,
            NAME: &quot;Ngân Hàng TMCP Bắc Á&quot;,
            LOGO: &quot;/msx-payment/images/bank/bacabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;5&quot;,
            CODE: &quot;BIDV&quot;,
            NAME: &quot;Ngân hàng đầu tư và phát triển Việt Nam (BIDV)&quot;,
            LOGO: &quot;/msx-payment/images/bank/bidv_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;6&quot;,
            CODE: &quot;DONGABANK&quot;,
            NAME: &quot;Ngân hàng Đông Á (DongABank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/dongabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;7&quot;,
            CODE: &quot;EXIMBANK&quot;,
            NAME: &quot;Ngân hàng EximBank&quot;,
            LOGO: &quot;/msx-payment/images/bank/eximbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;8&quot;,
            CODE: &quot;HDBANK&quot;,
            NAME: &quot;Ngan hàng HDBank&quot;,
            LOGO: &quot;/msx-payment/images/bank/hdbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;9&quot;,
            CODE: &quot;IVB&quot;,
            NAME: &quot;Ngân hàng TNHH Indovina (IVB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ivb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;10&quot;,
            CODE: &quot;MBBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần Quân đội&quot;,
            LOGO: &quot;/msx-payment/images/bank/mbbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;11&quot;,
            CODE: &quot;MSBANK&quot;,
            NAME: &quot;Ngân hàng Hàng Hải (MSBANK)&quot;,
            LOGO: &quot;/msx-payment/images/bank/msbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;12&quot;,
            CODE: &quot;NAMABANK&quot;,
            NAME: &quot;Ngân hàng Nam Á (NamABank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/namabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;13&quot;,
            CODE: &quot;NCB&quot;,
            NAME: &quot;Ngân hàng Quốc dân (NCB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ncb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;14&quot;,
            CODE: &quot;OCB&quot;,
            NAME: &quot;Ngân hàng Phương Đông (OCB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ocb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;15&quot;,
            CODE: &quot;OJB&quot;,
            NAME: &quot;Ngân hàng Đại Dương (OceanBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ojb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;16&quot;,
            CODE: &quot;PVCOMBANK&quot;,
            NAME: &quot;Ngân hàng TMCP Đại Chúng Việt Nam&quot;,
            LOGO: &quot;/msx-payment/images/bank/PVComBank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;17&quot;,
            CODE: &quot;SACOMBANK&quot;,
            NAME: &quot;Ngân hàng TMCP Sài Gòn Thương Tín (SacomBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/sacombank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;18&quot;,
            CODE: &quot;SAIGONBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần Sài Gòn Công Thương&quot;,
            LOGO: &quot;/msx-payment/images/bank/saigonbank.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;19&quot;,
            CODE: &quot;SCB&quot;,
            NAME: &quot;Ngân hàng TMCP Sài Gòn (SCB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/scb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;20&quot;,
            CODE: &quot;SHB&quot;,
            NAME: &quot;Ngân hàng Thương mại cổ phần Sài Gòn - Hà Nội(SHB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/shb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;21&quot;,
            CODE: &quot;TECHCOMBANK&quot;,
            NAME: &quot;Ngân hàng Kỹ thương Việt Nam (TechcomBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/techcombank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;22&quot;,
            CODE: &quot;TPBANK&quot;,
            NAME: &quot;Ngân hàng Tiên Phong (TPBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/tpbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;23&quot;,
            CODE: &quot;VPBANK&quot;,
            NAME: &quot;Ngân hàng Việt Nam Thịnh vượng (VPBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vpbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;24&quot;,
            CODE: &quot;SEABANK&quot;,
            NAME: &quot;Ngân Hàng TMCP Đông Nam Á&quot;,
            LOGO: &quot;/msx-payment/images/bank/seabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;25&quot;,
            CODE: &quot;VIB&quot;,
            NAME: &quot;Ngân hàng Thương mại cổ phần Quốc tế Việt Nam (VIB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vib_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;26&quot;,
            CODE: &quot;VIETABANK&quot;,
            NAME: &quot;Ngân hàng TMCP Việt Á&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;27&quot;,
            CODE: &quot;VIETBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần Việt Nam Thương Tín&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;28&quot;,
            CODE: &quot;VIETCAPITALBANK&quot;,
            NAME: &quot;Ngân Hàng Bản Việt&quot;,
            LOGO: &quot;/msx-payment/images/bank/vccb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;29&quot;,
            CODE: &quot;VIETCOMBANK&quot;,
            NAME: &quot;Ngân hàng Ngoại thương (Vietcombank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietcombank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;30&quot;,
            CODE: &quot;VIETINBANK&quot;,
            NAME: &quot;Ngân hàng Công thương (Vietinbank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietinbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;31&quot;,
            CODE: &quot;BIDC&quot;,
            NAME: &quot;Ngân Hàng BIDC&quot;,
            LOGO: &quot;/msx-payment/images/bank/bidc_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;32&quot;,
            CODE: &quot;LAOVIETBANK&quot;,
            NAME: &quot;NGÂN HÀNG LIÊN DOANH LÀO - VIỆT&quot;,
            LOGO: &quot;/msx-payment/images/bank/laovietbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;33&quot;,
            CODE: &quot;WOORIBANK&quot;,
            NAME: &quot;Ngân hàng TNHH MTV Woori Việt Nam&quot;,
            LOGO: &quot;/msx-payment/images/bank/woori_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;34&quot;,
            CODE: &quot;AMEX&quot;,
            NAME: &quot;American Express&quot;,
            LOGO: &quot;/msx-payment/images/bank/amex_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;35&quot;,
            CODE: &quot;VISA&quot;,
            NAME: &quot;Thẻ quốc tế Visa&quot;,
            LOGO: &quot;/msx-payment/images/bank/visa_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;36&quot;,
            CODE: &quot;MASTERCARD&quot;,
            NAME: &quot;Thẻ quốc tế MasterCard&quot;,
            LOGO: &quot;/msx-payment/images/bank/mastercard_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;37&quot;,
            CODE: &quot;JCB&quot;,
            NAME: &quot;Thẻ quốc tế JCB&quot;,
            LOGO: &quot;/msx-payment/images/bank/jcb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;38&quot;,
            CODE: &quot;UPI&quot;,
            NAME: &quot;UnionPay International&quot;,
            LOGO: &quot;/msx-payment/images/bank/upi_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;39&quot;,
            CODE: &quot;VNMART&quot;,
            NAME: &quot;Ví điện tử VnMart&quot;,
            LOGO: &quot;/msx-payment/images/bank/vnmart_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;40&quot;,
            CODE: &quot;VNPAYQR&quot;,
            NAME: &quot;Cổng thanh toán VNPAYQR&quot;,
            LOGO: &quot;/msx-payment/images/bank/paytype_qr.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_QRCODE.NAME
        },
        {
            INDEX: &quot;41&quot;,
            CODE: &quot;1PAY&quot;,
            NAME: &quot;Ví điện tử 1Pay&quot;,
            LOGO: &quot;/msx-payment/images/bank/1pay_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        // {
        //     INDEX: &quot;42&quot;,
        //     CODE: &quot;FOXPAY&quot;,
        //     NAME: &quot;Ví điện tử FOXPAY&quot;,
        //     LOGO: &quot;/msx-payment/images/bank/foxpay_logo.png&quot;,
        //     TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        // },
        {
            INDEX: &quot;43&quot;,
            CODE: &quot;VIMASS&quot;,
            NAME: &quot;Ví điện tử Vimass&quot;,
            LOGO: &quot;/msx-payment/images/bank/vimass_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;44&quot;,
            CODE: &quot;VINID&quot;,
            NAME: &quot;Ví điện tử VINID&quot;,
            LOGO: &quot;/msx-payment/images/bank/vinid_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;45&quot;,
            CODE: &quot;VIVIET&quot;,
            NAME: &quot;Ví điện tử Ví Việt&quot;,
            LOGO: &quot;/msx-payment/images/bank/viviet_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;46&quot;,
            CODE: &quot;VNPTPAY&quot;,
            NAME: &quot;Ví điện tử VNPTPAY&quot;,
            LOGO: &quot;/msx-payment/images/bank/vnptpay_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;47&quot;,
            CODE: &quot;YOLO&quot;,
            NAME: &quot;Ví điện tử YOLO&quot;,
            LOGO: &quot;/msx-payment/images/bank/yolo_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;28&quot;,
            CODE: &quot;VIETCAPITALBANK&quot;,
            NAME: &quot;Ngân Hàng Bản Việt&quot;,
            LOGO: &quot;/msx-payment/images/bank/vccb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        }
    ]</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/modules/shared/constant/payment.const.ts:18</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="BANK_TYPE_GLOBAL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            BANK_TYPE_GLOBAL</b>
                            <a href="#BANK_TYPE_GLOBAL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
        NAME: &#x27;type-global&#x27;,
        RATIO: 1.1 / 100,
        BONUS: 1650 // * VNĐ
    }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="7" class="link-to-prism">src/modules/shared/constant/payment.const.ts:7</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="BANK_TYPE_LOCAL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            BANK_TYPE_LOCAL</b>
                            <a href="#BANK_TYPE_LOCAL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
        NAME: &#x27;type-local&#x27;,
        RATIO: 2.4 / 100,
        BONUS: 2200 // * VNĐ
    }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="2" class="link-to-prism">src/modules/shared/constant/payment.const.ts:2</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="BANK_TYPE_QRCODE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            BANK_TYPE_QRCODE</b>
                            <a href="#BANK_TYPE_QRCODE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
        NAME: &#x27;type-qrcode&#x27;,
        RATIO: 0.88 / 100,
        BONUS: 0 // * VNĐ
    }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="12" class="link-to-prism">src/modules/shared/constant/payment.const.ts:12</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">export class PaymentConst {
    static BANK_TYPE_LOCAL &#x3D; {
        NAME: &#x27;type-local&#x27;,
        RATIO: 2.4 / 100,
        BONUS: 2200 // * VNĐ
    };
    static BANK_TYPE_GLOBAL &#x3D; {
        NAME: &#x27;type-global&#x27;,
        RATIO: 1.1 / 100,
        BONUS: 1650 // * VNĐ
    };
    static BANK_TYPE_QRCODE &#x3D; {
        NAME: &#x27;type-qrcode&#x27;,
        RATIO: 0.88 / 100,
        BONUS: 0 // * VNĐ
    };

    static BANK_LIST &#x3D; [
        {
            INDEX: &quot;1&quot;,
            CODE: &quot;ABBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần An Bình (ABBANK)&quot;,
            LOGO: &quot;/msx-payment/images/bank/abbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;2&quot;,
            CODE: &quot;ACB&quot;,
            NAME: &quot;Ngân hàng ACB&quot;,
            LOGO: &quot;/msx-payment/images/bank/acb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;3&quot;,
            CODE: &quot;AGRIBANK&quot;,
            NAME: &quot;Ngân hàng Nông nghiệp (Agribank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/agribank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;4&quot;,
            CODE: &quot;BACABANK&quot;,
            NAME: &quot;Ngân Hàng TMCP Bắc Á&quot;,
            LOGO: &quot;/msx-payment/images/bank/bacabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;5&quot;,
            CODE: &quot;BIDV&quot;,
            NAME: &quot;Ngân hàng đầu tư và phát triển Việt Nam (BIDV)&quot;,
            LOGO: &quot;/msx-payment/images/bank/bidv_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;6&quot;,
            CODE: &quot;DONGABANK&quot;,
            NAME: &quot;Ngân hàng Đông Á (DongABank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/dongabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;7&quot;,
            CODE: &quot;EXIMBANK&quot;,
            NAME: &quot;Ngân hàng EximBank&quot;,
            LOGO: &quot;/msx-payment/images/bank/eximbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;8&quot;,
            CODE: &quot;HDBANK&quot;,
            NAME: &quot;Ngan hàng HDBank&quot;,
            LOGO: &quot;/msx-payment/images/bank/hdbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;9&quot;,
            CODE: &quot;IVB&quot;,
            NAME: &quot;Ngân hàng TNHH Indovina (IVB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ivb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;10&quot;,
            CODE: &quot;MBBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần Quân đội&quot;,
            LOGO: &quot;/msx-payment/images/bank/mbbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;11&quot;,
            CODE: &quot;MSBANK&quot;,
            NAME: &quot;Ngân hàng Hàng Hải (MSBANK)&quot;,
            LOGO: &quot;/msx-payment/images/bank/msbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;12&quot;,
            CODE: &quot;NAMABANK&quot;,
            NAME: &quot;Ngân hàng Nam Á (NamABank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/namabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;13&quot;,
            CODE: &quot;NCB&quot;,
            NAME: &quot;Ngân hàng Quốc dân (NCB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ncb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;14&quot;,
            CODE: &quot;OCB&quot;,
            NAME: &quot;Ngân hàng Phương Đông (OCB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ocb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;15&quot;,
            CODE: &quot;OJB&quot;,
            NAME: &quot;Ngân hàng Đại Dương (OceanBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/ojb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;16&quot;,
            CODE: &quot;PVCOMBANK&quot;,
            NAME: &quot;Ngân hàng TMCP Đại Chúng Việt Nam&quot;,
            LOGO: &quot;/msx-payment/images/bank/PVComBank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;17&quot;,
            CODE: &quot;SACOMBANK&quot;,
            NAME: &quot;Ngân hàng TMCP Sài Gòn Thương Tín (SacomBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/sacombank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;18&quot;,
            CODE: &quot;SAIGONBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần Sài Gòn Công Thương&quot;,
            LOGO: &quot;/msx-payment/images/bank/saigonbank.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;19&quot;,
            CODE: &quot;SCB&quot;,
            NAME: &quot;Ngân hàng TMCP Sài Gòn (SCB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/scb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;20&quot;,
            CODE: &quot;SHB&quot;,
            NAME: &quot;Ngân hàng Thương mại cổ phần Sài Gòn - Hà Nội(SHB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/shb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;21&quot;,
            CODE: &quot;TECHCOMBANK&quot;,
            NAME: &quot;Ngân hàng Kỹ thương Việt Nam (TechcomBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/techcombank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;22&quot;,
            CODE: &quot;TPBANK&quot;,
            NAME: &quot;Ngân hàng Tiên Phong (TPBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/tpbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;23&quot;,
            CODE: &quot;VPBANK&quot;,
            NAME: &quot;Ngân hàng Việt Nam Thịnh vượng (VPBank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vpbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;24&quot;,
            CODE: &quot;SEABANK&quot;,
            NAME: &quot;Ngân Hàng TMCP Đông Nam Á&quot;,
            LOGO: &quot;/msx-payment/images/bank/seabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;25&quot;,
            CODE: &quot;VIB&quot;,
            NAME: &quot;Ngân hàng Thương mại cổ phần Quốc tế Việt Nam (VIB)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vib_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;26&quot;,
            CODE: &quot;VIETABANK&quot;,
            NAME: &quot;Ngân hàng TMCP Việt Á&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietabank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;27&quot;,
            CODE: &quot;VIETBANK&quot;,
            NAME: &quot;Ngân hàng thương mại cổ phần Việt Nam Thương Tín&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;28&quot;,
            CODE: &quot;VIETCAPITALBANK&quot;,
            NAME: &quot;Ngân Hàng Bản Việt&quot;,
            LOGO: &quot;/msx-payment/images/bank/vccb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;29&quot;,
            CODE: &quot;VIETCOMBANK&quot;,
            NAME: &quot;Ngân hàng Ngoại thương (Vietcombank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietcombank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;30&quot;,
            CODE: &quot;VIETINBANK&quot;,
            NAME: &quot;Ngân hàng Công thương (Vietinbank)&quot;,
            LOGO: &quot;/msx-payment/images/bank/vietinbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;31&quot;,
            CODE: &quot;BIDC&quot;,
            NAME: &quot;Ngân Hàng BIDC&quot;,
            LOGO: &quot;/msx-payment/images/bank/bidc_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;32&quot;,
            CODE: &quot;LAOVIETBANK&quot;,
            NAME: &quot;NGÂN HÀNG LIÊN DOANH LÀO - VIỆT&quot;,
            LOGO: &quot;/msx-payment/images/bank/laovietbank_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;33&quot;,
            CODE: &quot;WOORIBANK&quot;,
            NAME: &quot;Ngân hàng TNHH MTV Woori Việt Nam&quot;,
            LOGO: &quot;/msx-payment/images/bank/woori_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;34&quot;,
            CODE: &quot;AMEX&quot;,
            NAME: &quot;American Express&quot;,
            LOGO: &quot;/msx-payment/images/bank/amex_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;35&quot;,
            CODE: &quot;VISA&quot;,
            NAME: &quot;Thẻ quốc tế Visa&quot;,
            LOGO: &quot;/msx-payment/images/bank/visa_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;36&quot;,
            CODE: &quot;MASTERCARD&quot;,
            NAME: &quot;Thẻ quốc tế MasterCard&quot;,
            LOGO: &quot;/msx-payment/images/bank/mastercard_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;37&quot;,
            CODE: &quot;JCB&quot;,
            NAME: &quot;Thẻ quốc tế JCB&quot;,
            LOGO: &quot;/msx-payment/images/bank/jcb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;38&quot;,
            CODE: &quot;UPI&quot;,
            NAME: &quot;UnionPay International&quot;,
            LOGO: &quot;/msx-payment/images/bank/upi_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_GLOBAL.NAME
        },
        {
            INDEX: &quot;39&quot;,
            CODE: &quot;VNMART&quot;,
            NAME: &quot;Ví điện tử VnMart&quot;,
            LOGO: &quot;/msx-payment/images/bank/vnmart_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;40&quot;,
            CODE: &quot;VNPAYQR&quot;,
            NAME: &quot;Cổng thanh toán VNPAYQR&quot;,
            LOGO: &quot;/msx-payment/images/bank/paytype_qr.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_QRCODE.NAME
        },
        {
            INDEX: &quot;41&quot;,
            CODE: &quot;1PAY&quot;,
            NAME: &quot;Ví điện tử 1Pay&quot;,
            LOGO: &quot;/msx-payment/images/bank/1pay_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        // {
        //     INDEX: &quot;42&quot;,
        //     CODE: &quot;FOXPAY&quot;,
        //     NAME: &quot;Ví điện tử FOXPAY&quot;,
        //     LOGO: &quot;/msx-payment/images/bank/foxpay_logo.png&quot;,
        //     TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        // },
        {
            INDEX: &quot;43&quot;,
            CODE: &quot;VIMASS&quot;,
            NAME: &quot;Ví điện tử Vimass&quot;,
            LOGO: &quot;/msx-payment/images/bank/vimass_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;44&quot;,
            CODE: &quot;VINID&quot;,
            NAME: &quot;Ví điện tử VINID&quot;,
            LOGO: &quot;/msx-payment/images/bank/vinid_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;45&quot;,
            CODE: &quot;VIVIET&quot;,
            NAME: &quot;Ví điện tử Ví Việt&quot;,
            LOGO: &quot;/msx-payment/images/bank/viviet_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;46&quot;,
            CODE: &quot;VNPTPAY&quot;,
            NAME: &quot;Ví điện tử VNPTPAY&quot;,
            LOGO: &quot;/msx-payment/images/bank/vnptpay_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;47&quot;,
            CODE: &quot;YOLO&quot;,
            NAME: &quot;Ví điện tử YOLO&quot;,
            LOGO: &quot;/msx-payment/images/bank/yolo_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        },
        {
            INDEX: &quot;28&quot;,
            CODE: &quot;VIETCAPITALBANK&quot;,
            NAME: &quot;Ngân Hàng Bản Việt&quot;,
            LOGO: &quot;/msx-payment/images/bank/vccb_logo.png&quot;,
            TYPE: PaymentConst.BANK_TYPE_LOCAL.NAME
        }
    ]
}</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'PaymentConst.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
