<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>PrimaryContractDomainController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/primary-contract.domain/controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/primary-contract</code>
            </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approveContract">approveContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approveInterestCalculation">approveInterestCalculation</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#approvePurchaseContract">approvePurchaseContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createPrimaryContract">createPrimaryContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createPurchaseContract">createPurchaseContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteInterestCalculation">deleteInterestCalculation</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deletePolicy">deletePolicy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#handover">handover</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#importPropertyPrimary">importPropertyPrimary</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#requestApproveContract">requestApproveContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#sendDeliveryNotify">sendDeliveryNotify</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#showReceipt">showReceipt</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateDeliveryDate">updateDeliveryDate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateFiles">updateFiles</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateInterestCalculation">updateInterestCalculation</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateManyPrimaryContract">updateManyPrimaryContract</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updatePolicy">updatePolicy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updatePurchaseContract">updatePurchaseContract</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approveContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approveContract
                        </b>
                        <a href="#approveContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approveContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/PrimaryContractStatusDto.html">PrimaryContractStatusDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post(&#x27;approveContract&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="89"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:89</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/PrimaryContractStatusDto.html" target="_self" >PrimaryContractStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approveInterestCalculation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approveInterestCalculation
                        </b>
                        <a href="#approveInterestCalculation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approveInterestCalculation(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/UpdateInterestCalculationDto.html">UpdateInterestCalculationDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;approveInterestCalculation&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="118"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:118</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateInterestCalculationDto.html" target="_self" >UpdateInterestCalculationDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="approvePurchaseContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            approvePurchaseContract
                        </b>
                        <a href="#approvePurchaseContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>approvePurchaseContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/PrimaryContractStatusDto.html">PrimaryContractStatusDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;approvePurchaseContract&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="97"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:97</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/PrimaryContractStatusDto.html" target="_self" >PrimaryContractStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createPrimaryContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createPrimaryContract
                        </b>
                        <a href="#createPrimaryContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createPrimaryContract(user, dto: <a href="../classes/CreatePrimaryContractDto.html">CreatePrimaryContractDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="50"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:50</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/CreatePrimaryContractDto.html" target="_self" >CreatePrimaryContractDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createPurchaseContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createPurchaseContract
                        </b>
                        <a href="#createPurchaseContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createPurchaseContract(user, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Post(&#x27;purchase&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="55"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:55</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteInterestCalculation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteInterestCalculation
                        </b>
                        <a href="#deleteInterestCalculation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteInterestCalculation(user, dto: <a href="../classes/UpdateInterestCalculationDto.html">UpdateInterestCalculationDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;deleteInterestCalculation&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="193"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:193</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateInterestCalculationDto.html" target="_self" >UpdateInterestCalculationDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deletePolicy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deletePolicy
                        </b>
                        <a href="#deletePolicy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deletePolicy(user, id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Delete(&#x27;:id&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="181"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:181</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="handover"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            handover
                        </b>
                        <a href="#handover"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>handover(user, dto: <a href="../classes/HandoverPrimaryContractDto.html">HandoverPrimaryContractDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;deliveryConfirm&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="240"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:240</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/HandoverPrimaryContractDto.html" target="_self" >HandoverPrimaryContractDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="importPropertyPrimary"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            importPropertyPrimary
                        </b>
                        <a href="#importPropertyPrimary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>importPropertyPrimary(files, user, dto, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@UseInterceptors(undefined)<br />@Post(&#x27;/import&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="230"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:230</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>files</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="requestApproveContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            requestApproveContract
                        </b>
                        <a href="#requestApproveContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>requestApproveContract(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/PrimaryContractStatusDto.html">PrimaryContractStatusDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post(&#x27;requestApproveContract&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="73"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:73</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/PrimaryContractStatusDto.html" target="_self" >PrimaryContractStatusDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="sendDeliveryNotify"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            sendDeliveryNotify
                        </b>
                        <a href="#sendDeliveryNotify"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>sendDeliveryNotify(user, dto: <a href="../classes/SendPrimaryContractDeliveryNotifyDto.html">SendPrimaryContractDeliveryNotifyDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;sendDeliveryNotify&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="205"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:205</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/SendPrimaryContractDeliveryNotifyDto.html" target="_self" >SendPrimaryContractDeliveryNotifyDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="showReceipt"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            showReceipt
                        </b>
                        <a href="#showReceipt"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>showReceipt(user, dto: <a href="../classes/UpdateShowReceiptDto.html">UpdateShowReceiptDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;updateShowReceipt&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="217"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:217</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateShowReceiptDto.html" target="_self" >UpdateShowReceiptDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateDeliveryDate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateDeliveryDate
                        </b>
                        <a href="#updateDeliveryDate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateDeliveryDate(user, dto: <a href="../classes/UpdatePrimaryContractDeliveryDateDto.html">UpdatePrimaryContractDeliveryDateDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;deliveryDate&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="157"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:157</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdatePrimaryContractDeliveryDateDto.html" target="_self" >UpdatePrimaryContractDeliveryDateDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateFiles"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateFiles
                        </b>
                        <a href="#updateFiles"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateFiles(user, dto: <a href="../classes/UpdatePrimaryContractFileDto.html">UpdatePrimaryContractFileDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;files&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="145"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:145</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdatePrimaryContractFileDto.html" target="_self" >UpdatePrimaryContractFileDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateInterestCalculation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateInterestCalculation
                        </b>
                        <a href="#updateInterestCalculation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateInterestCalculation(user, dto: <a href="../classes/UpdateInterestCalculationDto.html">UpdateInterestCalculationDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put(&#x27;interestCalculation&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="169"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:169</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateInterestCalculationDto.html" target="_self" >UpdateInterestCalculationDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateManyPrimaryContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateManyPrimaryContract
                        </b>
                        <a href="#updateManyPrimaryContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateManyPrimaryContract(user, dto: <a href="../classes/UpdateManyPrimaryContract.html">UpdateManyPrimaryContract</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Post(&#x27;updateManyPrimaryContract&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="112"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:112</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateManyPrimaryContract.html" target="_self" >UpdateManyPrimaryContract</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatePolicy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updatePolicy
                        </b>
                        <a href="#updatePolicy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updatePolicy(user, dto: <a href="../classes/UpdatePrimaryContractDto.html">UpdatePrimaryContractDto</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@UseGuards(RolesGuard, ACGuard)<br />@UseRoles({resource: undefined, action: &#x27;read&#x27;, possession: &#x27;own&#x27;})<br />@Put()<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="133"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:133</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdatePrimaryContractDto.html" target="_self" >UpdatePrimaryContractDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatePurchaseContract"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updatePurchaseContract
                        </b>
                        <a href="#updatePurchaseContract"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updatePurchaseContract(user, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@Put(&#x27;purchase&#x27;)<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="61"
                            class="link-to-prism">src/modules/primary-contract.domain/controller.ts:61</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Controller,
  Post,
  Body,
  Headers,
  UseInterceptors,
  Put,
  Delete,
  Param, UploadedFiles,
} from &quot;@nestjs/common&quot;;
import { LoggingInterceptor } from &quot;../../common/interceptors/logging.interceptor&quot;;
import { PrimaryContractDomainService } from &quot;./service&quot;;
import { Action } from &quot;../shared/enum/action.enum&quot;;
import { Usr } from &#x27;../shared/services/user/decorator/user.decorator&#x27;;
import { ValidationPipe } from &quot;@nestjs/common&quot;;
import {
  HandoverPrimaryContractDto,
  CreatePrimaryContractDto,
  PrimaryContractStatusDto, SendPrimaryContractDeliveryNotifyDto,
  UpdateInterestCalculationDto, UpdateManyPrimaryContract, UpdatePrimaryContractDeliveryDateDto,
  UpdatePrimaryContractDto,
  UpdatePrimaryContractFileDto,
  UpdateShowReceiptDto
} from &quot;./dto/primary-contract.dto&quot;;
import { UseGuards } from &quot;@nestjs/common&quot;;
import { AuthGuard } from &quot;@nestjs/passport&quot;;
import { RolesGuard } from &quot;../../common/guards/roles.guard&quot;;
import { ACGuard, UseRoles } from &quot;nest-access-control&quot;;
import { PermissionEnum } from &quot;../shared/enum/permission.enum&quot;;
import {FilesInterceptor} from &quot;@nestjs/platform-express&quot;;
import { PrimaryContractCustomerDomainService } from &quot;./customer-service&quot;;

@Controller(&quot;v1/primary-contract&quot;)
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard(&#x27;jwt&#x27;))
export class PrimaryContractDomainController {
  private actionName: string &#x3D; Action.NOTIFY;
  private resSuccess &#x3D; { success: true };
  constructor(
    private readonly primaryContractService: PrimaryContractDomainService,
    private readonly primaryContractCustomerService: PrimaryContractCustomerDomainService
  ) {}
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_CREATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Post()
  async createPrimaryContract(@Usr() user, @Body(new ValidationPipe()) dto: CreatePrimaryContractDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.createContract(user, dto, this.actionName);
  }
  @Post(&#x27;purchase&#x27;)
  async createPurchaseContract(@Usr() user, @Body(new ValidationPipe()) dto: any, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.createPurchaseContract(user, dto, this.actionName);
  }

  @Put(&#x27;purchase&#x27;)
  async updatePurchaseContract(@Usr() user, @Body(new ValidationPipe()) dto: any, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.updatePurchaseContract(user, dto, this.actionName);
  }
  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_REQUEST_APPROVED,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })

  @Post(&#x27;requestApproveContract&#x27;)
  async requestApproveContract(
    @Usr() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.requestApproveContract(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_APPROVED,
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })

  @Post(&#x27;approveContract&#x27;)
  async approveContract(
    @Usr() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.approveContract(user, dto, this.actionName);
  }
  @Post(&#x27;approvePurchaseContract&#x27;)
  async approvePurchaseContract(
    @Usr() user: any,
    @Body(new ValidationPipe()) dto: PrimaryContractStatusDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.approvePurchaseContract(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Post(&#x27;updateManyPrimaryContract&#x27;)
  async updateManyPrimaryContract(@Usr() user, @Body() dto: UpdateManyPrimaryContract, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.updateManyPrimaryContract(user, dto, this.actionName);
  }

  @Put(&#x27;approveInterestCalculation&#x27;)
  async approveInterestCalculation(
    @Usr() user: any,
    @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto,
    @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.approveInterestCalculation(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put()
  async updatePolicy(@Usr() user, @Body(new ValidationPipe()) dto: UpdatePrimaryContractDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.updateContract(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put(&#x27;files&#x27;)
  async updateFiles(@Usr() user, @Body(new ValidationPipe()) dto: UpdatePrimaryContractFileDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.updateContractFiles(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put(&#x27;deliveryDate&#x27;)
  async updateDeliveryDate(@Usr() user, @Body(new ValidationPipe()) dto: UpdatePrimaryContractDeliveryDateDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.updateDeliveryDate(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put(&#x27;interestCalculation&#x27;)
  async updateInterestCalculation(@Usr() user, @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.updateInterestCalculation(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_DELETE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Delete(&#x27;:id&#x27;)
  async deletePolicy(@Usr() user, @Param(&#x27;id&#x27;) id: string, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.deleteContract(user, id, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_DELETE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put(&#x27;deleteInterestCalculation&#x27;)
  async deleteInterestCalculation(@Usr() user, @Body(new ValidationPipe()) dto: UpdateInterestCalculationDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.deleteInterestCalculation(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put(&#x27;sendDeliveryNotify&#x27;)
  async sendDeliveryNotify(@Usr() user, @Body(new ValidationPipe()) dto: SendPrimaryContractDeliveryNotifyDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractService.sendDeliveryNotify(user, dto, this.actionName);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_UPDATE, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @Put(&#x27;updateShowReceipt&#x27;)
  async showReceipt(@Usr() user, @Body(new ValidationPipe()) dto: UpdateShowReceiptDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    await this.primaryContractService.updateShowReceipt(dto);
  }

  @UseGuards(RolesGuard, ACGuard)
  @UseRoles({
    resource: PermissionEnum.PRIMARY_CONTRACT_IMPORT, // &#x3D;&gt; feature name
    action: &#x27;read&#x27;,
    possession: &#x27;own&#x27;,
  })
  @UseInterceptors(FilesInterceptor(&#x27;files&#x27;))
  @Post(&#x27;/import&#x27;)
  async importPropertyPrimary(
    @UploadedFiles() files,
    @Usr() user,
    @Body() dto,
    @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    await this.primaryContractService.importFiles(user, dto, files, this.actionName);
    return this.resSuccess;
  }
  @Put(&#x27;deliveryConfirm&#x27;)
  async handover(@Usr() user, @Body(new ValidationPipe()) dto: HandoverPrimaryContractDto, @Headers(&#x27;act&#x27;) actionName?: string) {
    this.actionName &#x3D; (actionName || this.actionName);
    return await this.primaryContractCustomerService.handover(user, dto, this.actionName) || this.resSuccess;
  }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'PrimaryContractDomainController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
