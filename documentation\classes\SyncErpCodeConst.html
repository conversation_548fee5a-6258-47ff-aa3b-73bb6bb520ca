<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>SyncErpCodeConst</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/constant/handover.const.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD00">XD00</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD01">XD01</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD02">XD02</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD03">XD03</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD04">XD04</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD05">XD05</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD06">XD06</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#XD07">XD07</a>
                            </li>
                        </ul>
                    </td>
                </tr>






        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD00"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD00</b>
                            <a href="#XD00"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD00&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/modules/shared/constant/handover.const.ts:29</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD01"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD01</b>
                            <a href="#XD01"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD01&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="30" class="link-to-prism">src/modules/shared/constant/handover.const.ts:30</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD02"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD02</b>
                            <a href="#XD02"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD02&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/modules/shared/constant/handover.const.ts:31</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD03"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD03</b>
                            <a href="#XD03"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD03&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/modules/shared/constant/handover.const.ts:32</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD04"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD04</b>
                            <a href="#XD04"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD04&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/modules/shared/constant/handover.const.ts:33</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD05"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD05</b>
                            <a href="#XD05"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD05&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/modules/shared/constant/handover.const.ts:34</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD06"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD06</b>
                            <a href="#XD06"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD06&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/modules/shared/constant/handover.const.ts:35</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XD07"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            XD07</b>
                            <a href="#XD07"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;XD07&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/modules/shared/constant/handover.const.ts:36</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>







    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">export class HandoverConst {
  static DN_MIN_PERCENT &#x3D; 100; // % tối thiểu để bàn giao Đất nền
  static CH_MIN_PERCENT &#x3D; 95; // % tối thiểu để bàn giao Căn hộ
  static CH_TYPE &#x3D; &#x27;Căn hộ&#x27;; // Căn hộ
}
export class HandoverScheduleStatusNameConst {
  static DURING_CONSTRUCTION &#x3D; &#x27;Sản phẩm trong quá trình XD/ Dở dang&#x27;;
  static QLDA_TEST &#x3D; &#x27;Sản phẩm cần Ban QLDA tiến hành kiểm tra&#x27;;
  static QA_QC_TEST &#x3D; &#x27;Sản phẩm cần Ban QA/QC tiến hành kiểm tra&#x27;;
  static COMPLETED &#x3D; &#x27;Sản phẩm đã hoàn thiện sẵn sàng bàn giao&#x27;;
  static ELIGIBLE &#x3D; &#x27;Sản phẩm đã đủ điều kiện bàn giao&#x27;;
  static HAVE_SCHEDULE &#x3D; &#x27;Sản phẩm đã có lịch bàn giao&#x27;;
  static HANDED_OVER &#x3D; &#x27;Căn hộ đã bàn giao&#x27;;
  static NEED_REPAIR &#x3D; &#x27;Ban quản lý cần sửa chữa trong tối đa 3 ngày&#x27;;
}

export class HandoverScheduleActionNameConst {
  static CREATE_SCHEDULE &#x3D; &#x27;Đặt lịch hẹn&#x27;;
  static ACCEPT_SCHEDULE &#x3D; &#x27;Xác nhận lịch bàn giao&#x27;;
  static ACCEPT_HANDOVER  &#x3D; &#x27;Xác nhận bàn giao&#x27;;
  static EDIT_SCHEDULE &#x3D; &#x27;Chỉnh sửa lịch bàn giao&#x27;;
  static RE_CREATE_SCHEDULE &#x3D; &#x27;Hẹn lại lịch bàn giao&#x27;;
  static RE_ACCEPT_SCHEDULE &#x3D; &#x27;Xác nhận hẹn lại lịch bàn giao&#x27;;
  static EDIT_STATUS_XD &#x3D; &#x27;Điều chỉnh trạng thái XD&#x27;;
  static EDIT_STATUS &#x3D; &#x27;Thay đổi trạng thái&#x27;;
}

export class SyncErpCodeConst {
  static XD00 &#x3D; &#x27;XD00&#x27;;
  static XD01 &#x3D; &#x27;XD01&#x27;;
  static XD02 &#x3D; &#x27;XD02&#x27;;
  static XD03 &#x3D; &#x27;XD03&#x27;;
  static XD04 &#x3D; &#x27;XD04&#x27;;
  static XD05 &#x3D; &#x27;XD05&#x27;;
  static XD06 &#x3D; &#x27;XD06&#x27;;
  static XD07 &#x3D; &#x27;XD07&#x27;;
}</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'SyncErpCodeConst.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
