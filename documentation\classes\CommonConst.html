<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content class">
                   <div class="content-data">











<ol class="breadcrumb">
  <li>Classes</li>
  <li>CommonConst</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/shared/constant/common.const.ts</code>
        </p>





            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#AGGREGATE_NAME">AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#AGGREGATES">AGGREGATES</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#AGGREGATES_LISTENER">AGGREGATES_LISTENER</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#BIEN_BAN_THANH_LY">BIEN_BAN_THANH_LY</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CODE_COLLECTION">CODE_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CONTRACT_COLLECTION">CONTRACT_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CONTRACT_EVENTS">CONTRACT_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CONTRACT_IMPORT_HISTORY_COLLECTION">CONTRACT_IMPORT_HISTORY_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#CONTRACT_IMPORT_HISTORY_QUERY_MODEL_TOKEN">CONTRACT_IMPORT_HISTORY_QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DATABASE_SERVICE_EVENTS_MODEL">DATABASE_SERVICE_EVENTS_MODEL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DATABASE_SERVICE_READ_MODEL">DATABASE_SERVICE_READ_MODEL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DISCOUNT_AGGREGATE_NAME">DISCOUNT_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DISCOUNT_COLLECTION">DISCOUNT_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DISCOUNT_EVENTS">DISCOUNT_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DOMAIN_CONNECTION_TOKEN">DOMAIN_CONNECTION_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#DOMAIN_MODEL_TOKEN">DOMAIN_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMAIL_THANH_TOAN_DOT_1">EMAIL_THANH_TOAN_DOT_1</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMAIL_THANH_TOAN_DOT_TIEP_THEO">EMAIL_THANH_TOAN_DOT_TIEP_THEO</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#EMPLOYEE_COLLECTION">EMPLOYEE_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_AGGREGATE_NAME">HANDOVER_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_COLLECTION">HANDOVER_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_DOMAIN_MODEL_TOKEN">HANDOVER_DOMAIN_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_EVENTS">HANDOVER_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_QUERY_MODEL_TOKEN">HANDOVER_QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_REQUEST_AGGREGATE_NAME">HANDOVER_REQUEST_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_REQUEST_COLLECTION">HANDOVER_REQUEST_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_REQUEST_DOMAIN_MODEL_TOKEN">HANDOVER_REQUEST_DOMAIN_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_REQUEST_EVENTS">HANDOVER_REQUEST_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_REQUEST_QUERY_MODEL_TOKEN">HANDOVER_REQUEST_QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_SCHEDULE_AGGREGATE_NAME">HANDOVER_SCHEDULE_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_SCHEDULE_COLLECTION">HANDOVER_SCHEDULE_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_SCHEDULE_DOMAIN_MODEL_TOKEN">HANDOVER_SCHEDULE_DOMAIN_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_SCHEDULE_EVENTS">HANDOVER_SCHEDULE_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HANDOVER_SCHEDULE_QUERY_MODEL_TOKEN">HANDOVER_SCHEDULE_QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HEADER_PARTERN_STR">HEADER_PARTERN_STR</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#HINH_THUC">HINH_THUC</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LIQUIDATION_AGGREGATE_NAME">LIQUIDATION_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LIQUIDATION_COLLECTION">LIQUIDATION_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LIQUIDATION_DOMAIN_MODEL_TOKEN">LIQUIDATION_DOMAIN_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LIQUIDATION_EVENTS">LIQUIDATION_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#LIQUIDATION_QUERY_MODEL_TOKEN">LIQUIDATION_QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#POLICY_AGGREGATE_NAME">POLICY_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#POLICY_COLLECTION">POLICY_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#POLICY_EVENTS">POLICY_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PRIMARY_CONTRACT">PRIMARY_CONTRACT</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROPOSAL_AGGREGATE_NAME">PROPOSAL_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROPOSAL_COLLECTION">PROPOSAL_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROPOSAL_DOMAIN_MODEL_TOKEN">PROPOSAL_DOMAIN_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROPOSAL_EVENTS">PROPOSAL_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#PROPOSAL_QUERY_MODEL_TOKEN">PROPOSAL_QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#QUERY_CONNECTION_TOKEN">QUERY_CONNECTION_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#QUERY_MODEL_TOKEN">QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REGEX_EMAIL">REGEX_EMAIL</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#REGEX_VN_PHONE">REGEX_VN_PHONE</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SCHEDULE_AGGREGATE_NAME">SCHEDULE_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SCHEDULE_COLLECTION">SCHEDULE_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SCHEDULE_EVENTS">SCHEDULE_EVENTS</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SMS_NHAC_NO_THANH_TOAN_DOT_1">SMS_NHAC_NO_THANH_TOAN_DOT_1</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO">SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#STATUSHISTORY_AGGREGATE_NAME">STATUSHISTORY_AGGREGATE_NAME</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#STATUSHISTORY_COLLECTION">STATUSHISTORY_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#STATUSHISTORY_QUERY_MODEL_TOKEN">STATUSHISTORY_QUERY_MODEL_TOKEN</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#TRANSFER_HISTORY_COLLECTION">TRANSFER_HISTORY_COLLECTION</a>
                            </li>
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#UNIT_AGGREGATE_NAME">UNIT_AGGREGATE_NAME</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Static</span>
                                <a href="#AGGREGATE_NAMES">AGGREGATE_NAMES</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>


            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            AGGREGATE_NAME</b>
                            <a href="#AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;primary-contract&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="59" class="link-to-prism">src/modules/shared/constant/common.const.ts:59</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="AGGREGATES"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            AGGREGATES</b>
                            <a href="#AGGREGATES"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    PROPOSAL: {
      NAME: CommonConst.PROPOSAL_AGGREGATE_NAME,
      CREATED: CommonConst.PROPOSAL_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.PROPOSAL_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.PROPOSAL_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.PROPOSAL_AGGREGATE_NAME,
      COLLECTION: CommonConst.PROPOSAL_AGGREGATE_NAME,
    },
    PRIMARY_CONTRACT: {
      NAME: CommonConst.AGGREGATE_NAME,
      CREATED: CommonConst.AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.AGGREGATE_NAME,
      COLLECTION: CommonConst.AGGREGATE_NAME,
      CODE_PREFIX_DEPOSIT_CONTRACT: &quot;HĐC-&quot;,// 
      CODE_PREFIX_TRANSFER_CONTRACT: &quot;HĐCN-&quot;,// 
      CODE_PREFIX_PURCHASE_CONTRACT: &quot;HDMB-&quot;,//
      CODE_PREFIX_PROPOSAL: &quot;ĐNTL-&quot;,//
      CODE_PREFIX_LIQUIDATION: &quot;BBTL-&quot;,//
      CODE_PREFIX_INTEREST: &quot;TL-&quot;,//
    },
    POLICY: {
      NAME: CommonConst.POLICY_AGGREGATE_NAME,
      CREATED: CommonConst.POLICY_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.POLICY_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.POLICY_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.POLICY_AGGREGATE_NAME,
      COLLECTION: CommonConst.POLICY_AGGREGATE_NAME,
      CODE_PREFIX_DISCOUNT: &quot;CSCK-&quot;,//Chính sách chiết khấu
      CODE_PREFIX_PAYMENT: &quot;CSTT-&quot;,//Chính sách thanh toán
    },
    DISCOUNT: {
      NAME: CommonConst.DISCOUNT_AGGREGATE_NAME,
      CREATED: CommonConst.DISCOUNT_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.DISCOUNT_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.DISCOUNT_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.DISCOUNT_AGGREGATE_NAME,
      COLLECTION: CommonConst.DISCOUNT_AGGREGATE_NAME,
      CODE_PREFIX: &quot;CK-&quot;,//Chiết khấu
    },
    SCHEDULE: {
      NAME: CommonConst.SCHEDULE_AGGREGATE_NAME,
      CREATED: CommonConst.SCHEDULE_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.SCHEDULE_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.SCHEDULE_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.SCHEDULE_AGGREGATE_NAME,
      COLLECTION: CommonConst.SCHEDULE_AGGREGATE_NAME,
      CODE_PREFIX_PURCHASE: &quot;TDTT-&quot;,//Tiến độ thanh toán
      CODE_PREFIX_LOAN: &quot;TDGV-&quot;,//Tiến độ góp vốn
    },
    LIQUIDATION: {
      NAME: CommonConst.LIQUIDATION_AGGREGATE_NAME,
      CREATED: CommonConst.LIQUIDATION_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.LIQUIDATION_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.LIQUIDATION_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.LIQUIDATION_AGGREGATE_NAME,
      COLLECTION: CommonConst.LIQUIDATION_AGGREGATE_NAME,
    },
    HANDOVER: {
      NAME: CommonConst.HANDOVER_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.HANDOVER_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.HANDOVER_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.HANDOVER_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_AGGREGATE_NAME,
    },
    HANDOVER_REQUEST: {
      NAME: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
    },
    HANDOVER_SCHEDULE: {
      NAME: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
    },
    STATUSHISTORY: {
      NAME: CommonConst.STATUSHISTORY_AGGREGATE_NAME,
      CREATED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.STATUSHISTORY_AGGREGATE_NAME,
      COLLECTION: CommonConst.STATUSHISTORY_AGGREGATE_NAME,
    },
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="92" class="link-to-prism">src/modules/shared/constant/common.const.ts:92</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="AGGREGATES_LISTENER"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            AGGREGATES_LISTENER</b>
                            <a href="#AGGREGATES_LISTENER"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    EMPLOYEE : {
        NAME: &#x27;employee&#x27;,
        CREATED: &#x27;employeeCreated&#x27;,
        UPDATED: &#x27;employeeUpdated&#x27;,
        LIST_UPDATED: &#x27;employeeListUpdated&#x27;,
        DELETED: &#x27;employeeDeleted&#x27;,
    }
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="79" class="link-to-prism">src/modules/shared/constant/common.const.ts:79</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="BIEN_BAN_THANH_LY"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            BIEN_BAN_THANH_LY</b>
                            <a href="#BIEN_BAN_THANH_LY"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Biên bản thanh lý&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="186" class="link-to-prism">src/modules/shared/constant/common.const.ts:186</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CODE_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CODE_COLLECTION</b>
                            <a href="#CODE_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;code-generates&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="27" class="link-to-prism">src/modules/shared/constant/common.const.ts:27</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CONTRACT_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CONTRACT_COLLECTION</b>
                            <a href="#CONTRACT_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;contracts&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/modules/shared/constant/common.const.ts:26</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CONTRACT_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CONTRACT_EVENTS</b>
                            <a href="#CONTRACT_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-contract&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="25" class="link-to-prism">src/modules/shared/constant/common.const.ts:25</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CONTRACT_IMPORT_HISTORY_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CONTRACT_IMPORT_HISTORY_COLLECTION</b>
                            <a href="#CONTRACT_IMPORT_HISTORY_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;contract-import-histories&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="7" class="link-to-prism">src/modules/shared/constant/common.const.ts:7</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CONTRACT_IMPORT_HISTORY_QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            CONTRACT_IMPORT_HISTORY_QUERY_MODEL_TOKEN</b>
                            <a href="#CONTRACT_IMPORT_HISTORY_QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;ContractImportHistory-Query-ModelToken&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="6" class="link-to-prism">src/modules/shared/constant/common.const.ts:6</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DATABASE_SERVICE_EVENTS_MODEL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DATABASE_SERVICE_EVENTS_MODEL</b>
                            <a href="#DATABASE_SERVICE_EVENTS_MODEL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-primary-contract-eventstore&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="10" class="link-to-prism">src/modules/shared/constant/common.const.ts:10</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DATABASE_SERVICE_READ_MODEL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DATABASE_SERVICE_READ_MODEL</b>
                            <a href="#DATABASE_SERVICE_READ_MODEL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;msx-primary-contract-readmodel&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="9" class="link-to-prism">src/modules/shared/constant/common.const.ts:9</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DISCOUNT_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DISCOUNT_AGGREGATE_NAME</b>
                            <a href="#DISCOUNT_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;discount&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="62" class="link-to-prism">src/modules/shared/constant/common.const.ts:62</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DISCOUNT_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DISCOUNT_COLLECTION</b>
                            <a href="#DISCOUNT_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;discounts&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="21" class="link-to-prism">src/modules/shared/constant/common.const.ts:21</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DISCOUNT_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DISCOUNT_EVENTS</b>
                            <a href="#DISCOUNT_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-discount&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/modules/shared/constant/common.const.ts:20</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DOMAIN_CONNECTION_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DOMAIN_CONNECTION_TOKEN</b>
                            <a href="#DOMAIN_CONNECTION_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;PrimaryContract-Domain-DbConnectionToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="15" class="link-to-prism">src/modules/shared/constant/common.const.ts:15</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DOMAIN_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            DOMAIN_MODEL_TOKEN</b>
                            <a href="#DOMAIN_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Domain-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="13" class="link-to-prism">src/modules/shared/constant/common.const.ts:13</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMAIL_THANH_TOAN_DOT_1"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMAIL_THANH_TOAN_DOT_1</b>
                            <a href="#EMAIL_THANH_TOAN_DOT_1"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;EMAIL_THANH_TOAN_DOT_1&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="64" class="link-to-prism">src/modules/shared/constant/common.const.ts:64</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMAIL_THANH_TOAN_DOT_TIEP_THEO"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMAIL_THANH_TOAN_DOT_TIEP_THEO</b>
                            <a href="#EMAIL_THANH_TOAN_DOT_TIEP_THEO"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;EMAIL_THANH_TOAN_DOT_TIEP_THEO&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="65" class="link-to-prism">src/modules/shared/constant/common.const.ts:65</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EMPLOYEE_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            EMPLOYEE_COLLECTION</b>
                            <a href="#EMPLOYEE_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;employees&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/modules/shared/constant/common.const.ts:28</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_AGGREGATE_NAME</b>
                            <a href="#HANDOVER_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;handover&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/modules/shared/constant/common.const.ts:39</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_COLLECTION</b>
                            <a href="#HANDOVER_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;handovers&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="40" class="link-to-prism">src/modules/shared/constant/common.const.ts:40</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_DOMAIN_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_DOMAIN_MODEL_TOKEN</b>
                            <a href="#HANDOVER_DOMAIN_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Handover-Domain-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/modules/shared/constant/common.const.ts:36</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_EVENTS</b>
                            <a href="#HANDOVER_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-handover&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/modules/shared/constant/common.const.ts:38</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_QUERY_MODEL_TOKEN</b>
                            <a href="#HANDOVER_QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Handover-Query-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="37" class="link-to-prism">src/modules/shared/constant/common.const.ts:37</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_REQUEST_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_REQUEST_AGGREGATE_NAME</b>
                            <a href="#HANDOVER_REQUEST_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;handover-request&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="45" class="link-to-prism">src/modules/shared/constant/common.const.ts:45</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_REQUEST_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_REQUEST_COLLECTION</b>
                            <a href="#HANDOVER_REQUEST_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;handovers-request&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="46" class="link-to-prism">src/modules/shared/constant/common.const.ts:46</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_REQUEST_DOMAIN_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_REQUEST_DOMAIN_MODEL_TOKEN</b>
                            <a href="#HANDOVER_REQUEST_DOMAIN_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Handover-Domain-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="42" class="link-to-prism">src/modules/shared/constant/common.const.ts:42</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_REQUEST_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_REQUEST_EVENTS</b>
                            <a href="#HANDOVER_REQUEST_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-handover-request&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="44" class="link-to-prism">src/modules/shared/constant/common.const.ts:44</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_REQUEST_QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_REQUEST_QUERY_MODEL_TOKEN</b>
                            <a href="#HANDOVER_REQUEST_QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Handover-Query-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="43" class="link-to-prism">src/modules/shared/constant/common.const.ts:43</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_SCHEDULE_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_SCHEDULE_AGGREGATE_NAME</b>
                            <a href="#HANDOVER_SCHEDULE_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;handover-schedule&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="51" class="link-to-prism">src/modules/shared/constant/common.const.ts:51</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_SCHEDULE_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_SCHEDULE_COLLECTION</b>
                            <a href="#HANDOVER_SCHEDULE_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;handovers-schedule&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="52" class="link-to-prism">src/modules/shared/constant/common.const.ts:52</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_SCHEDULE_DOMAIN_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_SCHEDULE_DOMAIN_MODEL_TOKEN</b>
                            <a href="#HANDOVER_SCHEDULE_DOMAIN_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Handover-Domain-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="48" class="link-to-prism">src/modules/shared/constant/common.const.ts:48</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_SCHEDULE_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_SCHEDULE_EVENTS</b>
                            <a href="#HANDOVER_SCHEDULE_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-handover-schedule&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="50" class="link-to-prism">src/modules/shared/constant/common.const.ts:50</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HANDOVER_SCHEDULE_QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HANDOVER_SCHEDULE_QUERY_MODEL_TOKEN</b>
                            <a href="#HANDOVER_SCHEDULE_QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Handover-Query-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="49" class="link-to-prism">src/modules/shared/constant/common.const.ts:49</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HEADER_PARTERN_STR"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HEADER_PARTERN_STR</b>
                            <a href="#HEADER_PARTERN_STR"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;pattern&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="11" class="link-to-prism">src/modules/shared/constant/common.const.ts:11</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="HINH_THUC"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            HINH_THUC</b>
                            <a href="#HINH_THUC"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    SO_HUU : {
      SU_DUNG_VINH_VIEN: &#x27;Sử dụng vĩnh viễn&#x27;,
      SU_DUNG_50_NAN: &#x27;Sử dụng 50 năm&#x27;,
    }
  }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="68" class="link-to-prism">src/modules/shared/constant/common.const.ts:68</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LIQUIDATION_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LIQUIDATION_AGGREGATE_NAME</b>
                            <a href="#LIQUIDATION_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;liquidation&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="77" class="link-to-prism">src/modules/shared/constant/common.const.ts:77</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LIQUIDATION_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LIQUIDATION_COLLECTION</b>
                            <a href="#LIQUIDATION_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;liquidations&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="78" class="link-to-prism">src/modules/shared/constant/common.const.ts:78</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LIQUIDATION_DOMAIN_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LIQUIDATION_DOMAIN_MODEL_TOKEN</b>
                            <a href="#LIQUIDATION_DOMAIN_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Liquidation-Domain-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="74" class="link-to-prism">src/modules/shared/constant/common.const.ts:74</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LIQUIDATION_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LIQUIDATION_EVENTS</b>
                            <a href="#LIQUIDATION_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-liquidation&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="76" class="link-to-prism">src/modules/shared/constant/common.const.ts:76</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LIQUIDATION_QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            LIQUIDATION_QUERY_MODEL_TOKEN</b>
                            <a href="#LIQUIDATION_QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Liquidation-Query-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="75" class="link-to-prism">src/modules/shared/constant/common.const.ts:75</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="POLICY_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            POLICY_AGGREGATE_NAME</b>
                            <a href="#POLICY_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;policy&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="60" class="link-to-prism">src/modules/shared/constant/common.const.ts:60</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="POLICY_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            POLICY_COLLECTION</b>
                            <a href="#POLICY_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;policies&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/modules/shared/constant/common.const.ts:19</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="POLICY_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            POLICY_EVENTS</b>
                            <a href="#POLICY_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-policy&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/modules/shared/constant/common.const.ts:18</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PRIMARY_CONTRACT"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PRIMARY_CONTRACT</b>
                            <a href="#PRIMARY_CONTRACT"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;primaryContract_&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="185" class="link-to-prism">src/modules/shared/constant/common.const.ts:185</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROPOSAL_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROPOSAL_AGGREGATE_NAME</b>
                            <a href="#PROPOSAL_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;proposal&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/modules/shared/constant/common.const.ts:33</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROPOSAL_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROPOSAL_COLLECTION</b>
                            <a href="#PROPOSAL_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;proposals&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/modules/shared/constant/common.const.ts:34</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROPOSAL_DOMAIN_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROPOSAL_DOMAIN_MODEL_TOKEN</b>
                            <a href="#PROPOSAL_DOMAIN_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Proposal-Domain-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="30" class="link-to-prism">src/modules/shared/constant/common.const.ts:30</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROPOSAL_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROPOSAL_EVENTS</b>
                            <a href="#PROPOSAL_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-proposal&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="32" class="link-to-prism">src/modules/shared/constant/common.const.ts:32</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROPOSAL_QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            PROPOSAL_QUERY_MODEL_TOKEN</b>
                            <a href="#PROPOSAL_QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Proposal-Query-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/modules/shared/constant/common.const.ts:31</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QUERY_CONNECTION_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            QUERY_CONNECTION_TOKEN</b>
                            <a href="#QUERY_CONNECTION_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;PrimaryContract-Query-DbConnectionToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="16" class="link-to-prism">src/modules/shared/constant/common.const.ts:16</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            QUERY_MODEL_TOKEN</b>
                            <a href="#QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Query-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="14" class="link-to-prism">src/modules/shared/constant/common.const.ts:14</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REGEX_EMAIL"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REGEX_EMAIL</b>
                            <a href="#REGEX_EMAIL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>/^[0-9-A-z][A-z0-9_\.-]{1,32}@[A-z0-9-_]{2,}(\.[A-z0-9]{2,}){1,2}$/</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="3" class="link-to-prism">src/modules/shared/constant/common.const.ts:3</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="REGEX_VN_PHONE"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            REGEX_VN_PHONE</b>
                            <a href="#REGEX_VN_PHONE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>/[0-9\+]{9,15}/gm</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="4" class="link-to-prism">src/modules/shared/constant/common.const.ts:4</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SCHEDULE_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SCHEDULE_AGGREGATE_NAME</b>
                            <a href="#SCHEDULE_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;schedule&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="63" class="link-to-prism">src/modules/shared/constant/common.const.ts:63</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SCHEDULE_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SCHEDULE_COLLECTION</b>
                            <a href="#SCHEDULE_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;schedules&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/modules/shared/constant/common.const.ts:23</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SCHEDULE_EVENTS"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SCHEDULE_EVENTS</b>
                            <a href="#SCHEDULE_EVENTS"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;events-schedule&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/modules/shared/constant/common.const.ts:22</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SMS_NHAC_NO_THANH_TOAN_DOT_1"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SMS_NHAC_NO_THANH_TOAN_DOT_1</b>
                            <a href="#SMS_NHAC_NO_THANH_TOAN_DOT_1"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;SMS_NHAC_NO_THANH_TOAN_DOT_1&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="66" class="link-to-prism">src/modules/shared/constant/common.const.ts:66</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO</b>
                            <a href="#SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/modules/shared/constant/common.const.ts:67</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="STATUSHISTORY_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            STATUSHISTORY_AGGREGATE_NAME</b>
                            <a href="#STATUSHISTORY_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;statusHistory&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="55" class="link-to-prism">src/modules/shared/constant/common.const.ts:55</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="STATUSHISTORY_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            STATUSHISTORY_COLLECTION</b>
                            <a href="#STATUSHISTORY_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;statusHistorys&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="56" class="link-to-prism">src/modules/shared/constant/common.const.ts:56</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="STATUSHISTORY_QUERY_MODEL_TOKEN"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            STATUSHISTORY_QUERY_MODEL_TOKEN</b>
                            <a href="#STATUSHISTORY_QUERY_MODEL_TOKEN"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;Statushistory-Query-ModelToken&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="54" class="link-to-prism">src/modules/shared/constant/common.const.ts:54</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TRANSFER_HISTORY_COLLECTION"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            TRANSFER_HISTORY_COLLECTION</b>
                            <a href="#TRANSFER_HISTORY_COLLECTION"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;transfer-histories&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/modules/shared/constant/common.const.ts:24</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="UNIT_AGGREGATE_NAME"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Static</span>
                            UNIT_AGGREGATE_NAME</b>
                            <a href="#UNIT_AGGREGATE_NAME"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&quot;unit&quot;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="61" class="link-to-prism">src/modules/shared/constant/common.const.ts:61</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="AGGREGATE_NAMES"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Static</span>
                            AGGREGATE_NAMES
                        </b>
                        <a href="#AGGREGATE_NAMES"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>AGGREGATE_NAMES()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="88"
                            class="link-to-prism">src/modules/shared/constant/common.const.ts:88</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Object[]</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>





    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">export class CommonConst {

  static REGEX_EMAIL &#x3D; /^[0-9-A-z][A-z0-9_\.-]{1,32}@[A-z0-9-_]{2,}(\.[A-z0-9]{2,}){1,2}$/;
  static REGEX_VN_PHONE &#x3D; /[0-9\+]{9,15}/gm;

  static CONTRACT_IMPORT_HISTORY_QUERY_MODEL_TOKEN &#x3D; &#x27;ContractImportHistory-Query-ModelToken&#x27;;
  static CONTRACT_IMPORT_HISTORY_COLLECTION &#x3D; &#x27;contract-import-histories&#x27;;

  static DATABASE_SERVICE_READ_MODEL &#x3D; &quot;msx-primary-contract-readmodel&quot;;
  static DATABASE_SERVICE_EVENTS_MODEL &#x3D; &quot;msx-primary-contract-eventstore&quot;;
  static HEADER_PARTERN_STR &#x3D; &quot;pattern&quot;;

  static DOMAIN_MODEL_TOKEN &#x3D; &quot;Domain-ModelToken&quot;;
  static QUERY_MODEL_TOKEN &#x3D; &quot;Query-ModelToken&quot;;
  static DOMAIN_CONNECTION_TOKEN &#x3D; &quot;PrimaryContract-Domain-DbConnectionToken&quot;;
  static QUERY_CONNECTION_TOKEN &#x3D; &quot;PrimaryContract-Query-DbConnectionToken&quot;;

  static POLICY_EVENTS &#x3D; &quot;events-policy&quot;;
  static POLICY_COLLECTION &#x3D; &quot;policies&quot;;
  static DISCOUNT_EVENTS &#x3D; &quot;events-discount&quot;;
  static DISCOUNT_COLLECTION &#x3D; &quot;discounts&quot;;
  static SCHEDULE_EVENTS &#x3D; &quot;events-schedule&quot;;
  static SCHEDULE_COLLECTION &#x3D; &quot;schedules&quot;;
  static TRANSFER_HISTORY_COLLECTION &#x3D; &quot;transfer-histories&quot;;
  static CONTRACT_EVENTS &#x3D; &quot;events-contract&quot;;
  static CONTRACT_COLLECTION &#x3D; &quot;contracts&quot;;
  static CODE_COLLECTION &#x3D; &quot;code-generates&quot;;
  static EMPLOYEE_COLLECTION &#x3D; &quot;employees&quot;;

  static PROPOSAL_DOMAIN_MODEL_TOKEN &#x3D; &quot;Proposal-Domain-ModelToken&quot;;
  static PROPOSAL_QUERY_MODEL_TOKEN &#x3D; &quot;Proposal-Query-ModelToken&quot;;
  static PROPOSAL_EVENTS &#x3D; &quot;events-proposal&quot;;
  static PROPOSAL_AGGREGATE_NAME &#x3D; &quot;proposal&quot;;
  static PROPOSAL_COLLECTION &#x3D; &quot;proposals&quot;;

  static HANDOVER_DOMAIN_MODEL_TOKEN &#x3D; &quot;Handover-Domain-ModelToken&quot;;
  static HANDOVER_QUERY_MODEL_TOKEN &#x3D; &quot;Handover-Query-ModelToken&quot;;
  static HANDOVER_EVENTS &#x3D; &quot;events-handover&quot;;
  static HANDOVER_AGGREGATE_NAME &#x3D; &quot;handover&quot;;
  static HANDOVER_COLLECTION &#x3D; &quot;handovers&quot;;

  static HANDOVER_REQUEST_DOMAIN_MODEL_TOKEN &#x3D; &quot;Handover-Domain-ModelToken&quot;;
  static HANDOVER_REQUEST_QUERY_MODEL_TOKEN &#x3D; &quot;Handover-Query-ModelToken&quot;;
  static HANDOVER_REQUEST_EVENTS &#x3D; &quot;events-handover-request&quot;;
  static HANDOVER_REQUEST_AGGREGATE_NAME &#x3D; &quot;handover-request&quot;;
  static HANDOVER_REQUEST_COLLECTION &#x3D; &quot;handovers-request&quot;;

  static HANDOVER_SCHEDULE_DOMAIN_MODEL_TOKEN &#x3D; &quot;Handover-Domain-ModelToken&quot;;
  static HANDOVER_SCHEDULE_QUERY_MODEL_TOKEN &#x3D; &quot;Handover-Query-ModelToken&quot;;
  static HANDOVER_SCHEDULE_EVENTS &#x3D; &quot;events-handover-schedule&quot;;
  static HANDOVER_SCHEDULE_AGGREGATE_NAME &#x3D; &quot;handover-schedule&quot;;
  static HANDOVER_SCHEDULE_COLLECTION &#x3D; &quot;handovers-schedule&quot;;

  static STATUSHISTORY_QUERY_MODEL_TOKEN &#x3D; &quot;Statushistory-Query-ModelToken&quot;;
  static STATUSHISTORY_AGGREGATE_NAME &#x3D; &quot;statusHistory&quot;;
  static STATUSHISTORY_COLLECTION &#x3D; &quot;statusHistorys&quot;;

  // aggregate
  static AGGREGATE_NAME &#x3D; &quot;primary-contract&quot;;
  static POLICY_AGGREGATE_NAME &#x3D; &quot;policy&quot;;
  static UNIT_AGGREGATE_NAME &#x3D; &quot;unit&quot;;
  static DISCOUNT_AGGREGATE_NAME &#x3D; &quot;discount&quot;;
  static SCHEDULE_AGGREGATE_NAME &#x3D; &quot;schedule&quot;;
  static EMAIL_THANH_TOAN_DOT_1 &#x3D; &#x27;EMAIL_THANH_TOAN_DOT_1&#x27;;
  static EMAIL_THANH_TOAN_DOT_TIEP_THEO &#x3D; &#x27;EMAIL_THANH_TOAN_DOT_TIEP_THEO&#x27;;
  static SMS_NHAC_NO_THANH_TOAN_DOT_1 &#x3D; &#x27;SMS_NHAC_NO_THANH_TOAN_DOT_1&#x27;;
  static SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO &#x3D; &#x27;SMS_NHAC_NO_THANH_TOAN_DOT_TIEP_THEO&#x27;;
  static HINH_THUC &#x3D; {
    SO_HUU : {
      SU_DUNG_VINH_VIEN: &#x27;Sử dụng vĩnh viễn&#x27;,
      SU_DUNG_50_NAN: &#x27;Sử dụng 50 năm&#x27;,
    }
  }
  static LIQUIDATION_DOMAIN_MODEL_TOKEN &#x3D; &quot;Liquidation-Domain-ModelToken&quot;;
  static LIQUIDATION_QUERY_MODEL_TOKEN &#x3D; &quot;Liquidation-Query-ModelToken&quot;;
  static LIQUIDATION_EVENTS &#x3D; &quot;events-liquidation&quot;;
  static LIQUIDATION_AGGREGATE_NAME &#x3D; &quot;liquidation&quot;;
  static LIQUIDATION_COLLECTION &#x3D; &quot;liquidations&quot;;
  static AGGREGATES_LISTENER &#x3D; {
    EMPLOYEE : {
        NAME: &#x27;employee&#x27;,
        CREATED: &#x27;employeeCreated&#x27;,
        UPDATED: &#x27;employeeUpdated&#x27;,
        LIST_UPDATED: &#x27;employeeListUpdated&#x27;,
        DELETED: &#x27;employeeDeleted&#x27;,
    }
  }
  static AGGREGATE_NAMES(): Object[] {
    return Object.keys(this.AGGREGATES).map((key) &#x3D;&gt; this.AGGREGATES[key].NAME);
  }

  static AGGREGATES &#x3D; {
    PROPOSAL: {
      NAME: CommonConst.PROPOSAL_AGGREGATE_NAME,
      CREATED: CommonConst.PROPOSAL_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.PROPOSAL_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.PROPOSAL_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.PROPOSAL_AGGREGATE_NAME,
      COLLECTION: CommonConst.PROPOSAL_AGGREGATE_NAME,
    },
    PRIMARY_CONTRACT: {
      NAME: CommonConst.AGGREGATE_NAME,
      CREATED: CommonConst.AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.AGGREGATE_NAME,
      COLLECTION: CommonConst.AGGREGATE_NAME,
      CODE_PREFIX_DEPOSIT_CONTRACT: &quot;HĐC-&quot;,// 
      CODE_PREFIX_TRANSFER_CONTRACT: &quot;HĐCN-&quot;,// 
      CODE_PREFIX_PURCHASE_CONTRACT: &quot;HDMB-&quot;,//
      CODE_PREFIX_PROPOSAL: &quot;ĐNTL-&quot;,//
      CODE_PREFIX_LIQUIDATION: &quot;BBTL-&quot;,//
      CODE_PREFIX_INTEREST: &quot;TL-&quot;,//
    },
    POLICY: {
      NAME: CommonConst.POLICY_AGGREGATE_NAME,
      CREATED: CommonConst.POLICY_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.POLICY_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.POLICY_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.POLICY_AGGREGATE_NAME,
      COLLECTION: CommonConst.POLICY_AGGREGATE_NAME,
      CODE_PREFIX_DISCOUNT: &quot;CSCK-&quot;,//Chính sách chiết khấu
      CODE_PREFIX_PAYMENT: &quot;CSTT-&quot;,//Chính sách thanh toán
    },
    DISCOUNT: {
      NAME: CommonConst.DISCOUNT_AGGREGATE_NAME,
      CREATED: CommonConst.DISCOUNT_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.DISCOUNT_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.DISCOUNT_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.DISCOUNT_AGGREGATE_NAME,
      COLLECTION: CommonConst.DISCOUNT_AGGREGATE_NAME,
      CODE_PREFIX: &quot;CK-&quot;,//Chiết khấu
    },
    SCHEDULE: {
      NAME: CommonConst.SCHEDULE_AGGREGATE_NAME,
      CREATED: CommonConst.SCHEDULE_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.SCHEDULE_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.SCHEDULE_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.SCHEDULE_AGGREGATE_NAME,
      COLLECTION: CommonConst.SCHEDULE_AGGREGATE_NAME,
      CODE_PREFIX_PURCHASE: &quot;TDTT-&quot;,//Tiến độ thanh toán
      CODE_PREFIX_LOAN: &quot;TDGV-&quot;,//Tiến độ góp vốn
    },
    LIQUIDATION: {
      NAME: CommonConst.LIQUIDATION_AGGREGATE_NAME,
      CREATED: CommonConst.LIQUIDATION_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.LIQUIDATION_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.LIQUIDATION_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.LIQUIDATION_AGGREGATE_NAME,
      COLLECTION: CommonConst.LIQUIDATION_AGGREGATE_NAME,
    },
    HANDOVER: {
      NAME: CommonConst.HANDOVER_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.HANDOVER_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.HANDOVER_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.HANDOVER_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_AGGREGATE_NAME,
    },
    HANDOVER_REQUEST: {
      NAME: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_REQUEST_AGGREGATE_NAME,
    },
    HANDOVER_SCHEDULE: {
      NAME: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
      CREATED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
      COLLECTION: CommonConst.HANDOVER_SCHEDULE_AGGREGATE_NAME,
    },
    STATUSHISTORY: {
      NAME: CommonConst.STATUSHISTORY_AGGREGATE_NAME,
      CREATED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + &quot;Created&quot;,
      UPDATED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + &quot;Updated&quot;,
      DELETED: CommonConst.STATUSHISTORY_AGGREGATE_NAME + &quot;Deleted&quot;,
      EVENTS: &quot;events-&quot; + CommonConst.STATUSHISTORY_AGGREGATE_NAME,
      COLLECTION: CommonConst.STATUSHISTORY_AGGREGATE_NAME,
    },
  };
  static PRIMARY_CONTRACT &#x3D; &quot;primaryContract_&quot;;
  static BIEN_BAN_THANH_LY &#x3D; &quot;Biên bản thanh lý&quot;;
}
</code></pre>
    </div>
</div>



                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'class';
            var COMPODOC_CURRENT_PAGE_URL = 'CommonConst.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
