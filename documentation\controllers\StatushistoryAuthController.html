<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-primaryContract documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-primaryContract documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>StatushistoryAuthController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/statusHistory/web/auth-controller.ts</code>
        </p>

            <p class="comment">
                <h3>Prefix</h3>
            </p>
            <p class="comment">
                <code>v1/auth/statusHistory</code>
            </p>






    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
} from &quot;@nestjs/common&quot;;
import {
  ApiUseTags,
  ApiBearerAuth,
  ApiImplicitQuery,
  ApiOperation,
  ApiImplicitParam,
} from &quot;@nestjs/swagger&quot;;
import { AuthGuard } from &quot;@nestjs/passport&quot;;
import { StatushistoryService } from &quot;../application/service&quot;;
import { LoggingInterceptor } from &quot;../../../common/interceptors/logging.interceptor&quot;;
import { Usr } from &quot;../../shared/services/user/decorator/user.decorator&quot;;
import { ValidationPipe } from &quot;../../../common/pipes/validation.pipe&quot;;

@ApiBearerAuth()
@ApiUseTags(&quot;[Auth] Statushistory - API của TVV&quot;)
@Controller(&quot;v1/auth/statusHistory&quot;)
@UseGuards(AuthGuard(&quot;jwt&quot;))
@UseInterceptors(LoggingInterceptor)
export class StatushistoryAuthController {
  constructor(private readonly service: StatushistoryService) {}

  // @ApiOperation({ title: &quot;Find all&quot; })
  // @ApiImplicitQuery({
  //   name: &quot;page&quot;,
  //   required: true,
  //   description: &quot;[Paging] page&quot;,
  // })
  // @ApiImplicitQuery({
  //   name: &quot;pageSize&quot;,
  //   required: true,
  //   description: &quot;[Paging] pageSize&quot;,
  // })
  // @ApiImplicitQuery({
  //   name: &quot;sort&quot;,
  //   required: false,
  //   isArray: true,
  //   description: &quot;[Sort] sort&quot;,
  // })
  // @ApiImplicitQuery({
  //   name: &quot;q&quot;,
  //   required: false,
  //   description: &quot;[Filter] Search text&quot;,
  // })
  // @Get()
  // findAll(@Usr() user, @Query() query?: any) {
  //   return this.service.findAll(query);
  // }
  //
  // @ApiOperation({ title: &quot;Find one&quot; })
  // @ApiImplicitParam({ name: &quot;id&quot;, required: true, description: &quot;Id&quot; })
  // @Get(&quot;:id&quot;)
  // findById(@Param(&quot;id&quot;) id: string) {
  //   return this.service.findById(id);
  // }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'StatushistoryAuthController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
